#!/usr/bin/env python3
"""
Targeted test to find which specific import is hanging
"""

import sys
import os

# Add paths
sys.path.append('src')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

def test_config_imports():
    try:
        print("Testing Environment enum...")
        from kbotloadscheduler.loader.confluence.config import Environment
        print("✅ Environment imported")
        
        print("Testing SearchCriteria...")
        from kbotloadscheduler.loader.confluence.config import SearchCriteria
        print("✅ SearchCriteria imported")
        
        print("Testing ThreadPoolConfig...")
        from kbotloadscheduler.loader.confluence.config import ThreadPoolConfig
        print("✅ ThreadPoolConfig imported")
        
        print("Testing RetryConfig...")
        from kbotloadscheduler.loader.confluence.config import RetryConfig
        print("✅ RetryConfig imported")
        
        print("Testing LoggingConfig...")
        from kbotloadscheduler.loader.confluence.config import LoggingConfig
        print("✅ LoggingConfig imported")
        
        print("Testing HealthCheckConfig...")
        from kbotloadscheduler.loader.confluence.config import HealthCheckConfig
        print("✅ HealthCheckConfig imported")
        
        print("Testing ConfluenceConfig...")
        from kbotloadscheduler.loader.confluence.config import ConfluenceConfig
        print("✅ ConfluenceConfig imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_imports()
    sys.exit(0 if success else 1)
