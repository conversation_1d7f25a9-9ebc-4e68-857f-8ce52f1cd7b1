#!/usr/bin/env python3

import sys
import os

# Add the src and tests directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
tests_path = os.path.join(current_dir, 'tests')

sys.path.insert(0, src_path)
sys.path.insert(0, tests_path)

def test_individual_imports():
    """Test each import individually to find the problematic one"""
    
    imports_to_test = [
        ("unittest.mock", "from unittest.mock import MagicMock, patch"),
        ("beans", "from kbotloadscheduler.bean.beans import DocumentBean, SourceBean"),
        ("confluence loader", "from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader"),
        ("mock_confluence", "from testutils.mock_confluence import MockConfluence"),
        ("create_mock_confluence_client", "from testutils.mock_confluence import create_mock_confluence_client"),
        ("setup_sample_confluence_data", "from testutils.mock_confluence import setup_sample_confluence_data"),
    ]
    
    for name, import_statement in imports_to_test:
        try:
            print(f"Testing import: {name}")
            exec(import_statement)
            print(f"✅ {name} import successful")
        except Exception as e:
            print(f"❌ {name} import failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

if __name__ == "__main__":
    print("Testing individual imports...")
    print("=" * 50)
    
    success = test_individual_imports()
    
    if success:
        print("🎉 All individual imports passed!")
    else:
        print("⚠️  Some imports failed")
