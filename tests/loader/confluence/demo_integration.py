#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de démonstration pour les tests d'intégration Confluence.
Ce script guide l'utilisateur à travers la configuration et l'exécution des tests.
"""

import os
import subprocess
import sys
from pathlib import Path

# Ajouter le chemin du projet au PYTHONPATH
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Import conditionnel pour éviter les erreurs si dotenv n'est pas installé
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


def print_banner():
    """Affiche la bannière de bienvenue."""
    print("=" * 60)
    print("🚀 DÉMONSTRATION DES TESTS D'INTÉGRATION CONFLUENCE")
    print("=" * 60)
    print()


def check_prerequisites():
    """Vérifie les prérequis pour les tests d'intégration."""
    print("📋 Vérification des prérequis...")

    # Vérifier Python
    python_version = sys.version_info
    if python_version.major < 3 or (
        python_version.major == 3 and python_version.minor < 8
    ):
        print("❌ Python 3.8+ requis")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}")

    # Vérifier pytest
    try:
        import pytest
        print(f"✅ pytest {pytest.__version__}")
    except ImportError:
        print("❌ pytest non installé")
        return False

    # Vérifier dotenv
    if DOTENV_AVAILABLE:
        print("✅ python-dotenv disponible")
    else:
        print("❌ python-dotenv non installé")
        return False

    return True


def setup_configuration():
    """Guide l'utilisateur pour configurer les tests."""
    print("\n🔧 Configuration des tests d'intégration...")

    env_file = Path(__file__).parent / ".env"
    env_example = Path(__file__).parent / ".env.example"

    if env_file.exists():
        print(f"✅ Fichier .env trouvé: {env_file}")

        if DOTENV_AVAILABLE:
            load_dotenv(env_file)

            # Vérifier les variables
            url = os.getenv("CONFLUENCE_URL")
            token = os.getenv("CONFLUENCE_PAT_TOKEN")

            if url and token:
                print(f"✅ Configuration détectée pour: {url}")
                return True
            else:
                print("⚠️  Configuration incomplète dans .env")

    print(f"\n📝 Créez un fichier .env basé sur {env_example}")
    print("Variables requises:")
    print("  - CONFLUENCE_URL: URL de votre instance Confluence")
    print("  - CONFLUENCE_PAT_TOKEN: Personal Access Token")
    print("  - CONFLUENCE_TEST_SPACE: Espace de test (optionnel)")

    return False


def run_demo_sequence():
    """Exécute une séquence de démonstration des tests."""
    print("\n🎬 Séquence de démonstration...")

    steps = [
        ("Vérification de la configuration", "--check-config"),
        ("Test de connexion (Health Check)", "--health-check"),
        ("Tests d'intégration rapides", "--run-fast"),
        ("Tests de performance rapides", "--run-performance"),
    ]

    script_path = Path(__file__).parent / "run_integration_tests.py"

    for step_name, step_arg in steps:
        print(f"\n🔍 {step_name}...")
        print(f"Commande: python {script_path.name} {step_arg}")

        response = input("Exécuter cette étape ? (o/N): ").lower().strip()
        if response in ["o", "oui", "y", "yes"]:
            try:
                result = subprocess.run(
                    ["python", str(script_path), step_arg],
                    cwd=script_path.parent,
                    check=True,
                    capture_output=True,
                    text=True,
                )

                print("✅ Succès!")
                if result.stdout:
                    print("Sortie:")
                    print(result.stdout)

            except subprocess.CalledProcessError as e:
                print(f"❌ Échec: {e}")
                if e.stdout:
                    print("Sortie:")
                    print(e.stdout)
                if e.stderr:
                    print("Erreur:")
                    print(e.stderr)

                continue_demo = (
                    input("Continuer la démonstration ? (o/N): ").lower().strip()
                )
                if continue_demo not in ["o", "oui", "y", "yes"]:
                    break
        else:
            print("⏭️  Étape ignorée")


def show_advanced_usage():
    """Montre les utilisations avancées."""
    print("\n🎓 Utilisations avancées...")

    examples = [
        (
            "Test spécifique",
            "python run_integration_tests.py --run-specific test_confluence_connection",
        ),
        ("Tous les tests d'intégration", "python run_integration_tests.py --run-all"),
        (
            "Tous les tests de performance",
            "python run_integration_tests.py --run-all-performance",
        ),
        (
            "Avec pytest directement",
            "pytest test_real_confluence_integration.py -v -m integration",
        ),
        (
            "Tests de performance uniquement",
            "pytest test_performance_integration.py -v -m performance",
        ),
    ]

    for description, command in examples:
        print(f"\n📌 {description}:")
        print(f"   {command}")


def show_troubleshooting():
    """Affiche les conseils de dépannage."""
    print("\n🔧 Dépannage...")

    issues = [
        (
            "Configuration manquante",
            "Créez un fichier .env avec CONFLUENCE_URL et CONFLUENCE_PAT_TOKEN",
        ),
        (
            "Erreur 401 Unauthorized",
            "Vérifiez que votre PAT token est valide et non expiré",
        ),
        (
            "Aucun document trouvé",
            "Vérifiez que l'espace de test existe et contient des pages",
        ),
        ("Tests lents", "Utilisez --run-fast pour éviter les tests marqués 'slow'"),
        (
            "Erreurs de dépendances",
            "Installez les dépendances: pip install -r tests/test-requirements.txt",
        ),
    ]

    for problem, solution in issues:
        print(f"\n❓ {problem}")
        print(f"   💡 {solution}")


def main():
    """Fonction principale de démonstration."""
    print_banner()

    # Vérifier les prérequis
    if not check_prerequisites():
        print("\n❌ Prérequis manquants. Installez les dépendances requises.")
        return

    # Configuration
    config_ok = setup_configuration()

    if config_ok:
        print("\n🎯 Que souhaitez-vous faire ?")
        print("1. Exécuter la démonstration guidée")
        print("2. Voir les utilisations avancées")
        print("3. Voir le dépannage")
        print("4. Quitter")

        choice = input("\nVotre choix (1-4): ").strip()

        if choice == "1":
            run_demo_sequence()
        elif choice == "2":
            show_advanced_usage()
        elif choice == "3":
            show_troubleshooting()
        elif choice == "4":
            print("👋 Au revoir !")
        else:
            print("❓ Choix invalide")
    else:
        print("\n💡 Configurez d'abord votre environnement, puis relancez ce script.")

    print("\n📚 Pour plus d'informations, consultez README_INTEGRATION.md")


if __name__ == "__main__":
    main()
