#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Modèles de données pour le système RAG Confluence.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field, field_validator


class UserInfo(BaseModel):
    """Informations sur un utilisateur Confluence."""

    id: str
    username: str
    display_name: str
    email: Optional[str] = None
    picture_url: Optional[str] = None


class LabelInfo(BaseModel):
    """Informations sur un label Confluence."""

    id: str
    name: str
    prefix: Optional[str] = None


class SpaceInfo(BaseModel):
    """Informations sur un espace Confluence."""

    id: str
    key: str
    name: str
    type: str
    description: Optional[str] = None
    homepage_id: Optional[str] = None


class AttachmentDetail(BaseModel):
    """
    Détails d'une pièce jointe Confluence.
    Les champs created et last_updated sont fournis par l'API Confluence :
    - created : date de création initiale
    - last_updated : date de dernière modification (mise à jour du fichier)
    """

    id: str
    title: str
    file_name: str
    file_size: int
    media_type: str
    created: datetime
    last_updated: datetime  # Nouveau champ
    creator: UserInfo
    download_url: str
    content_id: str
    content_type: Optional[str] = None
    extracted_text: Optional[str] = None
    processing_status: str = "pending"
    processing_error: Optional[str] = None
    processing_metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)

    @field_validator("media_type")
    @classmethod
    def validate_media_type(cls, v):
        """Valide que le type de média est supporté."""
        supported_types = [
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/html",
            "text/markdown",
        ]
        if v not in supported_types and not v.startswith("image/"):
            return v + " (non supporté)"
        return v

    @classmethod
    def from_json(
        cls, data: Dict[str, Any], content_id: str = None
    ) -> "AttachmentDetail":
        """Crée une instance AttachmentDetail à partir de données JSON."""
        adapted_data = {
            "id": data.get("id"),
            "title": data.get("title"),
            "file_name": data.get("title"),
            "file_size": data.get("extensions", {}).get("fileSize", 0),
            "media_type": data.get("extensions", {}).get(
                "mediaType", "application/octet-stream"
            ),
            "created": data.get("version", {}).get("createdDate", datetime.now()),
            "last_updated": data.get("version", {}).get("when", datetime.now()),
            "creator": UserInfo(
                id=data.get("version", {}).get("by", {}).get("accountId", "unknown"),
                username=data.get("version", {})
                .get("by", {})
                .get("username", "unknown"),
                display_name=data.get("version", {})
                .get("by", {})
                .get("displayName", "Unknown User"),
            ),
            "download_url": data.get("_links", {}).get("download", ""),
            "content_id": content_id or data.get("container", {}).get("id", ""),
        }

        return cls(**adapted_data)


class ContentItem(BaseModel):
    """Élément de contenu Confluence (page, blog, etc.)."""

    id: str
    type: str  # page, blogpost, etc.
    status: str  # current, draft, etc.
    title: str
    space: SpaceInfo
    version: Dict[str, Any]
    created: datetime
    creator: UserInfo
    last_updated: datetime
    last_updater: UserInfo
    content_url: str
    web_ui_url: str
    body_storage: Optional[str] = None  # Format de stockage (HTML)
    body_view: Optional[str] = None  # Format d'affichage (HTML)
    body_plain: Optional[str] = None  # Texte brut
    labels: List[LabelInfo] = Field(default_factory=list)
    attachments: List[AttachmentDetail] = Field(default_factory=list)
    parent_id: Optional[str] = None
    ancestors: List[Dict[str, Any]] = Field(default_factory=list)
    children: List[Dict[str, Any]] = Field(default_factory=list)
    processed_chunks: List[Dict[str, Any]] = Field(default_factory=list)

    def get_content_summary(self, max_length: int = 200) -> str:
        """Retourne un résumé du contenu."""
        if self.body_plain:
            if len(self.body_plain) <= max_length:
                return self.body_plain
            return self.body_plain[:max_length] + "..."
        return "Contenu non disponible"

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "ContentItem":
        """Crée une instance ContentItem à partir de données JSON."""
        # Adapter les données JSON au format attendu par le modèle

        # Extraire les informations de l'espace
        space_data = data.get("space", {})
        space = SpaceInfo(
            id=space_data.get("id", "unknown"),
            key=space_data.get("key", "UNKNOWN"),
            name=space_data.get("name", "Unknown Space"),
            type=space_data.get("type", "global"),
        )

        # Extraire les informations du créateur
        creator_data = data.get("history", {}).get("createdBy", {})
        creator = UserInfo(
            id=creator_data.get("accountId", "unknown"),
            username=creator_data.get("username", "unknown"),
            display_name=creator_data.get("displayName", "Unknown User"),
        )

        # Extraire les informations du dernier modificateur
        last_updater_data = data.get("version", {}).get("by", {})
        last_updater = UserInfo(
            id=last_updater_data.get("accountId", creator.id),
            username=last_updater_data.get("username", creator.username),
            display_name=last_updater_data.get("displayName", creator.display_name),
        )

        # Extraire le contenu du body
        body_data = data.get("body", {})
        body_storage = body_data.get("storage", {}).get("value", "")
        body_view = body_data.get("view", {}).get("value", "")

        # Construire les URLs
        base_url = data.get("_links", {}).get("base", "")
        web_ui = data.get("_links", {}).get("webui", "")
        content_url = f"{base_url}/rest/api/content/{data.get('id', '')}"
        web_ui_url = f"{base_url}{web_ui}" if web_ui else ""

        # Gérer le champ children qui peut être un dict ou une liste
        children_data = data.get("children", [])
        if isinstance(children_data, dict):
            # Si c'est un dict comme {"page": {"results": []}}, extraire la liste
            children = []
            for child_type, child_data in children_data.items():
                if isinstance(child_data, dict) and "results" in child_data:
                    children.extend(child_data["results"])
        else:
            children = children_data if isinstance(children_data, list) else []

        adapted_data = {
            "id": data.get("id"),
            "type": data.get("type", "page"),
            "status": data.get("status", "current"),
            "title": data.get("title", "Untitled"),
            "space": space,
            "version": data.get("version", {"number": 1}),
            "created": data.get("history", {}).get("createdDate", datetime.now()),
            "creator": creator,
            "last_updated": data.get("version", {}).get("when", datetime.now()),
            "last_updater": last_updater,
            "content_url": content_url,
            "web_ui_url": web_ui_url,
            "body_storage": body_storage,
            "body_view": body_view,
            "ancestors": data.get("ancestors", []),
            "children": children,
        }

        return cls(**adapted_data)
