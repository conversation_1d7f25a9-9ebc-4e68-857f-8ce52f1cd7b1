#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour les utilitaires de logging.
"""

import asyncio
import json
import logging
import threading
import time
import unittest

from ..logging_utils import (
    CorrelationContext,
    SecurityFilter,
    CorrelationIdFilter,
    StructuredLogFormatter,
    with_correlation_id,
)


class TestCorrelationContext(unittest.TestCase):
    """Tests pour la classe CorrelationContext."""

    def setUp(self):
        """Configuration des tests."""
        # Nettoyer le contexte avant chaque test
        CorrelationContext.clear_correlation_id()

    def tearDown(self):
        """Nettoyage après les tests."""
        # Nettoyer le contexte après chaque test
        CorrelationContext.clear_correlation_id()

    def test_generate_correlation_id(self):
        """Test de génération d'identifiant de corrélation."""
        correlation_id = CorrelationContext.generate_correlation_id()

        self.assertIsNotNone(correlation_id)
        self.assertIsInstance(correlation_id, str)
        self.assertEqual(len(correlation_id), 36)  # UUID4 length

        # Vérifier que l'ID est défini dans le contexte
        self.assertEqual(CorrelationContext.get_correlation_id(), correlation_id)

    def test_set_and_get_correlation_id(self):
        """Test de définition et récupération d'identifiant."""
        test_id = "test-correlation-123"

        CorrelationContext.set_correlation_id(test_id)
        retrieved_id = CorrelationContext.get_correlation_id()

        self.assertEqual(retrieved_id, test_id)

    def test_clear_correlation_id(self):
        """Test de suppression d'identifiant de corrélation."""
        # Définir un ID
        CorrelationContext.set_correlation_id("test-id")
        self.assertIsNotNone(CorrelationContext.get_correlation_id())

        # Supprimer l'ID
        CorrelationContext.clear_correlation_id()
        self.assertIsNone(CorrelationContext.get_correlation_id())

    def test_thread_isolation(self):
        """Test de l'isolation entre threads."""
        results = {}

        def thread_worker(thread_id):
            CorrelationContext.generate_correlation_id()  # Supprimer l'assignation inutile
            time.sleep(0.1)  # Simuler du travail
            results[thread_id] = CorrelationContext.get_correlation_id()

        # Créer plusieurs threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=thread_worker, args=(i,))
            threads.append(thread)
            thread.start()

        # Attendre que tous les threads se terminent
        for thread in threads:
            thread.join()

        # Vérifier que chaque thread a son propre ID
        self.assertEqual(len(results), 3)
        correlation_ids = list(results.values())
        self.assertEqual(len(set(correlation_ids)), 3)  # Tous différents

    def test_no_correlation_id_initially(self):
        """Test qu'aucun ID n'est défini initialement."""
        self.assertIsNone(CorrelationContext.get_correlation_id())


class TestSecurityFilter(unittest.TestCase):
    """Tests pour le filtre de sécurité."""

    def setUp(self):
        """Configuration des tests."""
        self.security_filter = SecurityFilter()

    def create_log_record(self, message, level=logging.INFO):
        """Crée un LogRecord pour les tests."""
        return logging.LogRecord(
            name="test_logger",
            level=level,
            pathname="test_path",
            lineno=1,
            msg=message,
            args=(),
            exc_info=None,
            func="test_func",
        )

    def test_sanitize_tokens(self):
        """Test de masquage des tokens."""
        test_cases = [
            ("Error with token ABC123XYZ456789012345", "Error with token ***TOKEN***"),
            (
                "Auth failed: ABCDEFGHIJKLMNOPQRSTUVWXYZ123456",
                "Auth failed: ***TOKEN***",
            ),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                record = self.create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()
                self.assertEqual(result, expected)

    def test_sanitize_email_token_pairs(self):
        """Test de masquage des paires email:token."""
        test_cases = [
            ("Auth: <EMAIL>:ABCD1234567890123456", "Auth: ***EMAIL:TOKEN***"),
            (
                "<NAME_EMAIL>:XYZ9876543210987654",
                "Login failed ***EMAIL:TOKEN***",
            ),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                record = self.create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()
                self.assertEqual(result, expected)

    def test_preserve_normal_messages(self):
        """Test que les messages normaux sont préservés."""
        normal_messages = [
            "Normal log message",
            "User logged in successfully",
            "Processing 100 items",
        ]

        for message in normal_messages:
            with self.subTest(message=message):
                record = self.create_log_record(message)
                self.security_filter.filter(record)
                result = record.getMessage()
                self.assertEqual(result, message)


class TestCorrelationIdFilter(unittest.TestCase):
    """Tests pour le filtre d'identifiant de corrélation."""

    def setUp(self):
        """Configuration des tests."""
        self.correlation_filter = CorrelationIdFilter()
        CorrelationContext.clear_correlation_id()

    def tearDown(self):
        """Nettoyage après les tests."""
        CorrelationContext.clear_correlation_id()

    def create_log_record(self, message):
        """Crée un LogRecord pour les tests."""
        return logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test_path",
            lineno=1,
            msg=message,
            args=(),
            exc_info=None,
            func="test_func",
        )

    def test_add_correlation_id(self):
        """Test d'ajout d'identifiant de corrélation."""
        # Définir un ID de corrélation
        test_id = "test-correlation-123"
        CorrelationContext.set_correlation_id(test_id)

        # Créer un enregistrement de log
        record = self.create_log_record("Test message")

        # Appliquer le filtre
        result = self.correlation_filter.filter(record)

        self.assertTrue(result)
        self.assertEqual(record.correlation_id, test_id)

    def test_no_correlation_id(self):
        """Test sans identifiant de corrélation."""
        # Ne pas définir d'ID de corrélation
        record = self.create_log_record("Test message")

        # Appliquer le filtre
        result = self.correlation_filter.filter(record)

        self.assertTrue(result)
        self.assertEqual(record.correlation_id, "no-correlation-id")


class TestStructuredLogFormatter(unittest.TestCase):
    """Tests pour le formateur de logs structurés."""

    def setUp(self):
        """Configuration des tests."""
        self.formatter = StructuredLogFormatter(include_traceback=True)

    def create_log_record(self, message, level=logging.INFO, extra=None):
        """Crée un LogRecord pour les tests."""
        record = logging.LogRecord(
            name="test_logger",
            level=level,
            pathname="test_path",
            lineno=1,
            msg=message,
            args=(),
            exc_info=None,
            func="test_func",
        )

        if extra:
            for key, value in extra.items():
                setattr(record, key, value)

        return record

    def test_format_basic_message(self):
        """Test de formatage d'un message basique."""
        record = self.create_log_record("Test message")
        formatted = self.formatter.format(record)

        # Vérifier que c'est du JSON valide
        log_data = json.loads(formatted)

        self.assertEqual(log_data["message"], "Test message")
        self.assertEqual(log_data["level"], "INFO")
        self.assertEqual(log_data["logger"], "test_logger")
        self.assertIn("timestamp", log_data)

    def test_format_with_extra_fields(self):
        """Test de formatage avec champs supplémentaires."""
        extra = {"user_id": 123, "action": "login"}
        record = self.create_log_record("User action", extra=extra)
        formatted = self.formatter.format(record)

        log_data = json.loads(formatted)

        self.assertEqual(log_data["user_id"], 123)
        self.assertEqual(log_data["action"], "login")

    def test_format_with_correlation_id(self):
        """Test de formatage avec identifiant de corrélation."""
        record = self.create_log_record("Test message")
        record.correlation_id = "test-correlation-123"
        formatted = self.formatter.format(record)

        log_data = json.loads(formatted)

        self.assertEqual(log_data["correlation_id"], "test-correlation-123")


class TestWithCorrelationIdDecorator(unittest.TestCase):
    """Tests pour le décorateur with_correlation_id."""

    def tearDown(self):
        """Nettoyage après les tests."""
        CorrelationContext.clear_correlation_id()

    def test_sync_function_decorator(self):
        """Test du décorateur sur une fonction synchrone."""

        @with_correlation_id
        def test_function():
            return CorrelationContext.get_correlation_id()

        # Vérifier qu'aucun ID n'est défini avant l'appel
        self.assertIsNone(CorrelationContext.get_correlation_id())

        # Appeler la fonction décorée et récupérer l'ID généré pendant l'exécution
        result_id = test_function()

        # Vérifier qu'un ID a été généré pendant l'exécution
        self.assertIsNotNone(result_id)

        # Vérifier que l'ID est nettoyé après l'exécution
        self.assertIsNone(CorrelationContext.get_correlation_id())

    def test_async_function_decorator(self):
        """Test du décorateur sur une fonction asynchrone."""

        @with_correlation_id
        async def test_async_function():
            return CorrelationContext.get_correlation_id()

        async def run_test():
            # Vérifier qu'aucun ID n'est défini avant l'appel
            self.assertIsNone(CorrelationContext.get_correlation_id())

            # Appeler la fonction décorée et récupérer l'ID généré pendant l'exécution
            result_id = await test_async_function()

            # Vérifier qu'un ID a été généré pendant l'exécution
            self.assertIsNotNone(result_id)

            # Vérifier que l'ID est nettoyé après l'exécution
            self.assertIsNone(CorrelationContext.get_correlation_id())

        asyncio.run(run_test())

    def test_nested_decorators(self):
        """Test de décorateurs imbriqués."""

        @with_correlation_id
        def outer_function():
            outer_id = CorrelationContext.get_correlation_id()

            @with_correlation_id
            def inner_function():
                return CorrelationContext.get_correlation_id()

            inner_id = inner_function()
            return outer_id, inner_id

        outer_id, inner_id = outer_function()

        # Les IDs doivent être différents (nouveau ID généré pour chaque décorateur)
        self.assertNotEqual(outer_id, inner_id)
        self.assertIsNotNone(outer_id)
        self.assertIsNotNone(inner_id)


if __name__ == "__main__":
    unittest.main()
