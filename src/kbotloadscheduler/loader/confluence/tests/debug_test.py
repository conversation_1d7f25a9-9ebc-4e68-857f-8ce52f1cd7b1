#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de débogage pour le problème d'intégration du ConfluenceLoader.
"""

import json
import tempfile
import unittest
import os
import logging
from datetime import datetime
from pathlib import Path
from types import SimpleNamespace
from unittest.mock import patch, MagicMock

from kbotloadscheduler.loader.confluence.config import (
    ConfluenceConfig,
    StorageConfig,
    HealthCheckConfig,
    SearchCriteria,
)
from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
from kbotloadscheduler.loader.confluence.models import ContentItem, SpaceInfo, UserInfo

# Configuration du logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TestDebug(unittest.TestCase):
    """
    Test pour déboguer le problème d'intégration.
    """

    def setUp(self):
        """Configuration du test avec logs de débogage."""
        logger.debug("Début de setUp")

        self.temp_dir_obj = tempfile.TemporaryDirectory()
        self.storage_path = self.temp_dir_obj.name
        logger.debug(f"Répertoire temporaire créé: {self.storage_path}")
        self.addCleanup(self.temp_dir_obj.cleanup)

        logger.debug(f"Vérification des permissions: {oct(os.stat(self.storage_path).st_mode)}")

        self.config = SimpleNamespace(
            confluence=ConfluenceConfig(
                url="https://mock.confluence.com",
                username="<EMAIL>",
                api_token="faketoken",
                search_criteria=SearchCriteria(
                    cql="space=TEST", space_keys=["TEST"], max_results=100
                ),
            ),
            storage=StorageConfig(provider="file", base_path=self.storage_path),
            health_check=HealthCheckConfig(enabled=False),
        )
        logger.debug("Configuration créée")

        # Création d'un objet ContentItem pour le test
        self.mock_confluence_data = [
            ContentItem(
                id="123",
                type="page",
                status="current",
                title="Test Page 1",
                space=SpaceInfo(id="test-id", key="TEST", name="Test Space", type="global"),
                version={"number": 2},
                created=datetime(2023, 1, 1, 12, 0, 0),
                creator=UserInfo(id="user1", username="user1", display_name="Test User 1"),
                last_updated=datetime(2023, 1, 2, 12, 0, 0),
                last_updater=UserInfo(id="user1", username="user1", display_name="Test User 1"),
                content_url="https://mock.confluence.com/rest/api/content/123",
                web_ui_url="https://mock.confluence.com/spaces/TEST/pages/123",
                body_storage="<p>Content 1</p>",
                parent_id=None,
            )
        ]
        logger.debug("Mock data créée")

    @patch("kbotloadscheduler.loader.confluence.loader.ConfluenceClient")
    def test_debug_workflow(self, MockConfluenceClient):
        """
        Test de débogage pour le workflow.
        """
        logger.debug("Début du test debug_workflow")

        mock_client_instance = MockConfluenceClient.return_value
        mock_client_instance.test_connection = MagicMock(return_value=True)

        # Configuration du mock pour renvoyer les données de test
        mock_client_instance.search_content_by_space = MagicMock(
            return_value=self.mock_confluence_data
        )
        mock_client_instance.close = MagicMock()

        logger.debug("Mock configuré")

        # Création et exécution du loader
        loader = ConfluenceLoader(self.config)
        logger.debug("Loader créé")

        # Exécution du loader
        result = loader.run()
        logger.debug(f"Loader exécuté, résultat: {result}")

        # Vérification des appels au mock
        mock_client_instance.search_content_by_space.assert_called_once_with(
            space_key="TEST"
        )
        logger.debug("Assertion d'appel au mock vérifiée")

        # Vérification de la création du répertoire
        space_storage_path = Path(self.storage_path) / "TEST"
        logger.debug(f"Vérification du chemin: {space_storage_path}")

        # Vérification de l'existence du répertoire et affichage du contenu du répertoire parent
        logger.debug(f"Contenu du répertoire parent: {list(Path(self.storage_path).glob('*'))}")
        exists = space_storage_path.exists()
        logger.debug(f"Le répertoire existe: {exists}")

        # Assertion avec un message d'erreur détaillé
        self.assertTrue(
            exists,
            f"Le répertoire {space_storage_path} n'existe pas. "
            f"Contenu du répertoire parent: {list(Path(self.storage_path).glob('*'))}"
        )

        # Si le répertoire existe, vérification des fichiers créés
        if exists:
            created_files = list(space_storage_path.glob("*.json"))
            logger.debug(f"Fichiers créés: {created_files}")
            self.assertEqual(len(created_files), len(self.mock_confluence_data))

            # Vérification du contenu du premier fichier
            if created_files:
                expected_file_path = space_storage_path / "123.json"
                logger.debug(f"Vérification du fichier: {expected_file_path}")
                with open(expected_file_path, "r", encoding="utf-8") as f:
                    saved_data = json.load(f)
                self.assertEqual(saved_data["id"], "123")

        mock_client_instance.close.assert_called_once()
        logger.debug("Fin du test debug_workflow")


if __name__ == "__main__":
    unittest.main()
