# Documentation des Mocks pour les Tests Confluence

## 📋 Vue d'Ensemble

Ce document décrit les mocks utilisés pour tester le module Confluence dans le contexte de kbot-load-scheduler. Ces
mocks permettent d'isoler les tests des dépendances externes et de créer des environnements de test reproductibles.

## 🎯 Objectifs des Mocks

### 1. **Isolation des Tests**

- Éviter les appels réseau réels vers Confluence
- Éliminer les dépendances à Google Cloud Storage
- Supprimer les appels à Google Secret Manager
- Garantir des tests rapides et reproductibles

### 2. **Simulation Réaliste**

- Reproduire fidèlement le comportement des APIs
- Utiliser des structures de données cohérentes
- Supporter différents scénarios (succès, erreurs, cas limites)

### 3. **Facilité de Maintenance**

- Interfaces compatibles avec les vraies classes
- Configuration flexible pour différents cas de test
- Documentation claire du comportement attendu

## 🔧 Mocks Disponibles

### 1. **MockConfluenceAPI**

**Localisation** : `tests/testutils/mock_confluence.py`

Mock spécialisé pour les tests end-to-end du ConfluenceLoader.

#### Utilisation

```python
from tests.testutils.mock_confluence import MockConfluenceAPI

# Création du mock
mock_api = MockConfluenceAPI()

# Configuration des pages
pages = [
    {
        "id": "page1",
        "title": "Documentation API",
        "type": "page",
        "space": {"key": "DOCS", "name": "Documentation"},
        "body": {"storage": {"value": "<p>Contenu de la page</p>"}},
        "_links": {"webui": "/spaces/DOCS/pages/page1/Documentation+API"}
    }
]
mock_api.set_mock_pages(pages)

# Configuration des attachments
attachments = [
    {
        "id": "att1",
        "title": "schema.pdf",
        "extensions": {"mediaType": "application/pdf"},
        "_links": {"download": "/download/attachments/page1/schema.pdf"}
    }
]
mock_api.set_mock_attachments("page1", attachments)
```

#### Méthodes Disponibles

| Méthode                                      | Description                          | Paramètres                                     |
|----------------------------------------------|--------------------------------------|------------------------------------------------|
| `set_mock_pages(pages)`                      | Configure les pages mockées          | `pages`: Liste de dictionnaires                |
| `set_mock_attachments(page_id, attachments)` | Configure les attachments            | `page_id`: ID de la page, `attachments`: Liste |
| `set_mock_attachment_content(content)`       | Configure le contenu des attachments | `content`: bytes                               |
| `get_mock_pages()`                           | Récupère les pages mockées           | Retourne: Liste                                |
| `get_mock_attachments(page_id)`              | Récupère les attachments d'une page  | `page_id`: ID de la page                       |
| `get_mock_attachment_content()`              | Récupère le contenu des attachments  | Retourne: bytes                                |

### 2. **MockGCSClient**

**Localisation** : `tests/testutils/mock_gcs.py`

Mock du client Google Cloud Storage compatible avec `google.cloud.storage.Client`.

#### Utilisation

```python
from tests.testutils.mock_gcs import MockGCSClient

# Création du client mock
client = MockGCSClient()

# Utilisation comme un vrai client GCS
bucket = client.bucket("test-bucket")
blob = bucket.blob("document.txt")
blob.upload_from_string("Contenu du document")

# Vérifications
assert bucket.exists() == True
assert blob.exists() == True
content = blob.download_as_string()
```

#### Hiérarchie des Classes

```
MockGCSClient
├── bucket(name) → MockGCSBucket
    ├── exists() → bool
    ├── blob(name) → MockGCSBlob
    └── get_blob(name) → MockGCSBlob | None

MockGCSBlob
├── exists() → bool
├── upload_from_string(content, content_type=None)
├── download_as_bytes() → bytes
├── download_as_string() → str
└── delete()
```

### 3. **Mock ConfigWithSecret**

**Localisation** : `tests/loader/test_confluence_end_to_end.py`

Mock pour éviter les appels réels à Google Secret Manager.

#### Configuration

```python
@pytest.fixture
def mock_config_with_secret():
    """Fixture pour créer un ConfigWithSecret mocké."""
    from dependency_injector import providers
    
    # Configuration de test
    config = providers.Configuration()
    config.env.from_value('tests')
    config.gcp_project_id.from_value('')
    
    # Instance mockée
    config_with_secret = ConfigWithSecret(config=config)
    
    # Mock des méthodes get_*
    config_with_secret.get_confluence_credentials = MagicMock(return_value={
        "pat_token": "test-pat-token"
    })
    config_with_secret.get_basic_client_id = MagicMock(return_value="fake-client-id")
    # ... autres méthodes
    
    return config_with_secret
```

## 🎭 Stratégies de Mock

### 1. **Mock par Injection de Dépendances**

Utilisation du container de dépendances pour injecter des mocks :

```python
@pytest.fixture
def container(mock_config_with_secret, mock_env_vars, mock_basic_client):
    """Fixture pour créer le container avec mocks."""
    container = Container()
    
    # Override du provider
    container.configWithSecret.override(mock_config_with_secret)
    
    yield container
```

### 2. **Mock par Patch de Méthodes**

Mock de méthodes spécifiques pour intercepter les appels :

```python
@pytest.fixture
def mock_confluence_client():
    """Mock du client Confluence."""
    mock_api = MockConfluenceAPI()
    
    with patch("kbotloadscheduler.loader.confluence.client.ConfluenceClient.search_content") as mock_search:
        async def mock_search_content(search_criteria):
            # Conversion des données mockées
            content_items = []
            for page_data in mock_api.get_mock_pages():
                content_item = MagicMock()
                content_item.id = page_data["id"]
                content_item.title = page_data["title"]
                # ... configuration complète
                content_items.append(content_item)
            return content_items
        
        mock_search.side_effect = mock_search_content
        yield mock_api
```

### 3. **Mock par Remplacement de Classes**

Remplacement complet de classes pour les tests :

```python
@pytest.fixture
def mock_gcs():
    """Mock de Google Cloud Storage."""
    with patch("google.cloud.storage.Client") as mock_client:
        mock_client.return_value = MockGCSClient()
        yield mock_client
```

## 📊 Données de Test

### Structure des Pages Confluence

```python
page_structure = {
    "id": "123456",
    "title": "Titre de la Page",
    "type": "page",  # ou "blogpost"
    "space": {
        "key": "DOCS",
        "name": "Documentation"
    },
    "body": {
        "storage": {
            "value": "<p>Contenu HTML de la page</p>"
        }
    },
    "version": {
        "number": 1,
        "when": "2024-01-15T10:30:00.000Z"
    },
    "_links": {
        "webui": "/spaces/DOCS/pages/123456/Titre+de+la+Page"
    },
    "metadata": {
        "labels": {
            "results": [
                {"name": "api"},
                {"name": "documentation"}
            ]
        }
    }
}
```

### Structure des Attachments

```python
attachment_structure = {
    "id": "att001",
    "title": "document.pdf",
    "extensions": {
        "fileSize": 1024,
        "mediaType": "application/pdf"
    },
    "version": {
        "createdDate": "2024-01-15T10:30:00.000Z"
    },
    "_links": {
        "download": "/download/attachments/123456/document.pdf"
    }
}
```

## 🧪 Scénarios de Test

### 1. **Scénario de Succès**

```python
def setup_success_scenario(mock_api):
    """Configure un scénario de succès avec données complètes."""
    pages = [
        {
            "id": "page1",
            "title": "Page de Test",
            "type": "page",
            "space": {"key": "TEST", "name": "Test Space"},
            "body": {"storage": {"value": "<p>Contenu de test</p>"}},
            "_links": {"webui": "/pages/viewpage.action?pageId=page1"}
        }
    ]
    
    attachments = [
        {
            "id": "att1",
            "title": "test.pdf",
            "extensions": {"mediaType": "application/pdf"},
            "_links": {"download": "/download/attachments/page1/test.pdf"}
        }
    ]
    
    mock_api.set_mock_pages(pages)
    mock_api.set_mock_attachments("page1", attachments)
    mock_api.set_mock_attachment_content(b"Contenu PDF de test")
```

### 2. **Scénario d'Erreur**

```python
def setup_error_scenario(mock_api):
    """Configure un scénario d'erreur."""
    # Aucune page disponible
    mock_api.set_mock_pages([])
    
    # Ou pages avec données incomplètes
    incomplete_pages = [
        {
            "id": "page1",
            "title": "Page Incomplète",
            # Champs manquants intentionnellement
        }
    ]
    mock_api.set_mock_pages(incomplete_pages)
```

### 3. **Scénario de Performance**

```python
def setup_performance_scenario(mock_api):
    """Configure un scénario avec beaucoup de données."""
    pages = []
    for i in range(100):
        pages.append({
            "id": f"page{i}",
            "title": f"Page de Test {i}",
            "type": "page",
            "space": {"key": "PERF", "name": "Performance Test"},
            "body": {"storage": {"value": f"<p>Contenu de test {i}</p>"}},
            "_links": {"webui": f"/pages/viewpage.action?pageId=page{i}"}
        })
    
    mock_api.set_mock_pages(pages)
```

## 🔧 Configuration des Tests

### Variables d'Environnement

```python
@pytest.fixture
def mock_env_vars():
    """Configure les variables d'environnement pour les tests."""
    original_env = os.environ.copy()
    
    os.environ["CONFLUENCE_URL"] = "https://test-confluence.example.com"
    os.environ["DEFAULT_SPACE_KEY"] = "TEST"
    
    yield
    
    # Restauration
    os.environ.clear()
    os.environ.update(original_env)
```

### Fixtures Réutilisables

```python
@pytest.fixture
def test_source():
    """Fixture pour créer une source de test."""
    source_config = {
        "spaces": ["TEST", "DOCS"],
        "max_results": 10,
        "include_attachments": True,
        "content_types": ["page"],
        "labels": ["test-label"]
    }
    
    return SourceBean(
        id=1,
        code="test_confluence",
        label="Test Confluence",
        src_type="confluence",
        configuration=json.dumps(source_config),
        last_load_time=1726156483,
        load_interval=24,
        domain_code="test-domain",
        perimeter_code="test-perimeter"
    )
```

## 🚀 Bonnes Pratiques

### 1. **Isolation Complète**

- Aucun appel réseau réel
- Mocks pour toutes les dépendances externes
- Tests reproductibles et rapides

### 2. **Données Réalistes**

- Structures de données cohérentes avec les APIs réelles
- Cas d'usage variés (succès, erreurs, cas limites)
- Volumes de données représentatifs

### 3. **Maintenance Facile**

- Interfaces compatibles avec les vraies classes
- Configuration centralisée des mocks
- Documentation claire du comportement

### 4. **Performance**

- Mocks légers et rapides
- Éviter les opérations coûteuses
- Optimiser pour les tests fréquents

## 🔄 Maintenance des Mocks

### Synchronisation avec les APIs

1. **Confluence API** : Vérifier les changements dans l'API REST
2. **Google Cloud Storage** : Suivre les mises à jour de la bibliothèque
3. **Interfaces Internes** : Maintenir la compatibilité

### Tests des Mocks

```python
def test_mock_confluence_api():
    """Teste que MockConfluenceAPI fonctionne correctement."""
    mock_api = MockConfluenceAPI()
    
    # Test de configuration
    pages = [{"id": "test", "title": "Test"}]
    mock_api.set_mock_pages(pages)
    
    # Test de récupération
    retrieved_pages = mock_api.get_mock_pages()
    assert len(retrieved_pages) == 1
    assert retrieved_pages[0]["id"] == "test"
```

Cette documentation sera mise à jour au fur et à mesure de l'évolution des mocks et des besoins de test.
