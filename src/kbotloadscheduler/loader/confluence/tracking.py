#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Suivi des changements pour le système RAG Confluence.
"""

import hashlib
import json
import logging
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

from .logging_utils import CorrelationContext
from .models import ContentItem, AttachmentDetail
from .utils import TextProcessor


class ConfluenceChangeTracker:
    """Suivi des changements dans les contenus Confluence."""

    def __init__(self, storage_dir: str = ".confluence_rag_data"):
        """Initialise le tracker avec le répertoire de stockage spécifié."""
        self.storage_dir = storage_dir
        self.logger = logging.getLogger(__name__)

        # Créer le répertoire de stockage s'il n'existe pas
        os.makedirs(storage_dir, exist_ok=True)
        os.makedirs(os.path.join(storage_dir, "content_hashes"), exist_ok=True)
        os.makedirs(os.path.join(storage_dir, "attachment_hashes"), exist_ok=True)
        os.makedirs(os.path.join(storage_dir, "sync_history"), exist_ok=True)

    def has_content_changed(self, content_item: ContentItem) -> bool:
        """Vérifie si un contenu a changé depuis la dernière synchronisation."""
        content_id = content_item.id
        hash_file = os.path.join(
            self.storage_dir, "content_hashes", f"{content_id}.json"
        )

        # Générer le hash du contenu actuel
        current_hash = self._generate_content_hash(content_item)

        # Vérifier si le fichier de hash existe
        if not os.path.exists(hash_file):
            # Le contenu n'a jamais été synchronisé
            self._save_content_hash(content_id, current_hash)
            return True

        # Charger le hash précédent
        try:
            with open(hash_file, "r", encoding="utf-8") as f:
                previous_hash_data = json.load(f)
                previous_hash = previous_hash_data.get("hash", "")
        except Exception as e:
            self.logger.error(
                f"Erreur lors du chargement du hash précédent pour {content_id}: {e}"
            )
            # En cas d'erreur, considérer que le contenu a changé
            return True

        # Comparer les hashs
        has_changed = current_hash != previous_hash

        # Si le contenu a changé, mettre à jour le hash
        if has_changed:
            self._save_content_hash(content_id, current_hash)

        return has_changed

    def has_attachment_changed(self, attachment: AttachmentDetail) -> bool:
        """Vérifie si une pièce jointe a changé depuis la dernière synchronisation."""
        attachment_id = attachment.id
        hash_file = os.path.join(
            self.storage_dir, "attachment_hashes", f"{attachment_id}.json"
        )

        # Générer le hash de la pièce jointe actuelle
        current_hash = self._generate_attachment_hash(attachment)

        # Vérifier si le fichier de hash existe
        if not os.path.exists(hash_file):
            # La pièce jointe n'a jamais été synchronisée
            self._save_attachment_hash(attachment_id, current_hash)
            return True

        # Charger le hash précédent
        try:
            with open(hash_file, "r", encoding="utf-8") as f:
                previous_hash_data = json.load(f)
                previous_hash = previous_hash_data.get("hash", "")
        except Exception as e:
            self.logger.error(
                f"Erreur lors du chargement du hash précédent pour la pièce jointe {attachment_id}: {e}"
            )
            # En cas d'erreur, considérer que la pièce jointe a changé
            return True

        # Comparer les hashs
        has_changed = current_hash != previous_hash

        # Si la pièce jointe a changé, mettre à jour le hash
        if has_changed:
            self._save_attachment_hash(attachment_id, current_hash)

        return has_changed

    def record_sync(self, content_items: List[ContentItem]) -> Dict[str, Any]:
        """Enregistre une synchronisation dans l'historique."""
        timestamp = datetime.now().isoformat()
        sync_id = f"sync_{timestamp.replace(':', '-')}"

        # Récupérer l'identifiant de corrélation actuel
        correlation_id = CorrelationContext.get_correlation_id()

        # Collecter les statistiques
        stats = {
            "sync_id": sync_id,
            "timestamp": timestamp,
            "correlation_id": correlation_id or "no-correlation-id",
            "total_content_items": len(content_items),
            "total_attachments": sum(len(item.attachments) for item in content_items),
            "spaces_processed": list(set(item.space.key for item in content_items)),
            "content_types": self._count_content_types(content_items),
            "content_ids": [item.id for item in content_items],
        }

        # Enregistrer les statistiques
        sync_file = os.path.join(self.storage_dir, "sync_history", f"{sync_id}.json")
        try:
            with open(sync_file, "w", encoding="utf-8") as f:
                json.dump(stats, f, indent=2)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de l'enregistrement de la synchronisation {sync_id}: {e}"
            )

        return stats

    def get_last_sync_info(self) -> Optional[Dict[str, Any]]:
        """Récupère les informations de la dernière synchronisation."""
        sync_dir = os.path.join(self.storage_dir, "sync_history")

        # Vérifier si le répertoire existe et n'est pas vide
        if not os.path.exists(sync_dir) or not os.listdir(sync_dir):
            return None

        # Trouver le fichier de synchronisation le plus récent
        sync_files = sorted(os.listdir(sync_dir), reverse=True)
        if not sync_files:
            return None

        # Charger les informations de synchronisation
        last_sync_file = os.path.join(sync_dir, sync_files[0])
        try:
            with open(last_sync_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(
                f"Erreur lors du chargement des informations de la dernière synchronisation: {e}"
            )
            return None

    def _generate_content_hash(self, content_item: ContentItem) -> str:
        """Génère un hash pour un contenu."""
        # Utiliser les champs pertinents pour le hash
        hash_data = {
            "id": content_item.id,
            "title": content_item.title,
            "version": content_item.version,
            "last_updated": (
                content_item.last_updated.isoformat()
                if hasattr(content_item.last_updated, "isoformat")
                else str(content_item.last_updated)
            ),
            "body_hash": TextProcessor.generate_content_hash(
                content_item.body_storage or ""
            ),
        }

        # Convertir en JSON et générer le hash
        hash_json = json.dumps(hash_data, sort_keys=True)
        return hashlib.sha256(hash_json.encode("utf-8")).hexdigest()

    def _generate_attachment_hash(self, attachment: AttachmentDetail) -> str:
        """Génère un hash pour une pièce jointe."""
        # Utiliser les champs pertinents pour le hash
        hash_data = {
            "id": attachment.id,
            "title": attachment.title,
            "file_name": attachment.file_name,
            "file_size": attachment.file_size,
            "media_type": attachment.media_type,
            "created": str(attachment.created),
        }

        # Convertir en JSON et générer le hash
        hash_json = json.dumps(hash_data, sort_keys=True)
        return hashlib.sha256(hash_json.encode("utf-8")).hexdigest()

    def _save_content_hash(self, content_id: str, content_hash: str) -> None:
        """Enregistre le hash d'un contenu."""
        hash_file = os.path.join(
            self.storage_dir, "content_hashes", f"{content_id}.json"
        )
        hash_data = {
            "id": content_id,
            "hash": content_hash,
            "timestamp": datetime.now().isoformat(),
        }

        try:
            with open(hash_file, "w", encoding="utf-8") as f:
                json.dump(hash_data, f, indent=2)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de l'enregistrement du hash pour {content_id}: {e}"
            )

    def _save_attachment_hash(self, attachment_id: str, attachment_hash: str) -> None:
        """Enregistre le hash d'une pièce jointe."""
        hash_file = os.path.join(
            self.storage_dir, "attachment_hashes", f"{attachment_id}.json"
        )
        hash_data = {
            "id": attachment_id,
            "hash": attachment_hash,
            "timestamp": datetime.now().isoformat(),
        }

        try:
            with open(hash_file, "w", encoding="utf-8") as f:
                json.dump(hash_data, f, indent=2)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de l'enregistrement du hash pour la pièce jointe {attachment_id}: {e}"
            )

    def _count_content_types(self, content_items: List[ContentItem]) -> Dict[str, int]:
        """Compte les types de contenu."""
        type_counts = {}
        for item in content_items:
            content_type = item.type
            if content_type in type_counts:
                type_counts[content_type] += 1
            else:
                type_counts[content_type] = 1
        return type_counts
