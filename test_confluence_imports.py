#!/usr/bin/env python3

import sys
import os

# Add the src and tests directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
tests_path = os.path.join(current_dir, 'tests')

sys.path.insert(0, src_path)
sys.path.insert(0, tests_path)

def test_confluence_imports():
    """Test confluence module imports individually"""
    
    confluence_imports = [
        ("config", "from kbotloadscheduler.loader.confluence.config import ConfluenceConfig"),
        ("models", "from kbotloadscheduler.loader.confluence.models import ContentItem"),
        ("orchestrator", "from kbotloadscheduler.loader.confluence.orchestrator import SyncOrchestrator"),
        ("abstract_loader", "from kbotloadscheduler.loader.abstract_loader import AbstractLoader"),
    ]
    
    for name, import_statement in confluence_imports:
        try:
            print(f"Testing import: {name}")
            exec(import_statement)
            print(f"✅ {name} import successful")
        except Exception as e:
            print(f"❌ {name} import failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

if __name__ == "__main__":
    print("Testing confluence module imports...")
    print("=" * 50)
    
    success = test_confluence_imports()
    
    if success:
        print("🎉 All confluence imports passed!")
        print("Now testing full loader import...")
        
        try:
            from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
            print("✅ Full loader import successful!")
        except Exception as e:
            print(f"❌ Full loader import failed: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("⚠️  Some confluence imports failed")
