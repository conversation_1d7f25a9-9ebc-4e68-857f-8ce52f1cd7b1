#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utilitaires pour le système RAG Confluence.
"""

import asyncio
import functools
import hashlib
import logging
import os
import random
import re
from typing import List, Any, Optional, Callable, TypeVar, Awaitable, Union, Type, cast

from bs4 import BeautifulSoup

from .config import RetryConfig
from .exceptions import SecurityValidationError, APIError, RateLimitExceededError

# Type variables pour le décorateur de retry
T = TypeVar("T")
F = TypeVar("F", bound=Callable[..., Awaitable[Any]])
ExceptionType = Union[Type[Exception], tuple[Type[Exception], ...]]


class SecurityValidator:
    """Validateur de sécurité pour les contenus et les pièces jointes."""

    # Liste des extensions de fichiers potentiellement dangereuses
    DANGEROUS_EXTENSIONS = [
        ".exe",
        ".bat",
        ".cmd",
        ".sh",
        ".ps1",
        ".vbs",
        ".js",
        ".jar",
        ".dll",
        ".so",
    ]

    # Liste des types MIME potentiellement dangereux
    DANGEROUS_MIME_TYPES = [
        "application/x-msdownload",
        "application/x-executable",
        "application/x-dosexec",
        "application/java-archive",
        "application/x-shockwave-flash",
    ]

    @classmethod
    def validate_attachment(cls, file_name: str, mime_type: str) -> bool:
        """Valide qu'une pièce jointe est sécurisée."""
        # Vérifier l'extension du fichier
        _, ext = os.path.splitext(file_name.lower())
        if ext in cls.DANGEROUS_EXTENSIONS:
            raise SecurityValidationError(f"Extension de fichier non autorisée: {ext}")

        # Vérifier le type MIME
        if mime_type in cls.DANGEROUS_MIME_TYPES:
            raise SecurityValidationError(f"Type MIME non autorisé: {mime_type}")

        return True

    @classmethod
    def sanitize_html(cls, html_content: str) -> str:
        """Nettoie le contenu HTML des scripts et autres éléments dangereux."""
        if not html_content:
            return ""

        soup = BeautifulSoup(html_content, "html.parser")

        # Supprimer les balises script
        for script in soup.find_all("script"):
            script.decompose()

        # Supprimer les gestionnaires d'événements inline
        for tag in soup.find_all(True):
            for attr in list(tag.attrs):
                if attr.startswith("on"):
                    del tag[attr]

        # Supprimer les liens javascript:
        for a in soup.find_all("a", href=True):
            if a["href"].startswith("javascript:"):
                a["href"] = "#"

        return str(soup)


class TextProcessor:
    """Processeur de texte pour le traitement des contenus."""

    @staticmethod
    def html_to_plain_text(html_content: str) -> str:
        """Convertit le HTML en texte brut."""
        if not html_content:
            return ""

        # Nettoyer d'abord le HTML
        clean_html = SecurityValidator.sanitize_html(html_content)

        # Convertir en texte brut
        soup = BeautifulSoup(clean_html, "html.parser")

        # Remplacer les sauts de ligne par des espaces
        for br in soup.find_all("br"):
            br.replace_with("\n")

        # Remplacer les paragraphes par des doubles sauts de ligne
        for p in soup.find_all("p"):
            p.append("\n\n")

        # Extraire le texte
        text = soup.get_text()

        # Nettoyer les espaces multiples mais préserver les sauts de ligne
        text = re.sub(
            r"[ \t]+", " ", text
        )  # Remplacer seulement les espaces et tabs multiples
        text = re.sub(
            r" *\n *", "\n", text
        )  # Nettoyer les espaces autour des sauts de ligne

        # Nettoyer les sauts de ligne multiples
        text = re.sub(r"\n\s*\n", "\n\n", text)

        return text.strip()

    @staticmethod
    def chunk_text(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Découpe le texte en morceaux de taille fixe avec chevauchement."""
        if not text:
            return []

        chunks = []
        start = 0
        text_length = len(text)

        # S'assurer que l'overlap n'est pas trop grand
        overlap = min(overlap, chunk_size // 2)

        while start < text_length:
            # Calculer la fin du chunk
            end = min(start + chunk_size, text_length)

            # Si ce n'est pas le dernier chunk, essayer de ne pas couper au milieu d'un mot
            if end < text_length:
                # Chercher le dernier espace dans les 20 derniers caractères
                search_start = max(start, end - 20)
                last_space = text.rfind(" ", search_start, end)

                if last_space > start:
                    end = last_space

            # Extraire le chunk
            chunk = text[start:end].strip()
            if chunk:  # Ajouter seulement si le chunk n'est pas vide
                chunks.append(chunk)

            # Si on a atteint la fin du texte, arrêter
            if end >= text_length:
                break

            # Calculer le début du prochain chunk avec overlap
            start = max(end - overlap, start + 1)  # S'assurer qu'on progresse toujours

        return chunks

    @staticmethod
    def generate_content_hash(content: str) -> str:
        """Génère un hash SHA-256 du contenu pour le suivi des modifications."""
        if not content:
            return ""

        return hashlib.sha256(content.encode("utf-8")).hexdigest()


class RetryHandler:
    """Gestionnaire de retry avec backoff exponentiel pour les appels API."""

    def __init__(self, config: RetryConfig):
        """Initialise le gestionnaire avec la configuration spécifiée."""
        self.config = config
        self.logger = logging.getLogger(__name__)

    def calculate_next_wait_time(self, attempt: int) -> float:
        """
        Calcule le temps d'attente pour la prochaine tentative avec backoff exponentiel.

        Args:
            attempt: Numéro de la tentative actuelle (commence à 1)

        Returns:
            Temps d'attente en secondes
        """
        # Calculer le backoff exponentiel de base
        wait_time = min(
            self.config.max_backoff,
            self.config.initial_backoff * (self.config.backoff_factor ** (attempt - 1)),
        )

        # Ajouter un jitter (variation aléatoire) si configuré
        if self.config.jitter:
            # Ajouter une variation aléatoire de ±15%
            jitter_factor = 1.0 + random.uniform(-0.15, 0.15)
            wait_time = wait_time * jitter_factor

        return wait_time

    @staticmethod
    def should_retry_exception(exception: Exception) -> tuple[bool, Optional[float]]:
        """
        Détermine si une exception justifie une nouvelle tentative.

        Args:
            exception: L'exception à évaluer

        Returns:
            Tuple (retry, wait_time) où:
                - retry: True si l'exception justifie une nouvelle tentative
                - wait_time: Temps d'attente suggéré (si disponible dans l'exception)
        """
        # Cas spécial pour RateLimitExceededError qui contient un temps d'attente
        if isinstance(exception, RateLimitExceededError) and exception.retry_after:
            return True, exception.retry_after

        # Cas pour les erreurs API temporaires
        if isinstance(exception, APIError):
            # Vérifier si le code de statut est dans la liste des codes à réessayer
            if hasattr(exception, "status_code") and exception.status_code in [
                429,
                500,
                502,
                503,
                504,
            ]:
                return True, None

        # Cas pour les erreurs réseau temporaires
        if (
            "timeout" in str(exception).lower()
            or "connection" in str(exception).lower()
        ):
            return True, None

        # Par défaut, ne pas réessayer
        return False, None

    @classmethod
    def async_retry(
        cls,
        retry_config: Optional[RetryConfig] = None,
        retry_on_exceptions: ExceptionType = (Exception,),
        max_tries: Optional[int] = None,
        logger: Optional[logging.Logger] = None,
    ):
        """
        Décorateur pour réessayer les fonctions asynchrones avec backoff exponentiel.

        Args:
            retry_config: Configuration de retry à utiliser
            retry_on_exceptions: Exceptions qui déclenchent une nouvelle tentative
            max_tries: Nombre maximum de tentatives (remplace la valeur de la config)
            logger: Logger à utiliser (si différent du logger par défaut)

        Returns:
            Décorateur pour fonctions asynchrones
        """

        def decorator(func: F) -> F:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                # Utiliser la configuration par défaut si non spécifiée
                config = retry_config or RetryConfig()

                # Utiliser le nombre maximum de tentatives spécifié ou celui de la config
                attempts_limit = max_tries or config.max_retries

                # Utiliser le logger spécifié ou celui par défaut
                log = logger or logging.getLogger(__name__)

                # Créer une instance du gestionnaire de retry
                handler = cls(config)

                attempt = 1
                last_exception = None

                while attempt <= attempts_limit:
                    try:
                        return await func(*args, **kwargs)
                    except retry_on_exceptions as e:
                        last_exception = e

                        # Vérifier si l'exception justifie une nouvelle tentative
                        should_retry, suggested_wait = handler.should_retry_exception(e)

                        if not should_retry or attempt >= attempts_limit:
                            # Ne pas réessayer ou limite atteinte
                            log.warning(
                                f"Échec définitif après {attempt}/{attempts_limit} tentatives: {str(e)}"
                            )
                            raise

                        # Calculer le temps d'attente
                        wait_time = suggested_wait or handler.calculate_next_wait_time(
                            attempt
                        )

                        log.info(
                            f"Tentative {attempt}/{attempts_limit} échouée: {str(e)}. "
                            f"Nouvelle tentative dans {wait_time:.2f} secondes."
                        )

                        # Attendre avant la prochaine tentative
                        await asyncio.sleep(wait_time)
                        attempt += 1

                # Si on arrive ici, c'est qu'on a épuisé toutes les tentatives
                if last_exception:
                    raise last_exception

                # Cas improbable, mais pour satisfaire le type checker
                raise RuntimeError("Toutes les tentatives ont échoué sans exception")

            return cast(F, wrapper)

        return decorator
