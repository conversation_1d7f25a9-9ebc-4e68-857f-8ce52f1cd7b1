#!/usr/bin/env python3
"""
Direct validation script to test our ConfluenceLoader fix
"""
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_confluence_config_creation():
    """Test that we can create ConfluenceConfig with SecretStr tokens"""
    try:
        from kbotloadscheduler.loader.confluence.config import ConfluenceConfig
        from pydantic import SecretStr, HttpUrl
        
        # Test creating config with SecretStr tokens (as our fix does)
        config = ConfluenceConfig(
            confluence_url=HttpUrl("https://example.atlassian.net"),
            confluence_username="test_user",
            pat_token=SecretStr("test_pat_token"),
            api_token=SecretStr("test_api_token"),
            space_key="TEST"
        )
        
        print("✓ ConfluenceConfig creation with SecretStr tokens: SUCCESS")
        return True
    except Exception as e:
        print(f"✗ ConfluenceConfig creation failed: {e}")
        return False

def test_loader_config_creation():
    """Test that the loader can create ConfluenceConfig with our fix"""
    try:
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        
        # Create a mock config similar to what the test uses
        mock_config = {
            'confluence': {
                'confluence_url': 'https://example.atlassian.net',
                'confluence_username': 'test_user',
                'pat_token': 'test_pat_token',
                'api_token': 'test_api_token', 
                'space_key': 'TEST'
            }
        }
        
        # Test the _create_confluence_config method
        loader = ConfluenceLoader()
        confluence_config = loader._create_confluence_config(mock_config['confluence'])
        
        print("✓ ConfluenceLoader._create_confluence_config: SUCCESS")
        print(f"  - confluence_url: {confluence_config.confluence_url}")
        print(f"  - confluence_username: {confluence_config.confluence_username}")
        print(f"  - space_key: {confluence_config.space_key}")
        print("  - pat_token: [SecretStr]")
        print("  - api_token: [SecretStr]")
        
        return True
    except Exception as e:
        print(f"✗ ConfluenceLoader config creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_confluence_import():
    """Test that we can import MockConfluence"""
    try:
        from tests.testutils.mock_confluence import MockConfluence
        print("✓ MockConfluence import: SUCCESS")
        return True
    except Exception as e:
        print(f"✗ MockConfluence import failed: {e}")
        return False

if __name__ == "__main__":
    # Write results to file for inspection
    with open('validation_results.txt', 'w') as f:
        def log_print(msg):
            print(msg)
            f.write(msg + '\n')
            f.flush()
        
        log_print("=== Direct Validation of ConfluenceLoader Fix ===\n")
        
        success_count = 0
        total_tests = 3
        
        # Redirect print to also write to file
        original_print = print
        def print_and_log(msg):
            original_print(msg)
            f.write(str(msg) + '\n')
            f.flush()
        
        # Temporarily replace print
        import builtins
        builtins.print = print_and_log
        
        try:
            if test_confluence_config_creation():
                success_count += 1
            
            if test_loader_config_creation():
                success_count += 1
                
            if test_mock_confluence_import():
                success_count += 1
        finally:
            # Restore original print
            builtins.print = original_print
        
        log_print(f"\n=== Results: {success_count}/{total_tests} tests passed ===")
        
        if success_count == total_tests:
            log_print("🎉 All validation tests passed! The fix should work.")
        else:
            log_print("❌ Some validation tests failed. Check the errors above.")
    
    sys.exit(0 if success_count == total_tests else 1)
