# Stratégie de Tests - Support GCS et Tracking

## 📋 Vue d'ensemble

Ce document décrit la stratégie de tests mise en place pour supporter le nouveau système de tracking avec stockage GCS,
tout en maintenant la compatibilité avec les environnements locaux et les pipelines CI/CD.

## 🎯 Objectifs

- ✅ **Tests complets** : Couvrir les deux modes de stockage (filesystem et GCS)
- ✅ **Isolation** : Tests indépendants sans dépendances externes
- ✅ **CI/CD friendly** : Fonctionnement optimal dans GitLab CI
- ✅ **Développement local** : Tests rapides en local sans GCS

## 🏗️ Architecture des Tests

### 1. **Séparation par Type de Stockage**

#### **Tests Filesystem** (`test_tracking.py`)

- ✅ **Markers** : `@pytest.mark.filesystem`, `@pytest.mark.tracking`
- ✅ **Scope** : Tests du `ConfluenceChangeTracker` original
- ✅ **Environnement** : Fonctionne partout (local, CI/CD)
- ✅ **Couverture** : Workflow complet de tracking filesystem

#### **Tests GCS** (`test_tracking_gcs.py`)

- ✅ **Markers** : `@pytest.mark.gcs`, `@pytest.mark.tracking`
- ✅ **Scope** : Tests du `ConfluenceChangeTrackerGCS` avec mocks
- ✅ **Environnement** : Mocks GCS, pas de vraies dépendances
- ✅ **Couverture** : API GCS, factory, intégration

### 2. **Factory et Intégration**

- ✅ **Tests unitaires** : Factory `get_change_tracker()`
- ✅ **Tests d'intégration** : Workflow complet avec mocks
- ✅ **Validation** : Cohérence entre les deux implémentations

## 🧪 Stratégie de Mocking

### **GCS Mocking Strategy**

```python
@patch('confluence_rag.tracking_gcs.gcs_storage')
@patch('confluence_rag.tracking_gcs.GCS_AVAILABLE', True)
@patch('confluence_rag.tracking_gcs.get_thread_pool_manager')
async def test_gcs_functionality(self, mock_thread_manager, mock_gcs_storage):
    # Setup mocks pour simuler GCS sans dépendances
    mock_client = Mock()
    mock_bucket = Mock()
    mock_blob = Mock()
    
    # Configuration des comportements attendus
    mock_bucket.exists.return_value = True
    mock_bucket.blob.return_value = mock_blob
    mock_client.bucket.return_value = mock_bucket
    mock_gcs_storage.Client.return_value = mock_client
    
    # Tests de l'API sans vraie connexion GCS
```

### **Avantages du Mocking**

- 🚀 **Performance** : Tests rapides sans I/O réseau
- 🔒 **Isolation** : Pas de dépendances externes
- 🎯 **Contrôle** : Simulation de tous les cas (succès, erreurs)
- 💰 **Coût** : Pas de frais GCS pour les tests

## 🔄 Pipeline GitLab CI

### **Configuration Optimisée**

```yaml
# Variables pour désactiver GCS en CI
variables:
  STORAGE_TYPE: "filesystem"
  TRACKING_STORAGE_TYPE: "filesystem"
  
# Tests par catégorie
test:tracking:
  script:
    - pytest confluence_rag/tests/test_tracking.py -v
    - pytest confluence_rag/tests/test_tracking_gcs.py -v
```

### **Stages de Tests**

1. **validate** : Validation de la structure et imports
2. **test:unit** : Tests unitaires rapides
3. **test:tracking** : Tests spécifiques au tracking
4. **test:security** : Tests de sécurité
5. **test:performance** : Tests de performance
6. **test:integration** : Tests d'intégration avec mocks

## 📊 Markers Pytest

### **Nouveaux Markers**

```ini
markers =
    tracking: Tests du système de suivi des changements
    gcs: Tests nécessitant Google Cloud Storage (ignorés en CI)
    filesystem: Tests utilisant le stockage filesystem
```

### **Utilisation des Markers**

```bash
# Tests filesystem uniquement
pytest -m "filesystem and tracking" -v

# Tests GCS avec mocks
pytest -m "gcs and tracking" -v

# Tous les tests de tracking
pytest -m "tracking" -v

# Exclure les tests GCS en CI
pytest -m "not gcs" -v
```

## 🏠 Développement Local

### **Configuration Recommandée**

```bash
# .env local
STORAGE_TYPE=filesystem
TRACKING_STORAGE_TYPE=filesystem
TRACKING_STORAGE_DIR=./local_tracking
```

### **Tests Rapides**

```bash
# Tests filesystem uniquement (rapides)
pytest confluence_rag/tests/test_tracking.py -v

# Factory tests (validation des deux modes)
pytest confluence_rag/tests/test_tracking_gcs.py::TestChangeTrackerFactory -v

# Workflow complet local
python run_tests.py --unit --pattern="tracking"
```

## 🌐 Environnement de Production

### **Tests avec GCS Réel** (optionnel)

```bash
# Configuration pour tests GCS réels
export TRACKING_STORAGE_TYPE=gcs
export TRACKING_GCS_BUCKET_NAME=test-tracking-bucket
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Tests d'intégration GCS (si disponible)
pytest confluence_rag/tests/test_tracking_gcs.py -v --tb=short
```

### **Validation de Production**

- ✅ Tests filesystem en CI/CD
- ✅ Tests GCS avec mocks
- ✅ Validation manuelle GCS en staging
- ✅ Monitoring en production

## 🔧 Commandes Utiles

### **Tests de Développement**

```bash
# Tests rapides tracking
pytest -m "tracking and filesystem" -v

# Tests complets avec couverture
pytest confluence_rag/tests/test_tracking*.py --cov=confluence_rag.tracking --cov-report=html

# Validation factory
python -c "
from confluence_rag.tracking_gcs import get_change_tracker
fs_tracker = get_change_tracker('filesystem', storage_dir='./test')
print('✅ Factory filesystem OK')
"
```

### **Tests CI/CD**

```bash
# Simulation pipeline locale
export CONFLUENCE_RAG_ENVIRONMENT=test
export STORAGE_TYPE=filesystem
pytest confluence_rag/tests/ -m "not gcs" -v

# Tests avec markers
pytest --strict-markers -m "unit and tracking" -v
```

## 📈 Métriques et Couverture

### **Objectifs de Couverture**

- 🎯 **Tracking filesystem** : 95%+
- 🎯 **Tracking GCS (mocks)** : 90%+
- 🎯 **Factory et intégration** : 100%

### **Validation Continue**

- ✅ Tests automatiques à chaque commit
- ✅ Couverture de code dans GitLab CI
- ✅ Validation des deux modes de stockage
- ✅ Tests de régression automatiques

## 🚀 Bonnes Pratiques

### **Pour les Développeurs**

1. **Tests locaux** : Toujours tester en filesystem d'abord
2. **Mocks GCS** : Utiliser les mocks pour les tests GCS
3. **Markers** : Bien marquer les tests selon leur type
4. **Factory** : Tester les deux modes via la factory

### **Pour la CI/CD**

1. **Isolation** : Pas de dépendances externes
2. **Performance** : Tests rapides avec mocks
3. **Couverture** : Validation de tous les chemins de code
4. **Artifacts** : Conserver les résultats de tests

Cette stratégie garantit une couverture complète des fonctionnalités de tracking tout en maintenant la simplicité et la
rapidité des tests dans tous les environnements.
