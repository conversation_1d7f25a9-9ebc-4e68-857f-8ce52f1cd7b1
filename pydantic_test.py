#!/usr/bin/env python3
"""
Test pydantic minimal example
"""

import sys
sys.path.append('src')

try:
    print("1. Testing basic pydantic import...")
    from pydantic import BaseModel, Field
    print("✅ Basic pydantic imported")
    
    print("2. Testing SecretStr import...")
    from pydantic import SecretStr
    print("✅ SecretStr imported")
    
    print("3. Testing field_validator import...")
    from pydantic import field_validator
    print("✅ field_validator imported")
    
    print("4. Testing model_validator import...")
    from pydantic import model_validator
    print("✅ model_validator imported")
    
    print("5. Testing HttpUrl import...")
    from pydantic import HttpUrl
    print("✅ HttpUrl imported")
    
    print("6. Creating a simple model...")
    class TestModel(BaseModel):
        name: str = Field(..., description="Test field")
        
        @field_validator('name')
        @classmethod
        def validate_name(cls, v):
            return v.strip()
    
    print("✅ Simple model created")
    
    print("7. Testing model instantiation...")
    test_instance = TestModel(name="test")
    print(f"✅ Model instantiated: {test_instance.name}")
    
    print("🎉 All pydantic tests passed!")
    
except Exception as e:
    print(f"❌ Pydantic test failed: {e}")
    import traceback
    traceback.print_exc()
