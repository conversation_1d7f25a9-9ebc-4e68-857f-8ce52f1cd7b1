#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gestion du stockage pour le système RAG Confluence.
Supporte le stockage sur système de fichiers local et Google Cloud Storage (GCS).
"""

import json
import logging
import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, TYPE_CHECKING

# Suppression de l'import thread_pool_manager pour les méthodes synchrones
# from .thread_pool_manager import get_thread_pool_manager

# Approche recommandée : imports séparés avec gestion d'erreur simple
try:
    # noinspection PyUnresolvedReferences
    from google.cloud import storage as gcs_storage

    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False
    # Pour TYPE_CHECKING, on peut importer pour l'analyse statique
    if TYPE_CHECKING:
        # noinspection PyUnresolvedReferences
        from google.cloud import storage as gcs_storage


class StorageProvider(ABC):
    """Interface abstraite pour les fournisseurs de stockage."""

    @abstractmethod
    def save_content(self, content_id: str, data: Dict[str, Any]) -> str:
        """Sauvegarde un contenu et retourne son chemin de stockage."""
        pass

    @abstractmethod
    def save_attachment(
        self, content_id: str, attachment_id: str, file_name: str, data: bytes
    ) -> str:
        """Sauvegarde une pièce jointe et retourne son chemin de stockage."""
        pass

    @abstractmethod
    def get_content(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID."""
        pass

    @abstractmethod
    def get_attachment(
        self, content_id: str, attachment_id: str
    ) -> Optional[bytes]:
        """Récupère une pièce jointe par son ID."""
        pass

    @abstractmethod
    def list_contents(self) -> List[str]:
        """Liste tous les IDs de contenu stockés."""
        pass

    @abstractmethod
    def list_attachments(self, content_id: str) -> List[str]:
        """Liste tous les IDs de pièces jointes pour un contenu donné."""
        pass

    @abstractmethod
    def delete_content(self, content_id: str) -> bool:
        """Supprime un contenu par son ID. Retourne True si supprimé avec succès."""
        pass

    @abstractmethod
    def content_exists(self, content_id: str) -> bool:
        """Vérifie si un contenu existe par son ID."""
        pass

    @abstractmethod
    def attachment_exists(self, content_id: str, attachment_id: str) -> bool:
        """Vérifie si une pièce jointe existe par son ID."""
        pass

    @abstractmethod
    def delete_attachment(self, content_id: str, attachment_id: str) -> bool:
        """Supprime une pièce jointe par son ID. Retourne True si supprimée avec succès."""
        pass

    @abstractmethod
    def get_storage_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de stockage (nombre de contenus, pièces jointes, taille totale)."""
        pass


class FileSystemStorage(StorageProvider):
    """Fournisseur de stockage utilisant le système de fichiers local."""

    def __init__(self, base_dir: str = "output_data_dir"):
        """Initialise le stockage avec le répertoire de base spécifié."""
        self.base_dir = base_dir
        self.logger = logging.getLogger(__name__)

        # Créer les répertoires nécessaires
        os.makedirs(base_dir, exist_ok=True)
        os.makedirs(os.path.join(base_dir, "contents"), exist_ok=True)
        os.makedirs(os.path.join(base_dir, "attachments"), exist_ok=True)

    def save_content(self, content_id: str, data: Dict[str, Any]) -> str:
        """Sauvegarde un contenu au format JSON."""
        try:
            # Créer le chemin du fichier
            content_dir = os.path.join(self.base_dir, "contents")
            file_path = os.path.join(content_dir, f"{content_id}.json")

            # Sauvegarder les données au format JSON
            self._write_json_file(file_path, data)

            self.logger.info(f"Contenu {content_id} sauvegardé dans {file_path}")
            return file_path
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la sauvegarde du contenu {content_id}: {e}"
            )
            raise

    def save_attachment(
        self, content_id: str, attachment_id: str, file_name: str, data: bytes
    ) -> str:
        """Sauvegarde une pièce jointe."""
        try:
            # Créer le répertoire pour les pièces jointes du contenu
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            os.makedirs(attachment_dir, exist_ok=True)

            # Nettoyer le nom de fichier pour éviter les problèmes de chemin
            safe_filename = self._sanitize_filename(file_name)

            # Ajouter l'ID de la pièce jointe au nom du fichier pour éviter les collisions
            file_path = os.path.join(attachment_dir, f"{attachment_id}_{safe_filename}")

            # Sauvegarder les données binaires
            self._write_binary_file(file_path, data)

            self.logger.info(
                f"Pièce jointe {attachment_id} sauvegardée dans {file_path}"
            )
            return file_path
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la sauvegarde de la pièce jointe {attachment_id}: {e}"
            )
            raise

    def get_content(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID."""
        try:
            file_path = os.path.join(self.base_dir, "contents", f"{content_id}.json")
            if not os.path.exists(file_path):
                return None

            return self._read_json_file(file_path)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération du contenu {content_id}: {e}"
            )
            return None

    def get_attachment(
        self, content_id: str, attachment_id: str
    ) -> Optional[bytes]:
        """Récupère une pièce jointe par son ID."""
        try:
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            if not os.path.exists(attachment_dir):
                return None

            # Trouver le fichier correspondant à l'ID de la pièce jointe
            files = [
                f
                for f in os.listdir(attachment_dir)
                if f.startswith(f"{attachment_id}_")
            ]

            if not files:
                return None

            file_path = os.path.join(attachment_dir, files[0])
            return self._read_binary_file(file_path)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération de la pièce jointe {attachment_id}: {e}"
            )
            return None

    def list_contents(self) -> List[str]:
        """Liste tous les IDs de contenu stockés."""
        try:
            content_dir = os.path.join(self.base_dir, "contents")
            files = os.listdir(content_dir)
            return [f.replace(".json", "") for f in files if f.endswith(".json")]
        except Exception as e:
            self.logger.error(f"Erreur lors de la liste des contenus: {e}")
            return []

    def list_attachments(self, content_id: str) -> List[str]:
        """Liste tous les IDs de pièces jointes pour un contenu donné."""
        try:
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            if not os.path.exists(attachment_dir):
                return []

            files = os.listdir(attachment_dir)
            return [f.split("_")[0] for f in files]
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la liste des pièces jointes pour {content_id}: {e}"
            )
            return []

    def delete_content(self, content_id: str) -> bool:
        """Supprime un contenu par son ID."""
        try:
            file_path = os.path.join(self.base_dir, "contents", f"{content_id}.json")
            if os.path.exists(file_path):
                os.remove(file_path)
                self.logger.info(f"Contenu {content_id} supprimé")
                return True
            return False
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la suppression du contenu {content_id}: {e}"
            )
            return False

    def content_exists(self, content_id: str) -> bool:
        """Vérifie si un contenu existe par son ID."""
        try:
            file_path = os.path.join(self.base_dir, "contents", f"{content_id}.json")
            return os.path.exists(file_path)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la vérification d'existence du contenu {content_id}: {e}"
            )
            return False

    def attachment_exists(self, content_id: str, attachment_id: str) -> bool:
        """Vérifie si une pièce jointe existe par son ID."""
        try:
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            if not os.path.exists(attachment_dir):
                return False

            # Trouver le fichier correspondant à l'ID de la pièce jointe
            files = [
                f
                for f in os.listdir(attachment_dir)
                if f.startswith(f"{attachment_id}_")
            ]
            return len(files) > 0
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la vérification d'existence de la pièce jointe {attachment_id}: {e}"
            )
            return False

    def delete_attachment(self, content_id: str, attachment_id: str) -> bool:
        """Supprime une pièce jointe par son ID."""
        try:
            attachment_dir = os.path.join(self.base_dir, "attachments", content_id)
            if not os.path.exists(attachment_dir):
                return False

            # Trouver le fichier correspondant à l'ID de la pièce jointe
            files = [
                f
                for f in os.listdir(attachment_dir)
                if f.startswith(f"{attachment_id}_")
            ]

            if not files:
                return False

            # Supprimer le fichier
            file_path = os.path.join(attachment_dir, files[0])
            os.remove(file_path)
            self.logger.info(f"Pièce jointe {attachment_id} supprimée")
            return True
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la suppression de la pièce jointe {attachment_id}: {e}"
            )
            return False

    def get_storage_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de stockage."""
        try:
            stats = {"total_contents": 0, "total_attachments": 0, "total_size_bytes": 0}

            # Compter les contenus et calculer leur taille
            content_dir = os.path.join(self.base_dir, "contents")
            if os.path.exists(content_dir):
                content_files = [f for f in os.listdir(content_dir) if f.endswith(".json")]
                stats["total_contents"] = len(content_files)

                # Calculer la taille des fichiers de contenu
                for file_name in content_files:
                    file_path = os.path.join(content_dir, file_name)
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    stats["total_size_bytes"] += file_size

            # Compter les pièces jointes et calculer leur taille
            attachments_dir = os.path.join(self.base_dir, "attachments")
            if os.path.exists(attachments_dir):
                content_dirs = [
                    d
                    for d in os.listdir(attachments_dir)
                    if os.path.isdir(os.path.join(attachments_dir, d))
                ]

                for content_dir_name in content_dirs:
                    content_attachment_dir = os.path.join(
                        attachments_dir, content_dir_name
                    )
                    attachment_files = os.listdir(content_attachment_dir) if os.path.exists(content_attachment_dir) else []
                    stats["total_attachments"] += len(attachment_files)

                    # Calculer la taille des fichiers de pièces jointes
                    for file_name in attachment_files:
                        file_path = os.path.join(content_attachment_dir, file_name)
                        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                        stats["total_size_bytes"] += file_size

            return stats
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération des statistiques de stockage: {e}"
            )
            return {"total_contents": 0, "total_attachments": 0, "total_size_bytes": 0}

    def _write_json_file(self, file_path: str, data: Dict[str, Any]) -> None:
        """Écrit des données JSON dans un fichier."""
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def _read_json_file(self, file_path: str) -> Dict[str, Any]:
        """Lit des données JSON depuis un fichier."""
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)

    def _write_binary_file(self, file_path: str, data: bytes) -> None:
        """Écrit des données binaires dans un fichier."""
        with open(file_path, "wb") as f:
            f.write(data)

    def _read_binary_file(self, file_path: str) -> bytes:
        """Lit des données binaires depuis un fichier."""
        with open(file_path, "rb") as f:
            return f.read()

    def _sanitize_filename(self, filename: str) -> str:
        """Nettoie un nom de fichier pour éviter les problèmes de chemin."""
        # Remplacer les caractères problématiques
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, "_")
        return filename


class GCSStorage(StorageProvider):
    """Fournisseur de stockage utilisant Google Cloud Storage."""

    def __init__(self, bucket_name: str, base_prefix: str = "confluence_rag"):
        """Initialise le stockage avec le bucket et le préfixe spécifiés."""
        if not GCS_AVAILABLE:
            raise ImportError(
                "Le module google-cloud-storage n'est pas installé. "
                "Installez-le avec 'pip install google-cloud-storage'"
            )

        self.bucket_name = bucket_name
        self.base_prefix = base_prefix
        self.logger = logging.getLogger(__name__)

        # Initialiser le client GCS - ici on sait que gcs_storage est disponible
        self.client = gcs_storage.Client()
        self.bucket = self.client.bucket(bucket_name)

        # Vérifier que le bucket existe
        if not self.bucket.exists():
            self.logger.warning(
                f"Le bucket {bucket_name} n'existe pas. Tentative de création..."
            )
            self.bucket.create()

    def save_content(self, content_id: str, data: Dict[str, Any]) -> str:
        """Sauvegarde un contenu au format JSON dans GCS."""
        try:
            # Créer le chemin de l'objet
            object_path = f"{self.base_prefix}/contents/{content_id}.json"
            blob = self.bucket.blob(object_path)

            # Convertir les données en JSON
            json_data = json.dumps(data, ensure_ascii=False, indent=2, default=str)

            # Sauvegarder les données
            blob.upload_from_string(json_data, content_type="application/json")

            self.logger.info(
                f"Contenu {content_id} sauvegardé dans gs://{self.bucket_name}/{object_path}"
            )
            return f"gs://{self.bucket_name}/{object_path}"
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la sauvegarde du contenu {content_id} dans GCS: {e}"
            )
            raise

    def save_attachment(
        self, content_id: str, attachment_id: str, file_name: str, data: bytes
    ) -> str:
        """Sauvegarde une pièce jointe dans GCS."""
        try:
            # Nettoyer le nom de fichier
            safe_filename = self._sanitize_filename(file_name)

            # Créer le chemin de l'objet
            object_path = f"{self.base_prefix}/attachments/{content_id}/{attachment_id}_{safe_filename}"
            blob = self.bucket.blob(object_path)

            # Déterminer le type de contenu
            content_type = self._guess_content_type(file_name)

            # Sauvegarder les données
            blob.upload_from_string(data, content_type=content_type)

            self.logger.info(
                f"Pièce jointe {attachment_id} sauvegardée dans gs://{self.bucket_name}/{object_path}"
            )
            return f"gs://{self.bucket_name}/{object_path}"
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la sauvegarde de la pièce jointe {attachment_id} dans GCS: {e}"
            )
            raise

    def get_content(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Récupère un contenu par son ID depuis GCS."""
        try:
            object_path = f"{self.base_prefix}/contents/{content_id}.json"
            blob = self.bucket.blob(object_path)

            # Vérifier si l'objet existe
            if not blob.exists():
                return None

            # Récupérer les données
            json_data = blob.download_as_text()
            return json.loads(json_data)
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération du contenu {content_id} depuis GCS: {e}"
            )
            return None

    def get_attachment(
        self, content_id: str, attachment_id: str
    ) -> Optional[bytes]:
        """Récupère une pièce jointe par son ID depuis GCS."""
        try:
            # Lister les objets pour trouver la pièce jointe
            prefix = f"{self.base_prefix}/attachments/{content_id}/{attachment_id}_"
            blobs = list(self.bucket.list_blobs(prefix=prefix, max_results=1))

            if not blobs:
                return None

            # Récupérer les données
            return blobs[0].download_as_bytes()
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération de la pièce jointe {attachment_id} depuis GCS: {e}"
            )
            return None

    def list_contents(self) -> List[str]:
        """Liste tous les IDs de contenu stockés dans GCS."""
        try:
            prefix = f"{self.base_prefix}/contents/"
            blobs = list(self.bucket.list_blobs(prefix=prefix))

            content_ids = []
            for blob in blobs:
                # Extraire l'ID du contenu du nom de l'objet
                name = blob.name.replace(prefix, "").replace(".json", "")
                if name:
                    content_ids.append(name)

            return content_ids
        except Exception as e:
            self.logger.error(f"Erreur lors de la liste des contenus depuis GCS: {e}")
            return []

    def list_attachments(self, content_id: str) -> List[str]:
        """Liste tous les IDs de pièces jointes pour un contenu donné dans GCS."""
        try:
            prefix = f"{self.base_prefix}/attachments/{content_id}/"
            blobs = list(self.bucket.list_blobs(prefix=prefix))

            attachment_ids = []
            for blob in blobs:
                # Extraire l'ID de la pièce jointe du nom de l'objet
                name = blob.name.replace(prefix, "")
                if name:
                    attachment_id = name.split("_")[0]
                    if attachment_id not in attachment_ids:
                        attachment_ids.append(attachment_id)

            return attachment_ids
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la liste des pièces jointes pour {content_id} depuis GCS: {e}"
            )
            return []

    def delete_content(self, content_id: str) -> bool:
        """Supprime un contenu par son ID depuis GCS."""
        try:
            object_path = f"{self.base_prefix}/contents/{content_id}.json"
            blob = self.bucket.blob(object_path)

            if blob.exists():
                blob.delete()
                self.logger.info(f"Contenu {content_id} supprimé de GCS")
                return True
            return False
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la suppression du contenu {content_id} depuis GCS: {e}"
            )
            return False

    def content_exists(self, content_id: str) -> bool:
        """Vérifie si un contenu existe par son ID dans GCS."""
        try:
            object_path = f"{self.base_prefix}/contents/{content_id}.json"
            blob = self.bucket.blob(object_path)

            return blob.exists()
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la vérification d'existence du contenu {content_id} dans GCS: {e}"
            )
            return False

    def attachment_exists(self, content_id: str, attachment_id: str) -> bool:
        """Vérifie si une pièce jointe existe par son ID dans GCS."""
        try:
            prefix = f"{self.base_prefix}/attachments/{content_id}/{attachment_id}_"
            blobs = list(self.bucket.list_blobs(prefix=prefix, max_results=1))
            return len(blobs) > 0
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la vérification d'existence de la pièce jointe {attachment_id} dans GCS: {e}"
            )
            return False

    def delete_attachment(self, content_id: str, attachment_id: str) -> bool:
        """Supprime une pièce jointe par son ID depuis GCS."""
        try:
            prefix = f"{self.base_prefix}/attachments/{content_id}/{attachment_id}_"
            blobs = list(self.bucket.list_blobs(prefix=prefix, max_results=1))

            if not blobs:
                return False

            # Supprimer le blob
            blobs[0].delete()
            self.logger.info(f"Pièce jointe {attachment_id} supprimée de GCS")
            return True
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la suppression de la pièce jointe {attachment_id} depuis GCS: {e}"
            )
            return False

    def get_storage_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de stockage depuis GCS."""
        try:
            stats = {"total_contents": 0, "total_attachments": 0, "total_size_bytes": 0}

            # Compter les contenus et calculer leur taille
            content_prefix = f"{self.base_prefix}/contents/"
            content_blobs = list(self.bucket.list_blobs(prefix=content_prefix))
            stats["total_contents"] = len(content_blobs)

            for blob in content_blobs:
                stats["total_size_bytes"] += blob.size or 0

            # Compter les pièces jointes et calculer leur taille
            attachments_prefix = f"{self.base_prefix}/attachments/"
            attachment_blobs = list(self.bucket.list_blobs(prefix=attachments_prefix))
            stats["total_attachments"] = len(attachment_blobs)

            for blob in attachment_blobs:
                stats["total_size_bytes"] += blob.size or 0

            return stats
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération des statistiques de stockage depuis GCS: {e}"
            )
            return {"total_contents": 0, "total_attachments": 0, "total_size_bytes": 0}

    def _sanitize_filename(self, filename: str) -> str:
        """Nettoie un nom de fichier pour GCS."""
        # Remplacer les caractères problématiques
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, "_")
        return filename

    def _guess_content_type(self, filename: str) -> str:
        """Devine le type MIME à partir du nom de fichier."""
        ext = os.path.splitext(filename.lower())[1]
        content_types = {
            ".pdf": "application/pdf",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ".txt": "text/plain",
            ".html": "text/html",
            ".htm": "text/html",
            ".md": "text/markdown",
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".png": "image/png",
            ".gif": "image/gif",
        }
        return content_types.get(ext, "application/octet-stream")


def get_storage_provider(storage_type: str, **kwargs) -> StorageProvider:
    """Crée et retourne un fournisseur de stockage selon le type spécifié."""
    if storage_type.lower() == "filesystem":
        base_dir = kwargs.get("base_dir", "output_data_dir")
        return FileSystemStorage(base_dir=base_dir)
    elif storage_type.lower() == "gcs":
        bucket_name = kwargs.get("bucket_name")
        if not bucket_name:
            raise ValueError(
                "Le paramètre 'bucket_name' est requis pour le stockage GCS"
            )
        base_prefix = kwargs.get("base_prefix", "confluence_rag")
        return GCSStorage(bucket_name=bucket_name, base_prefix=base_prefix)
    else:
        raise ValueError(f"Type de stockage non supporté: {storage_type}")
