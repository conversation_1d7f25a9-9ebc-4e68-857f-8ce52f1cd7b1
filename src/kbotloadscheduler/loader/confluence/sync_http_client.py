#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Synchronous HTTP request and response handling for the Confluence client.
"""

import logging
from dataclasses import dataclass
from typing import Dict, Any, Optional

import requests

from .constants import APIConstants
from .exceptions import (
    AuthenticationError,
    APIError,
    ContentNotFoundError,
    RateLimitExceededError,
)
from .security import SecurityUtils


@dataclass
class SyncRequestContext:
    """Context information for synchronous API requests."""

    method: str
    endpoint: str
    service_name: str
    download_mode: bool = False
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None


class SyncResponseProcessor:
    """Handles synchronous response processing and validation."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def process_response(
        self, response: requests.Response, context: SyncRequestContext
    ) -> Any:
        """Process HTTP response based on context."""
        self.logger.debug(
            f"Response status from {context.endpoint}: {response.status_code}, "
            f"Content-Type: {response.headers.get('Content-Type')}"
        )

        if response.status_code == 200:
            return self._handle_success_response(response, context)
        else:
            self._handle_error_response(response, context)

    def _handle_success_response(
        self, response: requests.Response, context: SyncRequestContext
    ) -> Any:
        """Handle successful HTTP responses."""
        if context.download_mode:
            return response.content

        content_type = response.headers.get("Content-Type", "").lower()
        if APIConstants.JSON_CONTENT_TYPE in content_type:
            return response.json()
        else:
            self.logger.debug(
                f"Response from {context.endpoint} is not JSON "
                f"(Content-Type: {content_type}). Reading as text."
            )
            return response.text

    def _handle_error_response(
        self, response: requests.Response, context: SyncRequestContext
    ) -> None:
        """Handle HTTP error responses."""
        error_text = response.text
        sanitized_error = SecurityUtils.sanitize_error_message(error_text)

        if response.status_code == 401:
            self.logger.error(f"Erreur d'authentification (401): {sanitized_error}")
            raise AuthenticationError(f"Erreur d'authentification: {sanitized_error}")
        elif response.status_code == 404:
            self.logger.warning(f"Ressource non trouvée (404): {context.endpoint}")
            raise ContentNotFoundError(f"Ressource non trouvée: {context.endpoint}")
        elif response.status_code == 429:
            retry_after = self._parse_retry_after(response.headers.get("Retry-After"))
            self.logger.warning(
                f"Limite de taux (429) dépassée. Retry-After: {retry_after}s"
            )
            raise RateLimitExceededError(
                "Limite de taux d'appels API dépassée", retry_after=retry_after
            )
        else:
            self.logger.error(f"Erreur API {response.status_code}: {sanitized_error}")
            raise APIError(
                f"Erreur API: {response.status_code} - {sanitized_error}",
                status_code=response.status_code,
            )

    def _parse_retry_after(self, retry_after_header: Optional[str]) -> int:
        """Parse Retry-After header value."""
        if not retry_after_header:
            return APIConstants.DEFAULT_RETRY_AFTER

        try:
            return int(retry_after_header)
        except ValueError:
            self.logger.warning(
                f"Invalid Retry-After header: {retry_after_header}. "
                f"Defaulting to {APIConstants.DEFAULT_RETRY_AFTER}s."
            )
            return APIConstants.DEFAULT_RETRY_AFTER


class SyncHTTPClient:
    """Synchronous HTTP client for making requests."""

    def __init__(self, timeout: int = APIConstants.DEFAULT_TIMEOUT):
        self.session = requests.Session()
        self.timeout = timeout
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        auth: Optional[tuple] = None,
        params: Optional[Dict[str, Any]] = None,
        json: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        **kwargs,
    ) -> requests.Response:
        """Make a synchronous HTTP request."""
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                auth=auth,
                params=params,
                json=json,
                data=data,
                timeout=self.timeout,
                **kwargs,
            )
            return response
        except requests.exceptions.Timeout as e:
            self.logger.error(f"Request timeout for {url}: {e}")
            raise APIError(f"Request timeout: {e}")
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"Connection error for {url}: {e}")
            raise APIError(f"Connection error: {e}")
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request error for {url}: {e}")
            raise APIError(f"Request error: {e}")

    def close(self):
        """Close the session."""
        self.session.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
