#!/usr/bin/env python3
import sys
import os

sys.path.insert(0, 'src')

os.environ['CONFLUENCE_URL'] = 'https://test-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'TEST'

try:
    print("Testing individual imports...")
    
    print("1. Importing ConfluenceLoader...")
    from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
    print("✓ ConfluenceLoader imported")
    
    print("2. Creating simple config...")
    class SimpleConfig:
        pass
    config = SimpleConfig()
    
    print("3. Creating ConfluenceLoader instance...")
    loader = ConfluenceLoader(config)
    print("✓ ConfluenceLoader created successfully!")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
