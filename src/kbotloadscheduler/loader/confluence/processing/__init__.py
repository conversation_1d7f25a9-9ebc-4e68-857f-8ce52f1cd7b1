#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Confluence  Processing Package

This package provides modular, focused components for processing Confluence content:

- enums: Processing status and media type enumerations
- document_extractors: Text extraction from various document formats
- drawio_processor: Specialized processing for draw.io diagrams
- sync_attachment_processor: Synchronous attachment download and processing
- content_chunker: Text chunking functionality
- sync_content_retriever: Synchronous content retrieval and orchestration

This is the new modular architecture that replaces the monolithic processing.py file.
"""

from .content_chunker import ContentChunker
from .document_extractors import DocumentExtractor
from .drawio_processor import DrawIOProcessor

# Import all public classes and enums
from .enums import ProcessingStatus, MediaType, ExtractionResult, DrawIOMetadata
from .sync_attachment_processor import SyncAttachmentProcessor


# Public API
__all__ = [
    # Enums and data classes
    "ProcessingStatus",
    "MediaType",
    "ExtractionResult",
    "DrawIOMetadata",
    # Processing components
    "DocumentExtractor",
    "DrawIOProcessor",
    "SyncAttachmentProcessor",
    "ContentChunker",
    "SyncContentRetriever",
]

# Version info
__version__ = "2.0.0"
__author__ = "CProfiling Team"
__description__ = "Modular processing components for Confluence documents"
