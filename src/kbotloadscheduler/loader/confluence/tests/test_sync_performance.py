#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests de performance pour l'orchestrateur Confluence synchrone.
"""

import shutil
import tempfile
import time
import unittest
from unittest.mock import Mock, patch

from pydantic import SecretStr

from .fixtures.test_data_factory import DataFactory, create_mock_confluence_client
from ..config import (
    ConfluenceConfig,
    SearchCriteria,
    StorageConfig,
    ProcessingConfig,
    ThreadPoolConfig,
)
from ..orchestrator import SyncOrchestrator


class TestSyncPerformance(unittest.TestCase):
    """Tests de performance pour l'orchestrateur synchrone."""

    def setUp(self):
        """Configuration des tests."""
        # Répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()

        # Configuration Confluence
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_pat_token"),
            default_space_key="TEST",
        )

        # Configuration de stockage
        self.storage_config = StorageConfig(type="filesystem", base_dir=self.temp_dir)

        # Créer des données de test plus importantes pour les tests de performance
        self.data_factory = DataFactory("TEST", "Test Space")
        self.large_test_pages = self._create_large_dataset(50)  # 50 pages
        self.large_test_attachments = self._create_large_attachments(
            100
        )  # 100 pièces jointes

    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def _create_large_dataset(self, num_pages: int):
        """Crée un jeu de données plus important pour les tests de performance."""
        pages = []
        for i in range(num_pages):
            page = self.data_factory.create_page(
                f"page_{i}",
                f"Test Page {i}",
                f"<p>Content for page {i} with some text to process.</p>" * 10,
                # Contenu plus long
            )
            pages.append(page)
        return pages

    def _create_large_attachments(self, num_attachments: int):
        """Crée un grand nombre de pièces jointes pour les tests."""
        attachments = {}
        for i in range(0, num_attachments, 2):  # 2 pièces jointes par page
            page_id = f"page_{i // 2}"
            if page_id not in attachments:
                attachments[page_id] = []

            # Ajouter 2 pièces jointes par page
            attachments[page_id].extend(
                [
                    self.data_factory.create_attachment(
                        f"att_{i}", f"document_{i}.pdf", page_id
                    ),
                    self.data_factory.create_attachment(
                        f"att_{i + 1}", f"image_{i + 1}.png", page_id
                    ),
                ]
            )
        return attachments

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_performance_baseline_small_dataset(self, mock_client_class):
        """Test de performance avec un petit jeu de données (baseline)."""
        # Configuration pour petit dataset
        criteria = SearchCriteria(
            spaces=["TEST"], max_results=10, include_attachments=True
        )

        processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=100,
            max_parallel_downloads=2,
            max_thread_workers=2,
        )

        # Créer un client mocké avec un petit dataset
        small_pages = self.large_test_pages[:10]
        small_attachments = {
            k: v
            for k, v in self.large_test_attachments.items()
            if k in [p.id for p in small_pages]
        }

        mock_client = create_mock_confluence_client(small_pages, small_attachments)
        mock_client_class.return_value = mock_client

        orchestrator = SyncOrchestrator(
            self.config, criteria, self.storage_config, processing_config
        )

        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(
            return_value={"sync_id": "test_sync"}
        )

        # Mesurer le temps d'exécution
        start_time = time.time()
        result = orchestrator.run()
        execution_time = time.time() - start_time

        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)
        self.assertLess(execution_time, 30)  # Devrait prendre moins de 30 secondes

        print(f"Performance baseline (10 pages): {execution_time:.2f}s")
        print(f"Pages traitées: {result['total_content_items']}")
        print(f"Pièces jointes: {result['total_attachments']}")

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_performance_medium_dataset(self, mock_client_class):
        """Test de performance avec un jeu de données moyen."""
        # Configuration pour dataset moyen
        criteria = SearchCriteria(
            spaces=["TEST"], max_results=25, include_attachments=True
        )

        processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=100,
            max_parallel_downloads=3,
            max_thread_workers=3,
        )

        # Créer un client mocké avec un dataset moyen
        medium_pages = self.large_test_pages[:25]
        medium_attachments = {
            k: v
            for k, v in self.large_test_attachments.items()
            if k in [p.id for p in medium_pages]
        }

        mock_client = create_mock_confluence_client(medium_pages, medium_attachments)
        mock_client_class.return_value = mock_client

        orchestrator = SyncOrchestrator(
            self.config, criteria, self.storage_config, processing_config
        )

        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(
            return_value={"sync_id": "test_sync"}
        )

        # Mesurer le temps d'exécution
        start_time = time.time()
        result = orchestrator.run()
        execution_time = time.time() - start_time

        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)
        self.assertLess(execution_time, 60)  # Devrait prendre moins de 60 secondes

        print(f"Performance medium (25 pages): {execution_time:.2f}s")
        print(f"Pages traitées: {result['total_content_items']}")
        print(f"Pièces jointes: {result['total_attachments']}")

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_performance_high_parallelism(self, mock_client_class):
        """Test de performance avec parallélisme élevé."""
        # Configuration haute performance
        criteria = SearchCriteria(
            spaces=["TEST"], max_results=20, include_attachments=True
        )

        processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=100,
            max_parallel_downloads=8,
            max_thread_workers=6,
            thread_pool_config=ThreadPoolConfig(
                io_thread_workers=10,
                document_processing_workers=6,
                api_thread_workers=8,
            ),
        )

        # Créer un client mocké
        hp_pages = self.large_test_pages[:20]
        hp_attachments = {
            k: v
            for k, v in self.large_test_attachments.items()
            if k in [p.id for p in hp_pages]
        }

        mock_client = create_mock_confluence_client(hp_pages, hp_attachments)
        mock_client_class.return_value = mock_client

        orchestrator = SyncOrchestrator(
            self.config, criteria, self.storage_config, processing_config
        )

        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(
            return_value={"sync_id": "test_sync"}
        )

        # Mesurer le temps d'exécution
        start_time = time.time()
        result = orchestrator.run()
        execution_time = time.time() - start_time

        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)

        print(
            f"Performance haute (20 pages, parallélisme élevé): {execution_time:.2f}s"
        )
        print(f"Pages traitées: {result['total_content_items']}")
        print(f"Pièces jointes: {result['total_attachments']}")

        # Vérifier les stats des pools de threads
        thread_stats = orchestrator.thread_pool_manager.get_pool_stats()
        print(f"Stats pools de threads: {thread_stats}")

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_performance_memory_usage(self, mock_client_class):
        """Test de l'utilisation mémoire."""
        import psutil
        import os

        # Configuration pour test mémoire
        criteria = SearchCriteria(
            spaces=["TEST"], max_results=30, include_attachments=True
        )

        processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=100,
            max_parallel_downloads=4,
            max_thread_workers=4,
        )

        # Créer un client mocké
        memory_pages = self.large_test_pages[:30]
        memory_attachments = {
            k: v
            for k, v in self.large_test_attachments.items()
            if k in [p.id for p in memory_pages]
        }

        mock_client = create_mock_confluence_client(memory_pages, memory_attachments)
        mock_client_class.return_value = mock_client

        # Mesurer l'utilisation mémoire avant
        process = psutil.Process(os.getpid())
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        orchestrator = SyncOrchestrator(
            self.config, criteria, self.storage_config, processing_config
        )

        # Mock du change tracker
        orchestrator.change_tracker.has_content_changed = Mock(return_value=True)
        orchestrator.change_tracker.record_content_change = Mock()
        orchestrator.change_tracker.record_sync = Mock(
            return_value={"sync_id": "test_sync"}
        )

        # Exécuter l'orchestration
        result = orchestrator.run()

        # Mesurer l'utilisation mémoire après
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = memory_after - memory_before

        # Vérifications
        self.assertIsNotNone(result)
        self.assertGreater(result["total_content_items"], 0)
        self.assertLess(memory_increase, 500)  # Augmentation < 500MB

        print("Utilisation mémoire:")
        print("  Avant: {memory_before:.2f} MB")
        print("  Après: {memory_after:.2f} MB")
        print("  Augmentation: {memory_increase:.2f} MB")

    def test_thread_pool_efficiency(self):
        """Test de l'efficacité des pools de threads."""
        with patch(
            "src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient"
        ):
            # Configuration avec différents niveaux de parallélisme
            configs = [
                {"workers": 2, "downloads": 2, "io": 3},
                {"workers": 4, "downloads": 4, "io": 6},
                {"workers": 6, "downloads": 6, "io": 8},
            ]

            results = []

            for config in configs:
                processing_config = ProcessingConfig(
                    max_thread_workers=config["workers"],
                    max_parallel_downloads=config["downloads"],
                    thread_pool_config=ThreadPoolConfig(
                        io_thread_workers=config["io"],
                        document_processing_workers=config["workers"],
                        api_thread_workers=config["workers"],
                    ),
                )

                orchestrator = SyncOrchestrator(
                    self.config,
                    SearchCriteria(spaces=["TEST"], max_results=5),
                    self.storage_config,
                    processing_config,
                )

                # Mesurer le temps de création et d'initialisation
                start_time = time.time()
                thread_stats = orchestrator.thread_pool_manager.get_pool_stats()
                init_time = time.time() - start_time

                results.append(
                    {
                        "config": config,
                        "init_time": init_time,
                        "thread_stats": thread_stats,
                    }
                )

            # Vérifier que l'initialisation est rapide
            for result in results:
                self.assertLess(result["init_time"], 1.0)  # < 1 seconde
                print(f"Config {result['config']}: init {result['init_time']:.3f}s")


if __name__ == "__main__":
    # Exécuter les tests avec verbosité pour voir les résultats de performance
    unittest.main(verbosity=2)
