#!/usr/bin/env python3
"""
Debug script to test the failing confluence test
"""

import sys
import os
sys.path.insert(0, 'src')
sys.path.insert(0, 'tests')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

# Import required modules
from unittest.mock import MagicMock, patch
import json
from datetime import datetime, timezone

try:
    # Import the test dependencies
    from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
    from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
    from testutils.mock_confluence import (
        MockConfluence,
        create_mock_confluence_client,
        setup_sample_confluence_data,
    )
    
    print("✓ All imports successful")
    
    # Create a mock config
    mock_config_with_secret = MagicMock()
    mock_config_with_secret.get_confluence_credentials.return_value = {
        "pat_token": "mock_confluence_pat_token"
    }
    print("✓ Mock config created")
    
    # Create a confluence source
    config = {
        "spaces": ["DOCS", "TECH"],
        "max_results": 100,
        "include_attachments": True,
        "content_types": ["page", "blogpost"],
        "labels": ["public"]
    }
    
    confluence_source = SourceBean(
        id=1,
        code="mock_confluence_test",
        label="Mock Confluence Test",
        src_type="confluence",
        configuration=json.dumps(config),
        last_load_time=int(datetime.now(timezone.utc).timestamp()),
        load_interval=24,
        domain_code="mock_domain",
        perimeter_code="mock_test",
        force_embedding=False
    )
    print("✓ Confluence source created")
    
    # Create mock confluence
    mock_confluence = MockConfluence("https://mock-confluence.example.com")
    mock_confluence = setup_sample_confluence_data(mock_confluence)
    print("✓ Mock confluence created")
    
    # Test the loader creation
    loader = ConfluenceLoader(mock_config_with_secret)
    print("✓ Loader created successfully")
    
    # Test document
    test_document = DocumentBean(
        id="mock_domain/mock_confluence_test/page_123456",
        name="API Guide",
        path="https://mock-confluence.example.com/display/DOCS/API+Guide",
        modification_time=datetime.now(timezone.utc)
    )
    print("✓ Test document created")
    
    # Try to call get_document with full mocking
    output_path = "gs://mock-confluence-bucket/confluence-data"
    
    # Mock all the configuration classes and orchestrator
    with patch('kbotloadscheduler.loader.confluence.loader.ConfluenceConfig') as mock_confluence_config_class, \
         patch('kbotloadscheduler.loader.confluence.loader.StorageConfig') as mock_storage_config_class, \
         patch('kbotloadscheduler.loader.confluence.loader.ProcessingConfig') as mock_processing_config_class, \
         patch('kbotloadscheduler.loader.confluence.loader.SyncOrchestrator') as mock_orchestrator_class:
        
        # Setup mock returns
        mock_confluence_config = MagicMock()
        mock_storage_config = MagicMock()
        mock_processing_config = MagicMock()
        mock_processing_config.enable_change_tracking = False
        
        mock_confluence_config_class.return_value = mock_confluence_config
        mock_storage_config_class.return_value = mock_storage_config
        mock_processing_config_class.from_env.return_value = mock_processing_config
        
        # Setup orchestrator mock
        mock_orchestrator = MagicMock()
        mock_sync_result = {
            "sync_id": "mock-sync-789",
            "total_content": 4,
            "processed_content": 4,
            "skipped_content": 0,
            "failed_content": 0,
            "total_attachments": 2,
            "processed_attachments": 2,
            "start_time": "2023-05-27T15:00:00Z",
            "end_time": "2023-05-27T15:02:30Z",
            "duration_seconds": 150,
            "spaces_processed": ["DOCS", "TECH"],
            "mock_confluence_stats": mock_confluence.get_stats()
        }
        mock_orchestrator.run.return_value = mock_sync_result
        mock_orchestrator_class.return_value = mock_orchestrator
        
        print("✓ All mocks set up")
        
        # Call get_document
        metadata = loader.get_document(confluence_source, test_document, output_path)
        
        print("✅ get_document call successful!")
        print(f"Metadata keys: {list(metadata.keys())}")
        
        # Verify metadata
        assert metadata is not None
        assert metadata["document_id"] == test_document.id
        assert metadata["document_name"] == test_document.name
        assert metadata["location"] == output_path
        assert metadata["domain"] == confluence_source.domain_code
        assert metadata["source"] == confluence_source.code
        assert metadata["source_type"] == "confluence"
        
        print("✅ All assertions passed!")
        
except Exception as e:
    import traceback
    print(f"❌ Error: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
