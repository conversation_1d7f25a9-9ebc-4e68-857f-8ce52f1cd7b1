#!/usr/bin/env python3
"""
Test script to verify that SyncContentRetriever can be imported successfully.
"""

import sys
import os

# Add src to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_sync_content_retriever_import():
    """Test importing SyncContentRetriever"""
    try:
        print("Testing SyncContentRetriever import...")
        from src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
        print("✅ SUCCESS: SyncContentRetriever imported successfully!")
        
        # Test class structure
        print(f"Class: {SyncContentRetriever}")
        print(f"Module: {SyncContentRetriever.__module__}")
        
        # Check for required methods
        required_methods = [
            'search_and_retrieve',
            'retrieve_content', 
            'get_retrieval_stats',
            'reset_stats'
        ]
        
        for method in required_methods:
            if hasattr(SyncContentRetriever, method):
                print(f"✅ Method '{method}' found")
            else:
                print(f"❌ Method '{method}' missing")
                return False
                
        print("✅ All required methods found!")
        return True
        
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ OTHER ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_sync_content_retriever_import()
    if success:
        print("\n🎉 ImportError fix SUCCESSFUL! SyncContentRetriever is now working.")
        sys.exit(0)
    else:
        print("\n💥 ImportError fix FAILED!")
        sys.exit(1)
