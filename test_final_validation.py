#!/usr/bin/env python3

"""
Comprehensive test to validate all fixes made to the content retriever.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from unittest.mock import Mock, MagicMock, patch
import logging

def test_imports():
    """Test that all imports work correctly."""
    try:
        from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
        from kbotloadscheduler.loader.confluence.config import ProcessingConfig, SearchCriteria
        from kbotloadscheduler.loader.confluence.models import ContentItem, SpaceInfo, UserInfo
        print("✓ All imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_mock_int_comparison_fix():
    """Test the fix for Mock/int comparison in _retrieve_children_recursively."""
    print("\nTesting Mock/int comparison fix...")
    
    try:
        from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import Sync<PERSON>ontentRetriever
        from kbotloadscheduler.loader.confluence.config import ProcessingConfig
        
        # Create mock config with Mock max_children_depth
        mock_config = Mock(spec=ProcessingConfig)
        mock_config.max_children_depth = Mock()  # This is a Mock, not an int
        mock_config.max_thread_workers = 4
        mock_config.page_limit = 100
        
        mock_client = Mock()
        
        # Mock the SyncAttachmentProcessor 
        with patch('kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor.get_processing_stats.return_value = {}
            mock_processor.reset_stats.return_value = None
            mock_processor_class.return_value = mock_processor
            
            # This should work without TypeError
            retriever = SyncContentRetriever(mock_client, mock_config)
            
            # Try to access the fixed comparison logic
            parent_content = Mock()
            parent_content.children = []
            
            # This should not raise TypeError anymore
            result = retriever._retrieve_children_recursively(
                parent_content=parent_content,
                current_depth=0,  # int
                processed_content_ids=set(),
                processing_stats={'pages_processed': 0}
            )
            
            print("✓ Mock/int comparison works - no TypeError raised")
            return True
            
    except TypeError as e:
        if "'<=' not supported between instances of" in str(e):
            print(f"✗ Mock/int comparison still fails: {e}")
            return False
        else:
            # Other TypeErrors might be expected
            print(f"✓ Mock/int comparison fixed - different TypeError: {e}")
            return True
    except Exception as e:
        # Other exceptions are fine, we just want to avoid the Mock/int TypeError
        print(f"✓ Mock/int comparison fixed - other exception: {type(e).__name__}: {e}")
        return True

def test_proper_mock_setup():
    """Test that mocks are properly set up in initialization."""
    print("\nTesting proper mock setup...")
    
    try:
        from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
        from kbotloadscheduler.loader.confluence.config import ProcessingConfig
        
        mock_config = Mock(spec=ProcessingConfig)
        mock_config.max_children_depth = 3
        mock_config.max_thread_workers = 4
        mock_config.page_limit = 100
        mock_config.chunk_size = 1000
        mock_config.overlap_size = 200
        
        mock_client = Mock()
        
        # Proper mock setup as in our fixed tests
        with patch('kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor.get_processing_stats.return_value = {'attachments_processed': 0}
            mock_processor.reset_stats.return_value = None
            mock_processor_class.return_value = mock_processor
            
            # This should work now
            retriever = SyncContentRetriever(mock_client, mock_config)
            
            # Test basic functionality
            stats = retriever.get_retrieval_stats()
            expected_keys = ['content_retrieved', 'attachments_processed', 'errors']
            
            if all(key in stats for key in expected_keys):
                print("✓ Proper mock setup works - stats accessible")
                return True
            else:
                print(f"✗ Missing stats keys: {expected_keys} vs {list(stats.keys())}")
                return False
                
    except Exception as e:
        print(f"✗ Mock setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stats_aggregation():
    """Test the stats aggregation functionality."""
    print("\nTesting stats aggregation...")
    
    try:
        from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
        
        mock_config = Mock()
        mock_config.max_children_depth = 3
        mock_config.max_thread_workers = 4
        mock_config.page_limit = 100
        
        mock_client = Mock()
        
        with patch('kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor.get_processing_stats.return_value = {}
            mock_processor.reset_stats.return_value = None
            mock_processor_class.return_value = mock_processor
            
            retriever = SyncContentRetriever(mock_client, mock_config)
            
            # Test stats aggregation
            stats1 = {'pages_processed': 5, 'errors': 1}
            stats2 = {'pages_processed': 3, 'errors': 0, 'attachments': 2}
            
            retriever._aggregate_stats(stats1, stats2)
            
            # Check the results
            expected = {'pages_processed': 8, 'errors': 1, 'attachments': 2}
            if stats1 == expected:
                print("✓ Stats aggregation works correctly")
                return True
            else:
                print(f"✗ Stats aggregation failed: expected {expected}, got {stats1}")
                return False
                
    except Exception as e:
        print(f"✗ Stats aggregation failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Validating fixes for content retriever...")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Mock/int comparison fix", test_mock_int_comparison_fix),
        ("Proper mock setup", test_proper_mock_setup),
        ("Stats aggregation", test_stats_aggregation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    for i, (test_name, _) in enumerate(tests):
        status = "✓ PASS" if results[i] else "✗ FAIL"
        print(f"  {test_name}: {status}")
    
    all_passed = all(results)
    print(f"\nOverall: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
