#!/usr/bin/env python3

import sys
import os

# Add the src and tests directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
tests_path = os.path.join(current_dir, 'tests')

sys.path.insert(0, src_path)
sys.path.insert(0, tests_path)

def run_first_test():
    """Run test_get_document_with_mock_confluence_and_gcs without pytest"""
    try:
        print("Importing test modules...")
        
        # Import necessary modules
        from unittest.mock import MagicMock, patch
        from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        from testutils.mock_confluence import (
            MockConfluence,
            create_mock_confluence_client,
            setup_sample_confluence_data,
        )
        
        print("✅ All imports successful")
        
        # Create test fixtures manually (mimicking what pytest would do)
        print("Creating test fixtures...")
        
        # Create mock_config_with_secret
        mock_config_with_secret = MagicMock()
        mock_config_with_secret.get_confluence_credentials.return_value = {
            "pat_token": "mock_confluence_pat_token"
        }
        
        # Create mock_confluence
        mock_confluence = MockConfluence("https://mock-confluence.example.com")
        mock_confluence = setup_sample_confluence_data(mock_confluence)
        
        # Create confluence_source
        confluence_source = SourceBean(
            id=1,
            code="confluence-test",
            label="Test Confluence Source",
            src_type="confluence",
            configuration='{"spaces": ["DOCS", "TECH"], "max_results": 100, "include_attachments": true, "content_types": ["page", "blogpost"], "labels": ["public"]}',
            last_load_time=0,
            load_interval=24
        )
        
        print("✅ Test fixtures created")
        
        # Now run the actual test logic
        print("Running test logic...")
        
        # Mock GCS storage
        with patch('kbotloadscheduler.loader.confluence.loader.GCSStorage') as mock_gcs_storage:
            # Configure mock GCS
            mock_storage_instance = MagicMock()
            mock_gcs_storage.return_value = mock_storage_instance
            mock_storage_instance.get_source_config.return_value = {"mock": "gcs_config"}
            
            # Mock confluence client dans le loader
            with patch.object(ConfluenceLoader, '_create_confluence_client') as mock_create_client:
                # Create the mock client
                mock_client = create_mock_confluence_client(mock_confluence, None)
                mock_create_client.return_value = mock_client
                
                # Create loader instance
                loader = ConfluenceLoader(mock_config_with_secret)
                
                # Execute the main test
                print("Calling get_document...")
                document = loader.get_document(confluence_source, "65537")  # API Guide ID from mock data
                
                # Assertions
                assert document is not None, "Document should not be None"
                assert isinstance(document, DocumentBean), f"Expected DocumentBean, got {type(document)}"
                assert document.id == "65537", f"Expected document ID 65537, got {document.id}"
                assert document.title == "API Guide", f"Expected title 'API Guide', got '{document.title}'"
                assert document.source_type == "confluence", f"Expected source_type 'confluence', got '{document.source_type}'"
                
                print("✅ All assertions passed")
                print(f"   - Document ID: {document.id}")
                print(f"   - Title: {document.title}")
                print(f"   - Source type: {document.source_type}")
                
                return True
                
    except Exception as e:
        print(f"❌ Test failed: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_second_test():
    """Run test_mock_confluence_client_integration without pytest"""
    try:
        print("\nRunning second test...")
        
        # Import necessary modules  
        from unittest.mock import MagicMock
        from testutils.mock_confluence import (
            MockConfluence,
            create_mock_confluence_client,
            setup_sample_confluence_data,
        )
        
        # Create mock_confluence
        mock_confluence = MockConfluence("https://mock-confluence.example.com")
        mock_confluence = setup_sample_confluence_data(mock_confluence)
        
        # Create the mock client
        mock_client = create_mock_confluence_client(mock_confluence, None)
        
        # Mock search criteria
        search_criteria = MagicMock()
        search_criteria.spaces = ["DOCS"]
        search_criteria.content_types = ["page"]
        search_criteria.max_results = 100
        search_criteria.labels = []
        search_criteria.title_contains = None
        search_criteria.last_modified_days = None
        
        # Handle async call
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            results = loop.run_until_complete(mock_client.search_content(search_criteria))
        finally:
            loop.close()
        
        # Assertions
        assert len(results) == 2, f"Expected 2 results, got {len(results)}"
        assert all(hasattr(item, 'id') for item in results), "All items should have 'id' attribute"
        assert all(hasattr(item, 'title') for item in results), "All items should have 'title' attribute"
        assert all(hasattr(item, 'attachments') for item in results), "All items should have 'attachments' attribute"
        
        # Find API Guide
        api_guide = next((item for item in results if item.title == "API Guide"), None)
        assert api_guide is not None, "Should find API Guide"
        assert len(api_guide.attachments) == 2, f"API Guide should have 2 attachments, got {len(api_guide.attachments)}"
        
        print("✅ Second test passed")
        print(f"   - Found {len(results)} content items")
        print(f"   - API Guide has {len(api_guide.attachments)} attachments")
        
        return True
        
    except Exception as e:
        print(f"❌ Second test failed: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Running Confluence tests without pytest...")
    print("=" * 60)
    
    # Run first test
    test1_passed = run_first_test()
    
    # Run second test  
    test2_passed = run_second_test()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"test_get_document_with_mock_confluence_and_gcs: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"test_mock_confluence_client_integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed")
        sys.exit(1)
