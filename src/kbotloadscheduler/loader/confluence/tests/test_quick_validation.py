#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test rapide pour valider le système RAG Confluence.
"""

import argparse
import os
import sys

# Ajouter le répertoire racine du projet au path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))


def run_mock_tests():
    """Exécute les tests avec des données simulées."""
    print("🧪 Tests avec Données Simulées")
    print("=" * 40)

    # Afficher le résumé des données
    print("\n1️⃣ Résumé des données de test :")
    os.system("python confluence_rag/tests/run_integration_tests.py --summary")

    # Exécuter un scénario
    print("\n2️⃣ Test d'un scénario spécifique :")
    os.system("python confluence_rag/tests/run_integration_tests.py --scenario")

    # Exécuter les tests d'intégration
    print("\n3️⃣ Tests d'intégration complets :")
    result = os.system(
        "python confluence_rag/tests/run_integration_tests.py --run-tests"
    )

    if result == 0:
        print("\n✅ Tous les tests simulés ont réussi !")
        return True
    else:
        print("\n❌ Certains tests simulés ont échoué.")
        return False


def check_real_confluence_config():
    """Vérifie si la configuration pour Confluence réel est disponible."""
    from dotenv import load_dotenv

    load_dotenv()

    url = os.getenv("CONFLUENCE_BASE_URL") or os.getenv("CONFLUENCE_URL")
    pat_token = os.getenv("CONFLUENCE_PAT_TOKEN")
    username = os.getenv("CONFLUENCE_USERNAME")
    api_token = os.getenv("CONFLUENCE_API_TOKEN")

    if not url:
        return False, "URL Confluence manquante"

    if not (pat_token or (username and api_token)):
        return False, "Identifiants Confluence manquants"

    return True, "Configuration OK"


def run_real_confluence_test(space_key: str):
    """Exécute un test avec un vrai espace Confluence."""
    print(f"🌐 Test avec Confluence Réel - Espace: {space_key}")
    print("=" * 50)

    # Vérifier la configuration
    config_ok, message = check_real_confluence_config()
    if not config_ok:
        print(f"❌ Configuration manquante : {message}")
        print("\n💡 Pour tester avec un vrai Confluence :")
        print("   1. Copiez .env.example vers .env")
        print("   2. Remplissez vos identifiants Confluence")
        print("   3. Relancez ce script avec --space YOUR_SPACE_KEY")
        return False

    # Analyser l'espace
    print("\n1️⃣ Analyse de l'espace :")
    result = os.system(
        f"python confluence_rag/tests/test_real_confluence_integration.py --space {space_key} --analyze"
    )

    if result != 0:
        print("❌ Échec de l'analyse de l'espace")
        return False

    # Demander confirmation pour la synchronisation
    print(f"\n2️⃣ Synchronisation de test pour l'espace {space_key}")
    confirm = input("   Voulez-vous continuer avec la synchronisation ? (y/N): ")

    if confirm.lower().startswith("y"):
        result = os.system(
            f"python confluence_rag/tests/test_real_confluence_integration.py "
            f"--space {space_key} --sync --max-results 5"
        )
        if result == 0:
            print("\n✅ Test avec Confluence réel réussi !")
            return True
        else:
            print("\n❌ Échec du test avec Confluence réel")
            return False
    else:
        print("⏭️  Synchronisation annulée")
        return True


def run_all_tests():
    """Exécute tous les types de tests disponibles."""
    print("🚀 Tests Complets du Système RAG Confluence")
    print("=" * 60)

    success = True

    # Tests simulés
    print("\n📋 ÉTAPE 1 : Tests avec données simulées")
    success &= run_mock_tests()

    # Tests avec Confluence réel (si configuré)
    config_ok, _ = check_real_confluence_config()
    if config_ok:
        space_key = os.getenv("DEFAULT_SPACE_KEY")
        if space_key:
            print(f"\n📋 ÉTAPE 2 : Tests avec Confluence réel (espace: {space_key})")
            success &= run_real_confluence_test(space_key)
        else:
            print("\n⚠️  DEFAULT_SPACE_KEY non défini, tests Confluence réel ignorés")
    else:
        print("\n⚠️  Configuration Confluence non disponible, tests réels ignorés")

    return success


def show_help():
    """Affiche l'aide détaillée."""
    print("🆘 Aide - Tests RAG Confluence")
    print("=" * 40)
    print()
    print(
        "Ce script vous aide à tester votre système RAG Confluence de différentes manières :"
    )
    print()
    print("📊 TESTS AVEC DONNÉES SIMULÉES :")
    print("   python confluence_rag/tests/test_quick_validation.py --mock")
    print("   • Utilise des données de test générées automatiquement")
    print("   • Espace de test avec 8 pages et hiérarchie")
    print("   • Pièces jointes simulées (PDF, DOCX, PNG, etc.)")
    print("   • Aucune configuration Confluence requise")
    print()
    print("🌐 TESTS AVEC CONFLUENCE RÉEL :")
    print(
        "   python confluence_rag/tests/test_quick_validation.py --real YOUR_SPACE_KEY"
    )
    print("   • Teste avec un vrai espace Confluence")
    print("   • Nécessite la configuration dans .env")
    print("   • Analyse l'espace avant synchronisation")
    print()
    print("🚀 TESTS COMPLETS :")
    print("   python confluence_rag/tests/test_quick_validation.py --all")
    print("   • Exécute tous les types de tests")
    print("   • Tests simulés + tests réels (si configurés)")
    print()
    print("⚙️  CONFIGURATION POUR TESTS RÉELS :")
    print("   1. cp .env.example .env")
    print("   2. Éditez .env avec vos identifiants :")
    print("      CONFLUENCE_BASE_URL=https://your-domain.atlassian.net")
    print("      CONFLUENCE_PAT_TOKEN=your_token")
    print("      DEFAULT_SPACE_KEY=YOUR_SPACE")
    print()
    print("📁 FICHIERS GÉNÉRÉS :")
    print("   • /tmp/confluence_rag_tests/ (tests simulés)")
    print("   • ./test_output/ (tests réels)")
    print()
    print("🔍 DÉPANNAGE :")
    print("   • Ajoutez --verbose pour plus de détails")
    print("   • Vérifiez les logs dans les répertoires de sortie")
    print("   • Consultez confluence_rag/tests/GUIDE_TESTS_INTEGRATION.md")


def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(
        description="Test rapide du système RAG Confluence"
    )
    parser.add_argument(
        "--mock", action="store_true", help="Tests avec données simulées"
    )
    parser.add_argument(
        "--real", metavar="SPACE_KEY", help="Tests avec Confluence réel"
    )
    parser.add_argument("--all", action="store_true", help="Tous les tests")
    parser.add_argument("--help-detailed", action="store_true", help="Aide détaillée")

    args = parser.parse_args()

    if args.help_detailed:
        show_help()
        return

    if not any([args.mock, args.real, args.all]):
        print("🧪 Test Rapide - Système RAG Confluence")
        print("=" * 45)
        print()
        print("Choisissez un type de test :")
        print("  --mock              Tests avec données simulées")
        print("  --real SPACE_KEY    Tests avec Confluence réel")
        print("  --all               Tous les tests")
        print("  --help-detailed     Aide complète")
        print()
        print("Exemple : python confluence_rag/tests/test_quick_validation.py --mock")
        return

    success = True

    if args.mock:
        success = run_mock_tests()
    elif args.real:
        success = run_real_confluence_test(args.real)
    elif args.all:
        success = run_all_tests()

    print("\n" + "=" * 60)
    if success:
        print("🎉 Tous les tests ont réussi ! Votre système fonctionne correctement.")
    else:
        print("⚠️  Certains tests ont échoué. Consultez les logs pour plus de détails.")
        sys.exit(1)


if __name__ == "__main__":
    main()
