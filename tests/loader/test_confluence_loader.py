from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

import pytest
from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
from kbotloadscheduler.loader.abstract_loader import LoaderException


class TestConfluenceLoader:
    """Tests pour le ConfluenceLoader"""

    @pytest.fixture
    def mock_config_with_secret(self):
        """Mock du ConfigWithSecret"""
        config_mock = MagicMock()
        config_mock.get_confluence_credentials.return_value = {
            "pat_token": "test_pat_token",
            "username": "test_user",
            "api_token": "test_api_token"
        }
        return config_mock

    @pytest.fixture
    def source_bean(self):
        """Source bean de test pour Confluence"""
        return SourceBean(
            id=1,
            code="test_confluence",
            label="Test Confluence Source",
            src_type="confluence",
            configuration='{"spaces": ["TEST"], "max_results": 100, "include_attachments": true}',
            last_load_time=1234567890,
            load_interval=24,
            domain_code="test_domain",
            perimeter_code="test_perimeter",
            force_embedding=False
        )

    @pytest.fixture
    def mock_content_item(self):
        """Mock d'un ContentItem Confluence"""
        content_item = MagicMock()
        content_item.id = "123456"
        content_item.title = "Test Page"
        content_item.web_ui_link = "https://confluence.example.com/display/TEST/Test+Page"
        content_item.last_updated = datetime.now(timezone.utc)
        content_item.attachments = []
        return content_item

    @pytest.fixture
    def mock_attachment(self):
        """Mock d'un Attachment Confluence"""
        attachment = MagicMock()
        attachment.id = "789012"
        attachment.title = "test_file.pdf"
        attachment.download_link = "https://confluence.example.com/download/attachments/123456/test_file.pdf"
        attachment.created = datetime.now(timezone.utc)
        return attachment

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST',
        'CONFLUENCE_TIMEOUT': '30'
    })
    def test_init_success(self, mock_config_with_secret):
        """Test de l'initialisation réussie du ConfluenceLoader"""
        loader = ConfluenceLoader(mock_config_with_secret)

        assert loader._loader_type == "confluence"
        assert loader.confluence_url == "https://confluence.example.com"
        assert loader.default_space_key == "TEST"

    def test_init_missing_url(self, mock_config_with_secret):
        """Test de l'initialisation avec URL manquante"""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(LoaderException, match="CONFLUENCE_URL environment variable is required"):
                ConfluenceLoader(mock_config_with_secret)

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_confluence_config_with_pat(self, mock_config_with_secret, source_bean):
        """Test de création de configuration avec PAT token"""
        mock_config_with_secret.get_confluence_credentials.return_value = {
            "pat_token": "test_pat_token"
        }

        loader = ConfluenceLoader(mock_config_with_secret)
        config = loader._create_confluence_config(source_bean)

        assert str(config.url).rstrip('/') == "https://confluence.example.com"
        assert config.default_space_key == "TEST"
        assert config.pat_token.get_secret_value() == "test_pat_token"

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_confluence_config_with_api_token(self, mock_config_with_secret, source_bean):
        """Test de création de configuration avec API token"""
        mock_config_with_secret.get_confluence_credentials.return_value = {
            "username": "<EMAIL>",
            "api_token": "test_api_token"
        }

        loader = ConfluenceLoader(mock_config_with_secret)
        config = loader._create_confluence_config(source_bean)

        assert str(config.url).rstrip('/') == "https://confluence.example.com"
        assert config.username == "<EMAIL>"
        assert config.api_token.get_secret_value() == "test_api_token"

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_confluence_config_no_credentials(self, mock_config_with_secret, source_bean):
        """Test de création de configuration sans credentials valides"""
        mock_config_with_secret.get_confluence_credentials.return_value = {}

        loader = ConfluenceLoader(mock_config_with_secret)

        with pytest.raises(LoaderException, match="No valid Confluence credentials found"):
            loader._create_confluence_config(source_bean)

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_create_search_criteria(self, mock_config_with_secret, source_bean):
        """Test de création des critères de recherche"""
        loader = ConfluenceLoader(mock_config_with_secret)
        criteria = loader._create_search_criteria(source_bean)

        assert criteria.spaces == ["TEST"]
        assert criteria.max_results == 100
        assert criteria.include_attachments is True

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_content_item_to_document_bean(self, mock_config_with_secret, source_bean, mock_content_item):
        """Test de conversion ContentItem vers DocumentBean"""
        loader = ConfluenceLoader(mock_config_with_secret)
        doc_bean = loader._content_item_to_document_bean(source_bean, mock_content_item)

        assert doc_bean.id == "test_domain/test_confluence/page_123456"
        assert doc_bean.name == "Test Page"
        assert doc_bean.path == "https://confluence.example.com/display/TEST/Test+Page"
        assert doc_bean.modification_time == mock_content_item.last_updated

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_content_item_to_document_bean_with_missing_web_ui_link(self, mock_config_with_secret, source_bean):
        """Test de conversion ContentItem vers DocumentBean avec web_ui_link manquant"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un content item sans web_ui_link
        content_item = MagicMock()
        content_item.id = "123456"
        content_item.title = "Test Page No Link"
        content_item.web_ui_link = None  # Web UI link manquant
        content_item.last_updated = datetime.now(timezone.utc)

        # Appeler la méthode de conversion
        doc_bean = loader._content_item_to_document_bean(source_bean, content_item)

        # Vérifier que l'URL par défaut a été utilisée
        assert doc_bean.id == "test_domain/test_confluence/page_123456"
        assert doc_bean.name == "Test Page No Link"
        assert doc_bean.path == "https://confluence.example.com/pages/viewpage.action?pageId=123456"
        assert doc_bean.modification_time == content_item.last_updated

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_content_item_to_document_bean_with_special_chars_in_title(self, mock_config_with_secret, source_bean):
        """Test de conversion ContentItem vers DocumentBean avec caractères spéciaux dans le titre"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un content item avec caractères spéciaux dans le titre
        content_item = MagicMock()
        content_item.id = "789012"
        content_item.title = "Test Page : caractères spéciaux & symboles [!@#]"
        content_item.web_ui_link = "https://confluence.example.com/display/TEST/Special+Chars"
        content_item.last_updated = datetime.now(timezone.utc)

        # Appeler la méthode de conversion
        doc_bean = loader._content_item_to_document_bean(source_bean, content_item)

        # Vérifier que le titre avec caractères spéciaux a été nettoyé par _sanitize_filename
        assert doc_bean.id == "test_domain/test_confluence/page_789012"
        # Caractères spéciaux remplacés par _
        assert doc_bean.name == "Test Page _ caractères spéciaux _ symboles [!@_]"
        assert doc_bean.path == "https://confluence.example.com/display/TEST/Special+Chars"
        assert doc_bean.modification_time == content_item.last_updated

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_content_item_to_document_bean_with_timestamp_instead_of_datetime(self, mock_config_with_secret,
                                                                              source_bean):
        """Test de conversion ContentItem vers DocumentBean avec timestamp au lieu d'objet datetime"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un timestamp (int) au lieu d'un objet datetime
        timestamp = int(datetime.now(timezone.utc).timestamp())

        # Créer un content item avec timestamp
        content_item = MagicMock()
        content_item.id = "456789"
        content_item.title = "Timestamp Test Page"
        content_item.web_ui_link = "https://confluence.example.com/display/TEST/Timestamp+Test"
        content_item.last_updated = timestamp  # Utilisation d'un timestamp

        # Appeler la méthode de conversion
        doc_bean = loader._content_item_to_document_bean(source_bean, content_item)

        # Vérifier que le timestamp a été converti en datetime par Pydantic
        assert doc_bean.id == "test_domain/test_confluence/page_456789"
        assert doc_bean.name == "Timestamp Test Page"
        assert doc_bean.path == "https://confluence.example.com/display/TEST/Timestamp+Test"
        # Pydantic convertit automatiquement le timestamp en datetime
        assert isinstance(doc_bean.modification_time, datetime)
        assert doc_bean.modification_time == datetime.fromtimestamp(timestamp, tz=timezone.utc)

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_attachment_to_document_bean(self, mock_config_with_secret, source_bean, mock_content_item,
                                         mock_attachment):
        """Test de conversion Attachment vers DocumentBean - cas normal"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Appeler la méthode de conversion
        doc_bean = loader._attachment_to_document_bean(source_bean, mock_content_item, mock_attachment)

        # Vérifier les propriétés du bean résultant
        assert doc_bean.id == "test_domain/test_confluence/attachment_789012"
        assert doc_bean.name == "Test Page/test_file.pdf"
        assert doc_bean.path == "https://confluence.example.com/download/attachments/123456/test_file.pdf"
        assert doc_bean.modification_time == mock_attachment.created

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_attachment_to_document_bean_with_no_created_date(self, mock_config_with_secret, source_bean,
                                                              mock_content_item):
        """Test de conversion Attachment vers DocumentBean avec date de création manquante"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un attachment sans date de création
        attachment = MagicMock()
        attachment.id = "567890"
        attachment.title = "fichier_sans_date.docx"
        attachment.download_link = "https://confluence.example.com/download/attachments/123456/fichier_sans_date.docx"
        attachment.created = None  # Date de création manquante

        # Appeler la méthode de conversion
        doc_bean = loader._attachment_to_document_bean(source_bean, mock_content_item, attachment)

        # Vérifier que la date de mise à jour du parent est utilisée
        assert doc_bean.id == "test_domain/test_confluence/attachment_567890"
        assert doc_bean.name == "Test Page/fichier_sans_date.docx"
        assert doc_bean.path == "https://confluence.example.com/download/attachments/123456/fichier_sans_date.docx"
        assert doc_bean.modification_time == mock_content_item.last_updated

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_attachment_to_document_bean_with_special_characters(self, mock_config_with_secret, source_bean,
                                                                 mock_content_item):
        """Test de conversion Attachment vers DocumentBean avec caractères spéciaux"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un attachment avec caractères spéciaux
        attachment = MagicMock()
        attachment.id = "123789"
        attachment.title = "données spéciales_éèà.csv"
        attachment.download_link = (
            "https://confluence.example.com/download/attachments/123456/"
            "donn%C3%A9es%20sp%C3%A9ciales_%C3%A9%C3%A8%C3%A0.csv"
        )
        attachment.created = datetime.now(timezone.utc)

        # Appeler la méthode de conversion
        doc_bean = loader._attachment_to_document_bean(source_bean, mock_content_item, attachment)

        # Vérifier que les caractères spéciaux sont préservés dans le nom
        assert doc_bean.id == "test_domain/test_confluence/attachment_123789"
        assert doc_bean.name == "Test Page/données spéciales_éèà.csv"
        expected_path = (
            "https://confluence.example.com/download/attachments/123456/"
            "donn%C3%A9es%20sp%C3%A9ciales_%C3%A9%C3%A8%C3%A0.csv"
        )
        assert doc_bean.path == expected_path
        assert doc_bean.modification_time == attachment.created

    @patch.dict('os.environ', {
        'CONFLUENCE_URL': 'https://confluence.example.com',
        'DEFAULT_SPACE_KEY': 'TEST'
    })
    def test_attachment_to_document_bean_with_timestamp(self, mock_config_with_secret, source_bean, mock_content_item):
        """Test de conversion Attachment vers DocumentBean avec timestamp au lieu d'objet datetime"""
        loader = ConfluenceLoader(mock_config_with_secret)

        # Créer un timestamp
        timestamp = int(datetime.now(timezone.utc).timestamp())

        # Créer un attachment avec timestamp
        attachment = MagicMock()
        attachment.id = "456123"
        attachment.title = "timestamped_file.xlsx"
        attachment.download_link = "https://confluence.example.com/download/attachments/123456/timestamped_file.xlsx"
        attachment.created = timestamp

        # Appeler la méthode de conversion
        doc_bean = loader._attachment_to_document_bean(source_bean, mock_content_item, attachment)

        # Vérifier que le timestamp a été converti en datetime par Pydantic
        assert doc_bean.id == "test_domain/test_confluence/attachment_456123"
        assert doc_bean.name == "Test Page/timestamped_file.xlsx"
        assert doc_bean.path == "https://confluence.example.com/download/attachments/123456/timestamped_file.xlsx"
        # Pydantic convertit automatiquement le timestamp en datetime
        assert isinstance(doc_bean.modification_time, datetime)
        assert doc_bean.modification_time == datetime.fromtimestamp(timestamp, tz=timezone.utc)
