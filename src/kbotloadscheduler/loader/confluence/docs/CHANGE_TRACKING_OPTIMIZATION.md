# Optimisation du Change Tracking pour Cloud Run

## Problème identifié

Dans le contexte de kbot-load-scheduler déploy<PERSON> sur Cloud Run, le suivi des changements se fait à 3 niveaux :

1. **Niveau 1** : `ConfluenceLoader.get_document_list()` - Filtrage initial
2. **Niveau 2** : `DocumentService.compare_document_list()` - Comparaison avec les documents déjà embeddés
3. **Niveau 3** : `ConfluenceChangeTracker` - Suivi interne par hash

Le problème : le `ConfluenceChangeTracker` (niveau 3) utilise un stockage local par défaut (`.confluence_rag_data/`) qui
est éphémère dans Cloud Run. Cela rend son suivi des changements inefficace entre les exécutions planifiées distinctes.

## Solution implémentée

### 1. Paramètre de configuration optionnel

Ajout du paramètre `enable_change_tracking` dans `ProcessingConfig` :

```python
class ProcessingConfig(BaseModel):
    # ... autres paramètres ...
    enable_change_tracking: bool = True  # Activer/désactiver le suivi des changements par hash
```

### 2. Configuration via variable d'environnement

```bash
# Activer le change tracking (par défaut)
export ENABLE_CHANGE_TRACKING=true

# Désactiver le change tracking
export ENABLE_CHANGE_TRACKING=false
```

### 3. Modification du SyncOrchestrator

Le `SyncOrchestrator` initialise maintenant le `ConfluenceChangeTracker` seulement si activé :

```python
# Initialiser le change tracker seulement si activé
self.change_tracker = ConfluenceChangeTracker() if self.processing_config.enable_change_tracking else None
```

Toutes les méthodes qui utilisent le `change_tracker` vérifient maintenant s'il est activé :

- `_process_changed_content()` : traite tous les contenus si le change tracking est désactivé
- `get_sync_status()` : retourne `None` pour `last_sync` si désactivé
- `run()` : enregistre la synchronisation seulement si activé

### 4. Optimisation dans ConfluenceLoader

Le `ConfluenceLoader` désactive automatiquement le change tracking car kbot-load-scheduler gère déjà le suivi des
changements via les niveaux 1 et 2 :

```python
# Configuration de traitement - désactiver le change tracking car kbot-load-scheduler
# gère déjà le suivi des changements via DocumentService.compare_document_list()
processing_config = ProcessingConfig.from_env()
processing_config.enable_change_tracking = False
```

## Avantages

1. **Performance optimisée** : Évite le calcul de hash redondant dans kbot-load-scheduler
2. **Compatibilité Cloud Run** : Évite les problèmes de stockage éphémère
3. **Flexibilité** : Le change tracking peut être réactivé si nécessaire à l'avenir
4. **Rétrocompatibilité** : Le module confluence conserve toutes ses fonctionnalités dans d'autres contextes

## Tests

Les tests unitaires valident :

- Configuration par défaut (change tracking activé)
- Configuration via variable d'environnement
- Comportement du SyncOrchestrator avec/sans change tracking
- Désactivation automatique dans ConfluenceLoader

## Usage futur

Si kbot-load-scheduler a besoin d'un suivi par hash pour Confluence à l'avenir, il pourra :

1. Configurer `ConfluenceChangeTrackerGCS` pour un stockage persistant
2. Réactiver le change tracking via la variable d'environnement
3. Modifier le ConfluenceLoader pour ne pas forcer la désactivation

## Impact sur les performances

- **Réduction** du temps de traitement (pas de calcul de hash)
- **Réduction** de la consommation mémoire
- **Élimination** des erreurs liées au stockage éphémère
- **Simplification** du flux de traitement dans kbot-load-scheduler
