import json
from kbotloadscheduler.service.sources_service import SourcesService
from kbotloadscheduler.apicall.kbot_back_api import KbotBackApi
from kbotloadscheduler.gcs.treatment_file_manager import TreatmentFileManager
from kbotloadscheduler.bean.beans import SourceBean
from data.kbot_back_api_test_data import KbotBackApiTestData
from tests.testutils.mock_gcs import MockGcs
from tests.testutils.mock_datetime import MockDatetime


class TestSourcesService:

    def test_load_sources(self, mocker, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket('mon_bucket-perimA')
        my_mock_gcs.add_bucket('mon_bucket-perimB')
        my_mock_gcs.add_bucket('mon_bucket-perimC')
        MockDatetime(mocker, "kbotloadscheduler.service.sources_service.datetime", "202409121430")

        treatment_file_manager = TreatmentFileManager('gs://mon_bucket-[perimeter_code]')
        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        sources_service = SourcesService(kbot_back_api, treatment_file_manager)
        actual_sources = sources_service.load_sources('perimB')

        expected_sources = []
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[21]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[22]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[23]])

        assert actual_sources == expected_sources

        for expected_source in expected_sources:
            adapter = kbot_back_api_test_data.adapters.get(f'/source/{expected_source.id}/done')
            assert adapter.called
            assert adapter.call_count == 1

            blob = my_mock_gcs.return_blob(
                'mon_bucket-'+expected_source.perimeter_code,
                '202409121430/'+expected_source.domain_code+'/'+expected_source.code+'/getlist/getlist.json')
            actual_source = SourceBean(**json.loads(blob.download_as_string()))
            assert actual_source == expected_source

    def test_load_sources_with_given_date(self, mocker, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket('mon_bucket-perimA')
        my_mock_gcs.add_bucket('mon_bucket-perimB')
        my_mock_gcs.add_bucket('mon_bucket-perimC')

        treatment_file_manager = TreatmentFileManager('gs://mon_bucket-[perimeter_code]')
        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        sources_service = SourcesService(kbot_back_api, treatment_file_manager)
        actual_sources = sources_service.load_sources('perimB', '202409121530')

        expected_sources = []
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[21]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[22]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[23]])

        assert actual_sources == expected_sources

        for expected_source in expected_sources:
            adapter = kbot_back_api_test_data.adapters.get(f'/source/{expected_source.id}/done')
            assert adapter.called
            assert adapter.call_count == 1

            blob = my_mock_gcs.return_blob(
                'mon_bucket-'+expected_source.perimeter_code,
                '202409121530/'+expected_source.domain_code+'/'+expected_source.code+'/getlist/getlist.json')
            actual_source = SourceBean(**json.loads(blob.download_as_string()))
            assert actual_source == expected_source

    def test_load_all_sources(self, mocker, requests_mock):
        kbot_back_api_test_data = KbotBackApiTestData()
        kbot_back_api_test_data.set_test_data(requests_mock)
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket('mon_bucket-perimA')
        my_mock_gcs.add_bucket('mon_bucket-perimB')
        my_mock_gcs.add_bucket('mon_bucket-perimC')
        MockDatetime(mocker, "kbotloadscheduler.service.sources_service.datetime", "202409121630")

        treatment_file_manager = TreatmentFileManager('gs://mon_bucket-[perimeter_code]')
        kbot_back_api = KbotBackApi(kbot_back_api_test_data.KBOT_BACK_API_URL)
        sources_service = SourcesService(kbot_back_api, treatment_file_manager)
        actual_sources = sources_service.load_all_sources()

        expected_sources = []
        expected_sources.extend([SourceBean(**s, perimeter_code='perimA')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[11]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimA')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[12]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimA')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[13]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[21]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[22]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimB')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[23]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimC')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[31]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimC')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[32]])
        expected_sources.extend([SourceBean(**s, perimeter_code='perimC')
                                 for s in kbot_back_api_test_data.SOURCES_LIST[33]])

        assert actual_sources == expected_sources

        for expected_source in expected_sources:
            adapter = kbot_back_api_test_data.adapters.get(f'/source/{expected_source.id}/done')
            assert adapter.called
            assert adapter.call_count == 1

            blob = my_mock_gcs.return_blob(
                'mon_bucket-'+expected_source.perimeter_code,
                '202409121630/'+expected_source.domain_code+'/'+expected_source.code+'/getlist/getlist.json')
            actual_source = SourceBean(**json.loads(blob.download_as_string()))
            assert actual_source == expected_source
