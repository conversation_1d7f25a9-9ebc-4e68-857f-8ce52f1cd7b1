#!/usr/bin/env python3
"""
Test importing lines of config.py step by step
"""

import sys
import os

# Add paths
sys.path.append('src')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

try:
    print("1. Testing basic imports...")
    from datetime import datetime
    from typing import Optional, List, Union, Dict, Any
    from enum import Enum
    import os
    import logging
    print("✅ Basic imports successful")
    
    print("2. Testing pydantic imports...")
    from pydantic import BaseModel, Field, HttpUrl, field_validator, model_validator, SecretStr
    print("✅ Pydantic imports successful")
    
    print("3. Testing Environment enum creation...")
    
    class Environment(Enum):
        """Énumération des environnements disponibles."""
        DEV = "dev"
        TEST = "test"
        STAGING = "staging"
        PROD = "prod"
    
    print("✅ Environment enum created")
    
    print("4. Testing SearchCriteria class creation...")
    
    class SearchCriteria(BaseModel):
        space_key: Optional[str] = Field(None, description="Clé de l'espace Confluence")
        spaces: Optional[List[str]] = Field(default=[], description="Liste des espaces à rechercher")
        labels: Optional[List[str]] = Field(default=[], description="Liste des labels à filtrer")
        start_date: Optional[datetime] = Field(None, description="Date de début pour la recherche")
        end_date: Optional[datetime] = Field(None, description="Date de fin pour la recherche")
        page_limit: Optional[int] = Field(default=100, description="Nombre maximum de pages à récupérer")
        max_results: Optional[int] = Field(default=1000, description="Nombre maximum de résultats")
        limit: Optional[int] = Field(default=100, description="Limite de résultats (alias pour max_results)")
        include_attachments: bool = Field(default=True, description="Inclure les pièces jointes")
        content_types: Optional[List[str]] = Field(default=["page"], description="Types de contenu à rechercher")
        title_contains: Optional[str] = Field(None, description="Filtre sur le titre")
        last_modified_days: Optional[int] = Field(None, description="Nombre de jours depuis la dernière modification")

        @field_validator('max_results')
        @classmethod
        def validate_max_results(cls, v):
            if v is not None and (v <= 0 or v > 10000):
                raise ValueError('max_results must be between 1 and 10000')
            return v

        @field_validator('last_modified_days')
        @classmethod
        def validate_last_modified_days(cls, v):
            if v is not None and v <= 0:
                raise ValueError('last_modified_days must be positive')
            return v
    
    print("✅ SearchCriteria class created successfully")
    
    print("5. Testing SearchCriteria instantiation...")
    criteria = SearchCriteria(max_results=100)
    print(f"✅ SearchCriteria instantiated: {criteria.max_results}")
    
    print("🎉 All tests passed! Config creation works.")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
