import re
from urllib.parse import urljoin, unquote, quote
from kbotloadscheduler.loader.basic.custom_markdownify import md as markdownify
from datetime import datetime, timezone
from typing import Optional

TITLE_MAPPING = {
    "caseContexts": "Contexte",
    "prerequisites": "Pré-requis",
    "keyPoints": "Points-Clés",
    "steps": "Étapes",
    "Generalites": "",
    "Cas": "",
    "Tabs": "",
    "sections": "",
    "generalities": "",
    "generalitiesTitle": ""
}

DATE_FORMAT = "%Y-%m-%dT%H:%M:%S%z"


class MissingMandatoryMetadataField(Exception):
    pass


# Replaces all relative urls in a markdown string with properly encoded absolute urls
def decode_and_replace_all_links(
        markdown_text, current_url, base_url="https://newbasic.sso.francetelecom.fr"
):
    """Replaces every relative link (or almost?) with its corresponding absolute url
    Ex: [CRM GP](/public/content/10068) -> [CRM GP](https://newbasic.sso.francetelecom.fr/public/content/10068)

        [Acquérir une offre haut-débit ADSL/VDSL2 (N1)](%23cas2) ->
            [Acquérir une offre haut-débit ADSL/VDSL2 (N1)]
            (https://newbasic.sso.francetelecom.fr/public/content/2186%23cas2)

        ![](../../contents/Procédures pro/ADCP/acr-media.JPG \"picto ACR MEDIA\") ->
            ![](https://newbasic.sso.francetelecom.fr/contents/
                    Proc%C3%A9dures%20pro/ADCP/acr-media.JPG \"picto ACR MEDIA\")
    """

    # Updated pattern to handle Markdown links correctly, capturing the URL and any following text separately
    # Le pattern initial : pattern = re.compile(r"(!?\[.*?\]\()([^ \)]+)([^\)]*)(\))", re.DOTALL)
    pattern = re.compile(r"(!?\[(?:\\\]|[^]]|](?!\())*+\]\()([^ \)]++)([^\)]*+)(\))", re.DOTALL)

    # Function to replace each encoded link with its decoded absolute URL
    def replace_link(match):
        link_text = match.group(
            1
        )  # Captures text before URL (e.g., [Link text] or ![Image description](
        url_part = match.group(2)  # Captures the URL part itself
        context_text = match.group(
            3
        )  # Captures the context text (e.g., titles or descriptions after the URL)

        # Process urls that start with "#"
        # Ex: "#cas2" becomes: "https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/761/#cas2"
        if url_part.startswith("#"):
            # avant on avait : return f"{current_url}/{url_part}"
            # Mais le text (link_text) n'était pas passé, le # était précédé d'un /
            # et le lien n'était pas entouré de () donc a priori pas bon
            return f"{link_text}{current_url}{url_part}{context_text}{match.group(4)}"

        # Decode percent-encoded characters and then encode only spaces in the URL part
        encoded_url = quote(
            unquote(url_part), safe="/:"
        )  # Decode any existing percent-encoding first, then re-encode spaces and special characters

        # le cas des url commençant par ../.. ne tient pas compte de current_url
        absolute_url = urljoin(base_url, encoded_url)  # Construct the absolute URL

        # Return the link with the correctly encoded URL and untouched context text
        return f"{link_text}{absolute_url}{context_text}{match.group(4)}"

    # Replace all occurrences in the markdown text
    updated_text = re.sub(pattern, replace_link, markdown_text)

    return updated_text


# Recursive preprocessing of items
def preprocess_items(items, current_url):
    if items:
        return list(
            map(
                lambda item: {
                    "title": item.get("title"),
                    "markdown": decode_and_replace_all_links(
                        (
                            markdownify(item.get("richText"))
                            if item.get("richText")
                            else ""
                        ),
                        current_url,
                    ),
                    "richText": item.get("richText"),
                    "sections": preprocess_items(item.get("sections"), current_url),
                },
                items,
            )
        )
    return []


# Prepare data for embedding by concatenating everything in the right order
def concatenate_content(content, depth=0, parent_num="", parent_key=""):

    if isinstance(content, dict):
        result = concatenate_content_dict(content, depth, parent_key, parent_num)
    elif isinstance(content, list):
        result = concatenate_content_list(content, depth, parent_num)
    elif isinstance(content, str):
        result = content + "\n"
    else:
        result = str(content) + "\n"
    return result


def concatenate_content_dict(content, depth, parent_key, parent_num):
    result = ""
    for i, (key, value) in enumerate(content.items(), 1):
        current_num = f"{parent_num}{i}." if parent_num else f"{i}."
        if value:
            result += concatenate_content_dict_key_value(depth, current_num, key, value)
        else:
            if key == "title" and (parent_key == "steps" or parent_key == TITLE_MAPPING["steps"]):
                value = content["markdown"].replace("#", "").replace("*", "")
                content["markdown"] = 0
                result += f"{'#' * (depth + 1)} {value}\n"
    return result


def concatenate_content_dict_key_value(depth, current_num, key, value):
    result_value = ""
    if key == "title":
        result_value = f"{'#' * (depth + 1)} {value}\n"
    elif key == "markdown":
        markdown = value
        result_value = concatenate_content(markdown, depth, current_num, key)
    elif key != "markdown" and key != "richText":
        title = TITLE_MAPPING[key]
        result_value = f"{'#' * (depth + 1)} {title}\n"
        result_value += concatenate_content(value, depth + 1, current_num, key)
    return result_value


def concatenate_content_list(content, depth, parent_num):
    result = ""
    for i, item in enumerate(content, 1):
        current_num = f"{parent_num}{i}." if parent_num else f"{i}."
        result += concatenate_content(item, depth, current_num)
    return result


# Preprocesses the sheet into digestable string content and returns content as string + metadata as dict
def preprocess_sheet(sheet, env):
    from pprint import pprint
    pprint(sheet)
    content = extract_content_from_sheet(sheet)
    metadata = extract_metadatas_from_sheet(sheet)
    # # checking fields only in prod
    # if env == "prd":
    #     check_metadatas(metadata)
    return content, metadata


def extract_content_from_sheet(sheet):
    sheet_title = sheet['content']['title']
    content = f"# {sheet_title}\n"
    cases = sheet["cases"]
    if cases:
        generalities = concatenate_content(sheet["generalities"])

        cases = [
            {
                "title": case["title"],
                "caseContexts": case["caseContexts"],
                "prerequisites": case["prerequisites"],
                "keyPoints": case["keyPoints"],
                "steps": case["steps"],
            }
            for case in cases
        ]
        cases = concatenate_content(cases)

        content += concatenate_content(
            {
                "Generalites": generalities,
                "Cas": cases,
            }
        )
    tabs = sheet["tabs"]
    if tabs:
        tabs = [
            {
                "title": tab["title"],
                "markdown": tab["markdown"],
                "sections": tab["sections"]
            }
            for tab in tabs
        ]

        content += concatenate_content({"Tabs": tabs})
    if not cases and sheet["generalities"]:
        content += concatenate_content({"generalitiesTitle": sheet["generalitiesTitle"],
                                        "generalities": sheet["generalities"]})
    return content


def extract_metadatas_from_sheet(sheet):
    short_info_markdown = sheet["shortInfoMarkdown"]
    universe = sheet["universe"]
    channel = sheet["channel"]
    market = sheet["market"]
    customer_need = sheet["customerNeed"]
    geographic_zone = sheet["geographicZone"]
    cases_processed = preprocess_cases(sheet["cases"])
    generalities_processed = clean(sheet["generalities"])
    tabs_processed = clean(sheet["tabs"])
    metadata = {
        "titre": sheet["content"]["title"],
        "url": sheet["url"],
        "url_content": sheet["url_content"],
        "summary": sheet["content"]["indexableAbstract"],
        "fiche_type": "procedure",
        "channel": channel,
        "market": market,
        "universe": universe,
        "customerNeed": customer_need,
        "geographicZone": geographic_zone,
        "status": sheet["status"],
        "endCommercialValidityDate": sheet["endCommercialValidityDate"],
        "creationDate": sheet["creationDate"],
        "modificationDate": convert_time_to_utc(sheet["privateModificationDate"]),
        "publicModificationDate": convert_time_to_utc(sheet["modificationDate"]),
        "privateModificationDate": convert_time_to_utc(sheet["privateModificationDate"]),
        "shortInfo": short_info_markdown,
        "procedure_id": sheet["id"],
        "legalSensitivity": sheet["content"].get("legalSensitivity"),
        "statusBa": sheet["content"].get("statusBa"),
        "structured": {
            "cases": cases_processed,
            "generalities": generalities_processed,
            "generalitiesTitle": sheet["generalitiesTitle"],
            "firstTabTitle": sheet["firstTabTitle"],
            "tabs": tabs_processed
        }
    }
    # checking if summary is empty
    if metadata["summary"] == "" and metadata["shortInfo"] != "":
        metadata["summary"] = metadata["shortInfo"]
    return metadata


def delete_keys(d, keys):
    for key in keys:
        d.pop(key)
    return d


def clean(list_to_clean):
    new_list = []
    for d in list_to_clean:
        d.pop("richText")
        new_sections = clean(d["sections"])
        d["sections"] = new_sections
        new_list.append(d)
    return new_list


def clean_all(d, keys):
    for key in keys:
        d[key] = clean(d[key])
    return d


def preprocess_cases(cases):
    from copy import deepcopy

    new_cases = []
    for case in cases:
        new_case = deepcopy(case)
        new_case = delete_keys(new_case, ["anchor", "position", "univers", "canal"])
        case_title = case["caseTitle"]
        title = case["title"]
        if len(case_title) > len(title):
            new_case["title"] = case_title
        new_case.pop("caseTitle")
        new_case = clean_all(new_case, ["prerequisites", "keyPoints", "steps", "caseContexts"])
        new_cases.append(new_case)
    return new_cases


def check_metadatas(metadata):
    for field in metadata.keys():
        if field not in ["summary", "shortInfo"] and metadata[field] == "":
            raise MissingMandatoryMetadataField("Missing %s in basic sheet" % field)
    for subfield in ["cases", "tabs"]:
        if subfield not in metadata["structured"].keys():
            raise MissingMandatoryMetadataField("Missing field in basic sheet structured->%s" % subfield)
    if len(metadata["structured"]["cases"]) + len(metadata["structured"]["tabs"]) == 0:
        raise MissingMandatoryMetadataField(
            "Empty list for both structured->cases and structured->tabs" % subfield)


def parse_datetime(string_datetime: Optional[str]) -> datetime:
    """
    Traduit la date et l'heure reçues de l'api en un objet datetime.
    Le format reçu est 2024-09-03T13:00:44Z compatible avec la norme ISO 8601
    """
    if string_datetime is None or string_datetime == '':
        return datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=timezone.utc)
    return datetime.strptime(string_datetime, DATE_FORMAT).astimezone(timezone.utc)


def convert_time_to_utc(string_datetime: str) -> str:
    return parse_datetime(string_datetime).strftime(DATE_FORMAT)
