# Package de Traitement RAG Confluence

Ce package contient les composants de traitement pour le système RAG Confluence.

## Vue d'ensemble de l'architecture

## Structure des modules

### 1. `enums.py`

**Objectif** : Énumérations et classes de données

- `ProcessingStatus` : Énumération des statuts de traitement
- `MediaType` : Types de médias pris en charge
- `ExtractionResult` : Classe de données du résultat d'extraction de texte
- `DrawIOMetadata` : Métadonnées des diagrammes Draw.io

### 2. `document_extractors.py`

**Objectif** : Extraction principale du texte des documents

- `DocumentExtractor` : Classe d'extraction principale
- Prise en charge : PDF, DOCX, XLSX, TXT, HTML, XML
- S'intègre avec des processeurs spécialisés

### 3. `drawio_processor.py`

**Objectif** : Traitement spécialisé des diagrammes Draw.io

- `DrawIOProcessor` : Gestionnaire dédié à Draw.io
- Analyse complexe du XML et extraction de métadonnées
- Analyse de la structure du diagramme

### 4. `attachment_processor.py`

**Objectif** : Traitement des pièces jointes Confluence

- `AttachmentProcessor` : Téléchargement et traitement des pièces jointes
- Traitement parallèle avec des pools de threads
- Gestion des erreurs et statistiques

### 5. `content_chunker.py`

**Objectif** : Fonctionnalité de découpage de texte (chunking)

- `ContentChunker` : Découpage de texte configurable
- Gestion des chevauchements
- Préservation des métadonnées

### 6. `content_retriever.py`

**Objectif** : Orchestration de la récupération de contenu

- `ContentRetriever` : Classe d'orchestration principale
- Flux de travail de recherche et de récupération
- Traitement récursif des enfants

## Exemples d'utilisation

### Nouvelle approche modulaire

```python
from confluence.processing import (
    DocumentExtractor,
    AttachmentProcessor,
    ContentRetriever
)

# Utiliser des composants spécifiques
extractor = DocumentExtractor(logger)
processor = SyncAttachmentProcessor(client, config)
retriever = SyncContentRetriever(client, config)
```

## Améliorations futures

1. **Système de plugins** : Permettre des processeurs de documents externes
2. **Mise en cache** : Ajouter une mise en cache intelligente pour le contenu extrait
3. **Streaming** : Prise en charge du streaming pour les documents volumineux
4. **Intégration OCR** : Ajouter la prise en charge de l'OCR pour les images
5. **Détection de format** : Améliorations de la détection automatique de format

## Tests

Chaque module peut être testé indépendamment :

```bash
# Tester les composants individuels
pytest confluence_rag/processing/test_document_extractors.py
pytest confluence_rag/processing/test_drawio_processor.py
pytest confluence_rag/processing/test_sync_attachment_processor.py
```

## Dépendances

- Traitement de documents : pypdf, python-docx, openpyxl, markdownify
- Traitement XML : xml.etree.ElementTree, BeautifulSoup4
- Traitement synchrone : requests + ThreadPoolExecutor
- Gestion des threads : threading intégré + gestionnaire de pool de threads personnalisé