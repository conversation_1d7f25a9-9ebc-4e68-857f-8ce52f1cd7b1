import requests
from .sharepoint_credentials import SharepointCredentials
import json
import urllib.parse


class SharepointClient:
    NAME = "Name"
    SERVER_RELATIVE_URL = "ServerRelativeUrl"
    TIME_CREATED = "TimeCreated"
    TIME_LAST_MODIFIED = "TimeLastModified"
    UNIQUE_ID = "UniqueId"
    FIELDS_TO_RETRIEVE = ','.join([NAME, SERVER_RELATIVE_URL, TIME_CREATED, TIME_LAST_MODIFIED, UNIQUE_ID])

    def __init__(self, sharepoint_credentials: SharepointCredentials, sharepoint_url, site_name: str):
        self.access_token = sharepoint_credentials.get_access_token()
        self.site_name = site_name
        self.site_url = sharepoint_url + '/sites/' + site_name
        self.headers = {'Content-Type': 'application/x-www-form-urlencoded'}

    def get_base_folders(self):
        full_url = self.build_full_url('folders', self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_folder_info(self, use_id, folder_info):
        if use_id == self.SERVER_RELATIVE_URL:
            return self.get_folder_info_by_path(folder_info.get(self.SERVER_RELATIVE_URL))
        if use_id == self.UNIQUE_ID:
            return self.get_folder_info_by_id(folder_info.get(self.UNIQUE_ID))
        raise RuntimeError(f"use_id={use_id} should be {self.SERVER_RELATIVE_URL} os {self.UNIQUE_ID}")

    def get_folder_info_by_path(self, relative_url):
        encoded_relative_url = urllib.parse.quote(relative_url)
        full_url = self.build_full_url(f"GetFolderByServerRelativePath(decodedurl='{encoded_relative_url}')",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_folder_info_by_id(self, folder_id):
        full_url = self.build_full_url(f"GetFolderById('{folder_id}')",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_sub_folders(self, use_id, folder_info):
        if use_id == self.SERVER_RELATIVE_URL:
            return self.get_sub_folders_by_path(folder_info.get(self.SERVER_RELATIVE_URL))
        if use_id == self.UNIQUE_ID:
            return self.get_sub_folders_by_id(folder_info.get(self.UNIQUE_ID))
        raise RuntimeError(f"use_id={use_id} should be {self.SERVER_RELATIVE_URL} os {self.UNIQUE_ID}")

    def get_sub_folders_by_path(self, relative_url):
        encoded_relative_url = urllib.parse.quote(relative_url)
        full_url = self.build_full_url(f"GetFolderByServerRelativePath(decodedurl='{encoded_relative_url}')/folders",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_sub_folders_by_id(self, folder_id):
        full_url = self.build_full_url(f"GetFolderById('{folder_id}')/folders",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_folders_files(self, use_id, folder_info):
        if use_id == self.SERVER_RELATIVE_URL:
            return self.get_folders_files_by_path(folder_info.get(self.SERVER_RELATIVE_URL))
        if use_id == self.UNIQUE_ID:
            return self.get_folders_files_by_id(folder_info.get(self.UNIQUE_ID))
        raise RuntimeError(f"use_id={use_id} should be {self.SERVER_RELATIVE_URL} os {self.UNIQUE_ID}")

    def get_folders_files_by_path(self, relative_url):
        encoded_relative_url = urllib.parse.quote(relative_url)
        full_url = self.build_full_url(f"GetFolderByServerRelativePath(decodedurl='{encoded_relative_url}')/files",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_folders_files_by_id(self, folder_id):
        full_url = self.build_full_url(f"GetFolderById('{folder_id}')/files",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_list_response(response)

    def get_file_info(self, use_id, file_info):
        if use_id == self.SERVER_RELATIVE_URL:
            return self.get_file_info_by_path(file_info.get(self.SERVER_RELATIVE_URL))
        if use_id == self.UNIQUE_ID:
            return self.get_file_info_by_id(file_info.get(self.UNIQUE_ID))
        raise RuntimeError(f"use_id={use_id} should be {self.SERVER_RELATIVE_URL} os {self.UNIQUE_ID}")

    def get_file_info_by_path(self, relative_url):
        encoded_relative_url = urllib.parse.quote(relative_url)
        full_url = self.build_full_url(f"GetFileByServerRelativePath(decodedurl='{encoded_relative_url}')",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_file_info_by_id(self, file_id):
        full_url = self.build_full_url(f"GetFileById('{file_id}')",
                                       self.FIELDS_TO_RETRIEVE)
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_file_properties(self, use_id, file_info):
        if use_id == self.SERVER_RELATIVE_URL:
            return self.get_file_properties_by_path(file_info.get(self.SERVER_RELATIVE_URL))
        if use_id == self.UNIQUE_ID:
            return self.get_file_properties_by_id(file_info.get(self.UNIQUE_ID))
        raise RuntimeError(f"use_id={use_id} should be {self.SERVER_RELATIVE_URL} os {self.UNIQUE_ID}")

    def get_file_properties_by_path(self, relative_url):
        encoded_relative_url = urllib.parse.quote(relative_url)
        full_url = self.build_full_url(f"GetFileByServerRelativePath(decodedurl='{encoded_relative_url}')/properties",
                                       '')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_file_properties_by_id(self, file_id):
        full_url = self.build_full_url(f"GetFileById('{file_id}')/properties",
                                       '')
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return self.prepare_info_response(response)

    def get_file_bytes_content(self, use_id, file_info):
        if use_id == self.SERVER_RELATIVE_URL:
            return self.get_file_bytes_content_by_path(file_info.get(self.SERVER_RELATIVE_URL))
        if use_id == self.UNIQUE_ID:
            return self.get_file_bytes_content_by_id(file_info.get(self.UNIQUE_ID))
        raise RuntimeError(f"use_id={use_id} should be {self.SERVER_RELATIVE_URL} os {self.UNIQUE_ID}")

    def get_file_bytes_content_by_path(self, relative_url):
        encoded_relative_url = urllib.parse.quote(relative_url)
        full_url = self.build_full_url(f"GetFileByServerRelativePath(decodedurl='{encoded_relative_url}')/$value")
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return response.content

    def get_file_bytes_content_by_id(self, file_id):
        full_url = self.build_full_url(f"GetFileById('{file_id}')/$value")
        response = self.call_get(full_url)
        if not response.ok:
            raise SharepointException(full_url, response)
        return response.content

    def build_full_url(self, command: str, fields: str = ""):
        full_url = f"{self.site_url}/_api/web/{command}"
        if fields != "":
            full_url = full_url + f"?$select={fields}"
        return full_url

    def call_get(self, full_url):
        return requests.get(full_url, timeout=300, headers={
            'Accept': 'application/json;odata=verbose',
            'Authorization': f'Bearer {self.access_token}'})

    def prepare_list_response(self, response):
        json_response = response.json()
        print(json.dumps(json_response))
        return_response = json_response.get('d', {}).get('results', [])
        return [self.prepare_item(item) for item in return_response]

    def prepare_info_response(self, response):
        json_response = response.json()
        print(json.dumps(json_response))
        item = json_response.get('d', {})
        return self.prepare_item(item)

    def prepare_item(self, item):
        metadata = item.pop('__metadata', {})
        item['type'] = metadata.get('type', '')
        item['uri'] = metadata.get('uri', '')
        return item


class SharepointException(Exception):
    NOT_CRITICAL_CODES = [404]
    status_code: int

    def __init__(self, url, response):
        error_message = "Sharepoint call on url %s failed: " % url
        if response.status_code == 200:
            error_message += response.json().get('error_description')
        elif response.status_code in SharepointException.NOT_CRITICAL_CODES:
            error_message += "status_code is %s" % response.status_code
        else:
            error_message += "[critical] status_code is %s" % response.status_code
        super().__init__(error_message)
        self.status_code = response.status_code
