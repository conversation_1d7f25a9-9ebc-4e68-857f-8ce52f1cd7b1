from datetime import datetime
from typing import Optional, List, Union, Dict, Any
from enum import Enum
import os
import logging
from pydantic import BaseModel, Field, HttpUrl, field_validator, model_validator, SecretStr

class Environment(Enum):
    """Énumération des environnements disponibles."""
    DEV = "dev"
    TEST = "test"
    STAGING = "staging"
    PROD = "prod"

class SearchCriteria(BaseModel):
    space_key: Optional[str] = Field(None, description="Clé de l'espace Confluence")
    spaces: Optional[List[str]] = Field(default=[], description="Liste des espaces à rechercher")
    labels: Optional[List[str]] = Field(default=[], description="Liste des labels à filtrer")
    start_date: Optional[datetime] = Field(None, description="Date de début pour la recherche")
    end_date: Optional[datetime] = Field(None, description="Date de fin pour la recherche")
    page_limit: Optional[int] = Field(default=100, description="Nombre maximum de pages à récupérer")
    max_results: Optional[int] = Field(default=1000, description="Nombre maximum de résultats")
    limit: Optional[int] = Field(default=100, description="Limite de résultats (alias pour max_results)")
    include_attachments: bool = Field(default=True, description="Inclure les pièces jointes")
    content_types: Optional[List[str]] = Field(default=["page"], description="Types de contenu à rechercher")
    title_contains: Optional[str] = Field(None, description="Filtre sur le titre")
    last_modified_days: Optional[int] = Field(None, description="Nombre de jours depuis la dernière modification")

    @field_validator('max_results')
    @classmethod
    def validate_max_results(cls, v):
        if v is not None and (v <= 0 or v > 10000):
            raise ValueError('max_results must be between 1 and 10000')
        return v

    @field_validator('last_modified_days')
    @classmethod
    def validate_last_modified_days(cls, v):
        if v is not None and v <= 0:
            raise ValueError('last_modified_days must be positive')
        return v

    def to_cql(self) -> str:
        """Convertit les critères de recherche en requête CQL Confluence."""
        cql_parts = []

        # Types de contenu
        if self.content_types:
            type_conditions = [f'type="{content_type}"' for content_type in self.content_types]
            if len(type_conditions) == 1:
                cql_parts.append(type_conditions[0])
            else:
                cql_parts.append(f"({' OR '.join(type_conditions)})")

        # Espaces
        spaces_to_use = []
        if self.spaces:
            spaces_to_use.extend(self.spaces)
        if self.space_key and self.space_key not in spaces_to_use:
            spaces_to_use.append(self.space_key)

        if spaces_to_use:
            space_conditions = [f'space="{space}"' for space in spaces_to_use]
            if len(space_conditions) == 1:
                cql_parts.append(space_conditions[0])
            else:
                cql_parts.append(f"({' OR '.join(space_conditions)})")

        # Labels
        if self.labels:
            label_conditions = [f'label="{label}"' for label in self.labels]
            cql_parts.extend(label_conditions)

        # Titre contient
        if self.title_contains:
            cql_parts.append(f'title ~ "{self.title_contains}"')

        # Dates
        if self.start_date:
            cql_parts.append(f'lastModified >= "{self.start_date.strftime("%Y-%m-%d")}"')
        if self.end_date:
            cql_parts.append(f'lastModified <= "{self.end_date.strftime("%Y-%m-%d")}"')

        # Joindre toutes les conditions avec AND
        if not cql_parts:
            return "type=page"  # Requête par défaut

        return " AND ".join(cql_parts)

class CircuitBreakerConfig(BaseModel):
    failure_threshold: int = Field(default=5, description="Nombre d'échecs avant ouverture")
    reset_timeout: float = Field(default=60.0, description="Délai avant tentative de réinitialisation (secondes)")
    reset_threshold: int = Field(default=2, description="Nombre de succès requis pour fermer le circuit")
    half_open_timeout: float = Field(default=30.0, description="Délai en état semi-ouvert (secondes)")
    exception_types: List[str] = Field(
        default=["ConnectionError", "TimeoutError"],
        description="Types d'exceptions à surveiller"
    )
    minimum_throughput: int = Field(default=10, description="Nombre minimum de requêtes avant activation")

class ThreadPoolConfig(BaseModel):
    io_thread_workers: Optional[int] = Field(default=8, description="Nombre de workers pour les opérations I/O")
    document_processing_workers: Optional[int] = Field(default=4, description="Nombre de workers pour le traitement de documents")
    api_thread_workers: Optional[int] = Field(default=3, description="Nombre de workers pour les appels API")
    thread_name_prefix: str = Field(default="ConfluenceRAG-Worker", description="Préfixe pour les noms des threads")
    max_queue_size: int = Field(default=100, description="Taille maximale de la file d'attente")

    @field_validator('io_thread_workers', 'document_processing_workers', 'api_thread_workers')
    @classmethod
    def validate_workers(cls, v):
        if v is not None:
            if v <= 0:
                raise ValueError("Worker count must be positive")
            if v > 32:  # MAX_THREAD_WORKERS
                raise ValueError(f"Worker count cannot exceed 32")
        return v

    @classmethod
    def from_env(cls) -> 'ThreadPoolConfig':
        """Crée une configuration à partir des variables d'environnement."""
        import os
        return cls(
            io_thread_workers=int(os.getenv('IO_THREAD_WORKERS', 8)),
            document_processing_workers=int(os.getenv('DOCUMENT_PROCESSING_WORKERS', 4)),
            api_thread_workers=int(os.getenv('API_THREAD_WORKERS', 3)),
            thread_name_prefix=os.getenv('THREAD_NAME_PREFIX', 'ConfluenceRAG-Worker'),
            max_queue_size=int(os.getenv('MAX_QUEUE_SIZE', 100))
        )

class StorageConfig(BaseModel):
    type: str = Field(default="filesystem", description="Type de stockage: 'filesystem', 'gcs'")
    path: Optional[str] = Field(None, description="Chemin de stockage (pour filesystem)")
    base_dir: Optional[str] = Field(None, description="Répertoire de base (pour filesystem)")
    bucket_name: Optional[str] = Field(None, description="Nom du bucket (pour gcs)")
    base_prefix: Optional[str] = Field(None, description="Préfixe de base (pour gcs)")
    bucket: Optional[str] = Field(None, description="Nom du bucket GCS (alias pour bucket_name)")
    prefix: Optional[str] = Field(None, description="Préfixe pour les objets GCS (alias pour base_prefix)")
    max_size: int = Field(default=1000000, description="Taille maximale en octets")
    max_attachment_size_mb: int = Field(default=100, description="Taille maximale des pièces jointes en MB")
    attachment_extensions_to_convert: Optional[List[str]] = Field(None, description="Extensions à convertir en texte")
    attachment_extensions_to_download_raw: Optional[List[str]] = Field(None, description="Extensions à télécharger en brut")

class ProcessingConfig(BaseModel):
    batch_size: int = Field(default=10, description="Taille des lots de traitement")
    max_threads: int = Field(default=4, description="Nombre maximum de threads")
    timeout: int = Field(default=300, description="Délai d'expiration en secondes")
    retry_count: int = Field(default=3, description="Nombre de tentatives de traitement")
    max_file_size: int = Field(default=10_000_000, description="Taille maximale des fichiers en octets")
    chunk_size: int = Field(default=1000, description="Taille des chunks de texte")
    overlap_size: int = Field(default=200, description="Taille de chevauchement entre chunks")
    max_parallel_downloads: int = Field(default=3, description="Nombre maximum de téléchargements parallèles")
    max_thread_workers: int = Field(default=4, description="Nombre maximum de workers de threads")
    enable_change_tracking: bool = Field(default=True, description="Activer le suivi des changements")
    thread_pool_config: ThreadPoolConfig = Field(default_factory=ThreadPoolConfig, description="Configuration du pool de threads")

    @classmethod
    def from_env(cls) -> 'ProcessingConfig':
        """Crée une configuration à partir des variables d'environnement."""
        import os

        # Traitement amélioré des valeurs booléennes pour enable_change_tracking
        change_tracking_value = os.getenv('CONFLUENCE_ENABLE_CHANGE_TRACKING', 'true')
        enable_change_tracking = change_tracking_value.lower() in ('true', '1', 'yes', 't', 'y')

        return cls(
            batch_size=int(os.getenv('CONFLUENCE_BATCH_SIZE', 10)),
            max_threads=int(os.getenv('CONFLUENCE_MAX_THREADS', 4)),
            timeout=int(os.getenv('CONFLUENCE_TIMEOUT', 300)),
            retry_count=int(os.getenv('CONFLUENCE_RETRY_COUNT', 3)),
            max_file_size=int(os.getenv('CONFLUENCE_MAX_FILE_SIZE', 10_000_000)),
            chunk_size=int(os.getenv('CONFLUENCE_CHUNK_SIZE', 1000)),
            overlap_size=int(os.getenv('CONFLUENCE_OVERLAP_SIZE', 200)),
            max_parallel_downloads=int(os.getenv('CONFLUENCE_MAX_PARALLEL_DOWNLOADS', 3)),
            max_thread_workers=int(os.getenv('CONFLUENCE_MAX_THREAD_WORKERS', 4)),
            enable_change_tracking=enable_change_tracking
        )

class RetryConfig(BaseModel):
    max_attempts: int = Field(default=3, description="Nombre maximum de tentatives")
    initial_delay: float = Field(default=1.0, description="Délai initial entre les tentatives (secondes)")
    max_delay: float = Field(default=60.0, description="Délai maximum entre les tentatives (secondes)")
    exponential_base: float = Field(default=2.0, description="Base pour le délai exponentiel")
    jitter: bool = Field(default=True, description="Ajouter un délai aléatoire")

    @field_validator('max_attempts')
    @classmethod
    def validate_max_attempts(cls, v):
        if v <= 0:
            raise ValueError('max_attempts must be positive')
        return v

    @field_validator('initial_delay')
    @classmethod
    def validate_initial_delay(cls, v):
        if v < 0:
            raise ValueError('initial_delay must be non-negative')
        return v

class LoggingConfig(BaseModel):
    level: str = Field(default="INFO", description="Niveau de journalisation")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Format des messages de log"
    )
    file_path: Optional[str] = Field(None, description="Chemin du fichier de log")
    max_file_size: int = Field(default=10_485_760, description="Taille maximale du fichier de log (10MB)")
    backup_count: int = Field(default=5, description="Nombre de fichiers de backup à conserver")
    console_output: bool = Field(default=True, description="Activer la sortie console")

class HealthCheckConfig(BaseModel):
    enabled: bool = Field(default=True, description="Activer les contrôles de santé")
    interval: int = Field(default=60, description="Intervalle entre les contrôles (secondes)")
    check_interval_seconds: int = Field(default=60, description="Intervalle entre les contrôles (secondes) - alias pour interval")
    timeout: float = Field(default=5.0, description="Délai d'expiration pour les contrôles")
    timeout_seconds: float = Field(default=5.0, description="Délai d'expiration pour les contrôles (secondes) - alias pour timeout")
    failure_threshold: int = Field(default=3, description="Nombre d'échecs avant notification")
    success_threshold: int = Field(default=2, description="Nombre de succès requis pour rétablissement")
    memory_threshold_percent: float = Field(default=85.0, description="Seuil d'alerte mémoire en pourcentage")
    disk_threshold_percent: float = Field(default=90.0, description="Seuil d'alerte disque en pourcentage")
    check_confluence_api: bool = Field(default=True, description="Vérifier l'API Confluence")
    check_storage: bool = Field(default=True, description="Vérifier le stockage")
    check_circuit_breakers: bool = Field(default=True, description="Vérifier les circuit breakers")
    check_thread_pools: bool = Field(default=True, description="Vérifier les thread pools")
    check_system_resources: bool = Field(default=True, description="Vérifier les ressources système")
    endpoints: List[str] = Field(
        default=["api/health", "api/status"],
        description="Points de terminaison à vérifier"
    )

class ConfluenceConfig(BaseModel):
    url: HttpUrl = Field(
        ...,
        description="URL de l'instance Confluence",
        example="https://your-domain.atlassian.net"
    )
    username: Optional[str] = Field(
        None,
        description="Nom d'utilisateur",
        min_length=1,
        example="<EMAIL>"
    )
    api_token: Optional[SecretStr] = Field(
        None,
        description="Token d'API",
        min_length=1,
        example="ATATT3xFfGF0iPZYsM6j9vKN_EXAMPLE"
    )
    pat_token: Optional[SecretStr] = Field(
        None,
        description="Personal Access Token (PAT)",
        example="ATATT3xFfGF0iPZYsM6j9vKN_EXAMPLE"
    )
    space_key: Optional[str] = Field(
        None,
        description="Clé de l'espace par défaut",
        example="TEAM"
    )
    default_space_key: Optional[str] = Field(
        None,
        description="Clé de l'espace par défaut (alias pour space_key)",
        example="TEAM"
    )
    api_version: str = Field(
        default="v2",
        description="Version de l'API Confluence"
    )
    timeout: Optional[int] = Field(
        default=30,
        description="Délai d'expiration pour les requêtes API (secondes)"
    )
    enable_parallel_pagination: bool = Field(
        default=True,
        description="Activer la pagination parallèle"
    )
    max_parallel_requests: int = Field(
        default=3,
        description="Nombre maximum de requêtes parallèles"
    )
    parallel_pagination_threshold: int = Field(
        default=200,
        description="Seuil pour activer la pagination parallèle"
    )
    circuit_breaker_config: CircuitBreakerConfig = Field(
        default_factory=CircuitBreakerConfig,
        description="Configuration du disjoncteur pour les appels API"
    )
    retry_config: RetryConfig = Field(
        default_factory=RetryConfig,
        description="Configuration des tentatives pour les appels API"
    )
    processing_config: ProcessingConfig = Field(
        default_factory=ProcessingConfig,
        description="Configuration du traitement des contenus"
    )
    storage_config: Optional[StorageConfig] = Field(
        default_factory=StorageConfig,
        description="Configuration du stockage des contenus"
    )
    logging_config: LoggingConfig = Field(
        default_factory=LoggingConfig,
        description="Configuration de la journalisation"
    )
    health_check_config: HealthCheckConfig = Field(
        default_factory=HealthCheckConfig,
        description="Configuration des contrôles de santé"
    )

    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        if not str(v).endswith('/'):
            v = str(v) + '/'
        return v

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if v and (not isinstance(v, str) or '@' not in v):
            raise ValueError('Username must be a valid email address')
        return v

    @field_validator('default_space_key', 'space_key', mode='before')
    @classmethod
    def set_space_key(cls, v, values):
        # Permet d'utiliser space_key ou default_space_key
        # Si une valeur est fournie, l'utiliser
        if v is not None:
            return v
        # Sinon, essayer de récupérer l'autre champ
        if isinstance(values, dict):
            return values.get('space_key') or values.get('default_space_key')
        return v

    @model_validator(mode='after')
    def validate_credentials(self):
        # Vérifier qu'au moins une méthode d'authentification est fournie
        pat_token = self.pat_token
        api_token = self.api_token
        username = self.username

        # Si aucun PAT token n'est fourni
        if pat_token is None:
            # Il faut alors un username ET un api_token
            if not api_token or not username:
                raise ValueError('Either pat_token must be provided, or both username and api_token must be provided')

        # Si PAT token est fourni, c'est suffisant
        return self

    @classmethod
    def from_env(cls) -> 'ConfluenceConfig':
        """Crée une configuration à partir des variables d'environnement."""
        url = os.getenv('CONFLUENCE_URL')
        if not url:
            raise ValueError('CONFLUENCE_URL environment variable is required')

        pat_token = os.getenv('CONFLUENCE_PAT_TOKEN')
        username = os.getenv('CONFLUENCE_USERNAME')
        api_token = os.getenv('CONFLUENCE_API_TOKEN')

        return cls(
            url=url,
            pat_token=SecretStr(pat_token) if pat_token else None,
            username=username,
            api_token=SecretStr(api_token) if api_token else None,
            space_key=os.getenv('CONFLUENCE_SPACE_KEY'),
            timeout=int(os.getenv('CONFLUENCE_TIMEOUT', 30))
        )

    class Config:
        json_schema_extra = {
            "example": {
                "url": "https://your-domain.atlassian.net",
                "username": "<EMAIL>",
                "api_token": "ATATT3xFfGF0example",
                "space_key": "TEAM",
                "api_version": "v2"
            }
        }


class EnvironmentConfig(BaseModel):
    """Configuration de l'environnement d'exécution."""
    active_environment: Environment = Field(default=Environment.DEV, description="Environnement actif")
    config_file_path: Optional[str] = Field(None, description="Chemin vers le fichier de configuration")
    debug_mode: bool = Field(default=False, description="Mode debug activé")

    @classmethod
    def from_env(cls) -> 'EnvironmentConfig':
        """Crée une configuration d'environnement à partir des variables d'environnement."""
        env_str = os.getenv('CONFLUENCE_RAG_ENVIRONMENT', 'dev').lower()

        try:
            environment = Environment(env_str)
        except ValueError:
            logging.warning(f"Invalid environment '{env_str}', defaulting to 'dev'")
            environment = Environment.DEV

        return cls(
            active_environment=environment,
            config_file_path=os.getenv('CONFLUENCE_RAG_CONFIG_FILE'),
            debug_mode=os.getenv('DEBUG', 'false').lower() == 'true'
        )


def get_config() -> ConfluenceConfig:
    """
    Fonction utilitaire pour obtenir une configuration Confluence.

    Returns:
        ConfluenceConfig: Configuration Confluence créée à partir des variables d'environnement
    """
    return ConfluenceConfig.from_env()
