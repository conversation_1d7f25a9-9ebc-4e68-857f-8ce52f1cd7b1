#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires adaptés pour les modèles de données Confluence.

Adapté depuis src/kbotloadscheduler/loader/confluence/tests/test_models.py
pour l'architecture kbot-load-scheduler.
"""

import unittest
from datetime import datetime, timezone

import pytest
from src.kbotloadscheduler.loader.confluence.config import SearchCriteria
from src.kbotloadscheduler.loader.confluence.models import (
    AttachmentDetail,
    ContentItem,
    SpaceInfo,
    UserInfo,
)
from pydantic import ValidationError


class TestSearchCriteriaAdapted(unittest.TestCase):
    """Tests adaptés pour la classe SearchCriteria."""

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_search_criteria_basic(self):
        """Test de création basique des critères de recherche."""
        criteria = SearchCriteria(spaces=["TEST", "DOCS"], max_results=100)

        assert criteria.spaces == ["TEST", "DOCS"]
        assert criteria.max_results == 100
        assert criteria.include_attachments is True  # valeur par défaut

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_search_criteria_with_content_types(self):
        """Test avec types de contenu spécifiques."""
        criteria = SearchCriteria(
            spaces=["TEST"], content_types=["page", "blogpost"], max_results=50
        )

        assert criteria.content_types == ["page", "blogpost"]
        assert criteria.max_results == 50

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_search_criteria_validation_max_results(self):
        """Test de validation de max_results."""
        with pytest.raises(ValidationError):
            SearchCriteria(spaces=["TEST"], max_results=0)  # Invalide

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_search_criteria_validation_empty_spaces(self):
        """Test de validation avec espaces vides."""
        # Les espaces vides sont autorisés dans SearchCriteria
        criteria = SearchCriteria(spaces=[], max_results=100)  # Autorisé
        assert criteria.spaces == []
        assert criteria.max_results == 100


class TestContentItemAdapted(unittest.TestCase):
    """Tests adaptés pour la classe ContentItem."""

    def setUp(self):
        """Configuration des tests."""
        self.sample_datetime = datetime.now(timezone.utc)
        # Créer un UserInfo pour le créateur et le dernier modificateur
        self.creator = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )
        # Créer un SpaceInfo pour l'espace
        self.space = SpaceInfo(
            id="space123", key="TEST", name="Test Space", type="global"
        )

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_content_item_basic(self):
        """Test de création basique d'un ContentItem."""
        content = ContentItem(
            id="123456",
            title="Test Page",
            type="page",
            status="current",
            space=self.space,
            version={"number": 1},
            created=self.sample_datetime,
            creator=self.creator,
            last_updated=self.sample_datetime,
            last_updater=self.creator,
            content_url="https://test.atlassian.net/rest/api/content/123456",
            web_ui_url="https://test.atlassian.net/display/TEST/Test+Page",
        )

        assert content.id == "123456"
        assert content.title == "Test Page"
        assert content.type == "page"
        assert content.space.key == "TEST"
        assert content.last_updated == self.sample_datetime

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_content_item_with_attachments(self):
        """Test de ContentItem avec attachments."""
        attachment = AttachmentDetail(
            id="att001",
            title="document.pdf",
            file_name="document.pdf",
            file_size=1024,
            media_type="application/pdf",
            created=self.sample_datetime,
            last_updated=self.sample_datetime,
            creator=self.creator,
            download_url="https://test.atlassian.net/download/att001",
            content_id="123456",
        )

        content = ContentItem(
            id="123456",
            title="Test Page",
            type="page",
            status="current",
            space=self.space,
            version={"number": 1},
            created=self.sample_datetime,
            creator=self.creator,
            last_updated=self.sample_datetime,
            last_updater=self.creator,
            content_url="https://test.atlassian.net/rest/api/content/123456",
            web_ui_url="https://test.atlassian.net/display/TEST/Test+Page",
            attachments=[attachment],
        )

        assert len(content.attachments) == 1
        assert content.attachments[0].id == "att001"
        assert content.attachments[0].title == "document.pdf"

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_content_item_validation_required_fields(self):
        """Test de validation des champs requis."""
        with pytest.raises(ValidationError):
            ContentItem(
                # id manquant
                title="Test Page",
                type="page",
                status="current",
                space=self.space,
                version={"number": 1},
                created=self.sample_datetime,
                creator=self.creator,
                last_updated=self.sample_datetime,
                last_updater=self.creator,
                content_url="https://test.atlassian.net/rest/api/content/123456",
                web_ui_url="https://test.atlassian.net/display/TEST/Test+Page",
            )


class TestAttachmentDetailAdapted(unittest.TestCase):
    """Tests adaptés pour la classe AttachmentDetail."""

    def setUp(self):
        """Configuration des tests."""
        self.sample_datetime = datetime.now(timezone.utc)
        self.creator = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_attachment_detail_basic(self):
        """Test de création basique d'un AttachmentDetail."""
        attachment = AttachmentDetail(
            id="att001",
            title="document.pdf",
            file_name="document.pdf",
            file_size=1024,
            media_type="application/pdf",
            created=self.sample_datetime,
            last_updated=self.sample_datetime,
            creator=self.creator,
            download_url="https://test.atlassian.net/download/att001",
            content_id="123456",
        )

        assert attachment.id == "att001"
        assert attachment.title == "document.pdf"
        assert attachment.download_url == "https://test.atlassian.net/download/att001"
        assert attachment.created == self.sample_datetime
        assert attachment.media_type == "application/pdf"

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_attachment_detail_with_media_type(self):
        """Test d'AttachmentDetail avec type de média."""
        attachment = AttachmentDetail(
            id="att002",
            title="image.png",
            file_name="image.png",
            file_size=2048,
            media_type="image/png",
            created=self.sample_datetime,
            last_updated=self.sample_datetime,
            creator=self.creator,
            download_url="https://test.atlassian.net/download/att002",
            content_id="123456",
        )

        assert attachment.media_type == "image/png"
        assert attachment.file_size == 2048

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_attachment_detail_validation(self):
        """Test de validation d'AttachmentDetail."""
        # Les titres vides sont autorisés dans AttachmentDetail
        attachment = AttachmentDetail(
            id="att001",
            title="",  # Titre vide autorisé
            file_name="document.pdf",
            file_size=1024,
            media_type="application/pdf",
            created=self.sample_datetime,
            last_updated=self.sample_datetime,
            creator=self.creator,
            download_url="https://test.atlassian.net/download/att001",
            content_id="123456",
        )
        assert attachment.title == ""
        assert attachment.file_name == "document.pdf"


if __name__ == "__main__":
    unittest.main()
