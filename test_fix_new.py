#!/usr/bin/env python3
"""
Test script to verify the Confluence loader fix works
"""

import sys
import os

# Add paths
sys.path.insert(0, 'src')
sys.path.insert(0, 'tests')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

def main():
    print("🧪 Testing Confluence loader fix...")
    
    try:
        # Test 1: Import all required modules
        print("\n1. Testing imports...")
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        from kbotloadscheduler.loader.confluence.config import ConfluenceConfig
        from pydantic import SecretStr
        from unittest.mock import MagicMock, patch
        print("   ✓ All imports successful")
        
        # Test 2: Test ConfluenceConfig creation with SecretStr
        print("\n2. Testing ConfluenceConfig creation...")
        config = ConfluenceConfig(
            url="https://mock-confluence.example.com",
            pat_token=SecretStr("test_token"),
            default_space_key="DOCS",
            timeout=30
        )
        print("   ✓ ConfluenceConfig created successfully")
        
        # Test 3: Test loader creation
        print("\n3. Testing loader creation...")
        mock_config = MagicMock()
        mock_config.get_confluence_credentials.return_value = {
            "pat_token": "mock_token"
        }
        
        loader = ConfluenceLoader(mock_config)
        print("   ✓ Loader created successfully")
        
        # Test 4: Test _create_confluence_config method
        print("\n4. Testing _create_confluence_config method...")
        from kbotloadscheduler.bean.beans import SourceBean
        import json
        from datetime import datetime, timezone
        
        source = SourceBean(
            id=1,
            code="test",
            label="Test",
            src_type="confluence",
            configuration=json.dumps({}),
            last_load_time=int(datetime.now(timezone.utc).timestamp()),
            load_interval=24,
            domain_code="test",
            perimeter_code="test",
            force_embedding=False
        )
        
        # This should now work with the SecretStr fix
        confluence_config = loader._create_confluence_config(source)
        print("   ✓ _create_confluence_config works correctly")
        print(f"   ✓ Config URL: {confluence_config.url}")
        print(f"   ✓ Config has PAT token: {confluence_config.pat_token is not None}")
        
        print("\n✅ All tests passed! The fix is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
