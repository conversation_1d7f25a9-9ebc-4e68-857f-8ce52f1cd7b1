# Confluence Client System

Ce système permet de récupérer et traiter du contenu depuis Confluence pour l'utiliser dans des applications de
génération augmentée par récupération (RAG).

> **🎉 NOUVEAU** : Ce module est maintenant **intégré dans kbot-load-scheduler** ! Vous pouvez l'utiliser via l'
> architecture standardisée du projet. Consultez [docs/INTEGRATION_README.md](docs/INTEGRATION_README.md) pour le guide
> d'intégration.

> **Note** : Ce fichier contient la documentation technique complète du module `confluence`. Pour une vue d'ensemble du
> projet global, consultez [../README.md](../README.md).

## 🚀 Modes d'utilisation

Ce module peut être utilisé de **deux façons** :

### 1. 🔗 **Intégré dans kbot-load-scheduler** (Recommandé)

- ✅ **Architecture standardisée** avec `AbstractLoader`
- ✅ **Gestion des secrets** via Secret Manager
- ✅ **API REST** unifiée (`/loader/list`, `/loader/document`)
- ✅ **Container de dépendances** pour injection
- ✅ **Tests intégrés** dans la suite de tests du projet

**Guide d'utilisation** : [docs/INTEGRATION_README.md](docs/INTEGRATION_README.md)

### 2. 📦 **Module standalone** (Usage avancé)

- ✅ **Contrôle total** de la configuration
- ✅ **Utilisation programmatique** directe
- ✅ **Personnalisation avancée** des workflows
- ✅ **Déploiement indépendant**

**Guide d'utilisation** : Voir sections ci-dessous

## Fonctionnalités Principales

### 🚀 Architecture Synchrone et Performance

- **Client synchrone unifié** : Architecture entièrement synchrone basée sur `requests` pour une intégration parfaite
- **Parallélisme contrôlé** : Utilisation de `ThreadPoolExecutor` pour un parallélisme optimal sans overhead asyncio
- **Session HTTP réutilisable** : Optimisation des connexions pour réduire la latence et améliorer le débit
- **Téléchargements parallèles** : Téléchargement des pièces jointes en parallèle avec un nombre configurable de
  téléchargements simultanés
- **Circuit breaker** : Protection contre les défaillances avec retry automatique et backoff exponentiel
- **Configuration adaptative** : Paramètres ajustables selon les besoins de performance

### 🔐 Authentification et Sécurité

- **Authentification avec token PAT** : Utilisation du Personal Access Token (PAT) pour l'authentification à Confluence
- **Protection des logs** : Masquage automatique des tokens et informations sensibles dans les logs
- **Validation des entrées** : Vérification des paramètres de configuration et des données

### 📊 Récupération et Traitement

- **Récupération de contenu** : Pages, blogs, et leurs pages enfants depuis Confluence via l'API REST
- **Navigation hiérarchique** : Récupération récursive des pages enfants jusqu'à une profondeur configurable
- **Filtrage flexible** : Basé sur les espaces, les étiquettes, les types de contenu et la date de dernière modification
- **Critères de recherche personnalisables** : Configuration via un fichier JSON local ou stocké sur Google Cloud
  Storage
- **Gestion des pièces jointes** : Téléchargement et traitement des pièces jointes (PDF, DOCX, PPTX, TXT, etc.)

### 💾 Stockage et Suivi

- **Options de stockage multiples** : Stockage sur système de fichiers local ou Google Cloud Storage (GCS)
- **Détection des changements** : Suivi des modifications entre les synchronisations pour un traitement efficace
- **Stratégie de mise à jour intelligente** : Traitement sélectif des contenus et pièces jointes modifiés uniquement
- **Hachage cryptographique** : Utilisation de SHA-256 pour détecter avec précision les modifications de contenu
- **Synchronisation incrémentale** : Optimisation des performances avec traitement uniquement des éléments modifiés

### 🔧 Monitoring et Debugging

- **Logging structuré** : Journalisation détaillée avec identifiants de corrélation pour le suivi et le débogage
- **Mécanismes de résilience** : Retry avec backoff exponentiel, Circuit Breaker Pattern
- **Architecture modulaire** : Conception synchrone pour une meilleure simplicité et maintenabilité
- **Debugging simplifié** : Stack traces claires sans complexité asyncio

### 🏗️ Architecture de Traitement Modulaire Synchrone

- **Refactorisation complète** : Architecture entièrement synchrone avec modules spécialisés
- **Séparation des responsabilités** : Chaque module a une fonction claire et définie
- **Maintenabilité améliorée** : Code plus lisible, testable et extensible
- **Migration terminée** : Tous les composants asyncio ont été supprimés pour une architecture unifiée
- **Traitement spécialisé** : Support avancé pour draw.io, PDF, DOCX, XLSX et autres formats

### 🏥 Health Checks et Surveillance

- **Health checks complets** : Surveillance de tous les composants critiques du système
- **Monitoring des ressources** : Surveillance de la mémoire, disque, et utilisation des threads
- **Tests de connectivité** : Vérification automatique de l'API Confluence et du stockage
- **Endpoints Kubernetes** : Support natif des probes `/health`, `/ready`, `/live`
- **Seuils configurables** : Alertes personnalisables selon l'environnement
- **Cache intelligent** : Optimisation des performances avec mise en cache des résultats

## Architecture

### Modules principaux

- **`sync_client.py`** - Client Confluence synchrone avec optimisations de performance
- **`config.py`** - Configuration et modèles de données
- **`processing/`** - **Architecture modulaire de traitement** (nouveau !)
    - `document_extractors.py` - Extraction de texte des documents
    - `drawio_processor.py` - Traitement spécialisé draw.io
    - `sync_attachment_processor.py` - Traitement synchrone des pièces jointes
    - `content_chunker.py` - Découpage de texte en chunks
    - `sync_content_retriever.py` - Orchestration synchrone de récupération
- **`storage.py`** - Stockage sur filesystem ou GCS
- **`orchestrator.py`** - Orchestration du processus de synchronisation
- **`thread_pool_manager.py`** - Gestion optimisée des pools de threads
- **`tracking.py`** - Suivi des changements
- **`logging_utils.py`** - Journalisation structurée et sécurisée
- **`health_check.py`** - Système de health checks et surveillance

### Modules utilitaires

- **`circuit_breaker.py`** - Pattern Circuit Breaker pour la résilience
- **`exceptions.py`** - Exceptions personnalisées
- **`utils.py`** - Utilitaires divers
- **`models.py`** - Modèles de données Pydantic

## 🔄 Architecture Synchrone Unifiée

Le module Confluence utilise maintenant une **architecture entièrement synchrone** pour une intégration parfaite avec
kbot-load-scheduler et des performances optimisées.

### Composants Synchrones

- **`sync_client.py`** - Client Confluence synchrone basé sur `requests`
- **`orchestrator.py`** - `SyncOrchestrator` pour l'orchestration synchrone
- **`processing/sync_content_retriever.py`** - Récupération de contenu synchrone
- **`processing/sync_attachment_processor.py`** - Traitement de pièces jointes synchrone

### Avantages de l'Architecture Synchrone

- ✅ **Simplicité** : Plus de gestion d'`asyncio` ou de boucles d'événements
- ✅ **Performance** : Parallélisme contrôlé avec `ThreadPoolExecutor`
- ✅ **Debugging** : Stack traces plus claires et debugging simplifié
- ✅ **Intégration** : Compatible avec les systèmes synchrones existants
- ✅ **Maintenance** : Code plus linéaire et prévisible
- ✅ **Cohérence** : Architecture unifiée avec le reste de kbot-load-scheduler

### Utilisation Synchrone

```python
from kbotloadscheduler.loader.confluence import (
    ConfluenceConfig, SearchCriteria, StorageConfig,
    SyncOrchestrator
)


def sync_confluence():
    # Configuration
    config = ConfluenceConfig(
        url="https://your-instance.atlassian.net",
        pat_token="your_pat_token"
    )

    criteria = SearchCriteria(
        spaces=["SPACE1", "SPACE2"],
        max_results=100,
        include_attachments=True
    )

    storage = StorageConfig(
        type="gcs",
        bucket_name="your-bucket",
        base_prefix="confluence-data/"
    )

    # Orchestration synchrone - plus simple !
    orchestrator = SyncOrchestrator(config, criteria, storage)
    result = orchestrator.run()

    print(f"Synchronisé {result['total_content_items']} éléments")


# Exécution directe
sync_confluence()
```

### Migration Terminée

Les composants asyncio ont été **complètement supprimés**. L'architecture est maintenant entièrement synchrone :

- ✅ `SyncConfluenceClient` - Client principal
- ✅ `SyncContentRetriever` - Récupération de contenu
- ✅ `SyncAttachmentProcessor` - Traitement des pièces jointes
- ✅ `SyncOrchestrator` - Orchestration complète

**Plus besoin de migration** - tous les composants sont synchrones !

## Installation

### 🔗 Installation avec kbot-load-scheduler (Recommandé)

Si vous utilisez le module intégré dans kbot-load-scheduler, les dépendances sont déjà incluses dans`requirements.txt` :

```bash
# Les dépendances Confluence sont automatiquement installées
pip install -r requirements.txt
```

**Dépendances ajoutées** :

- `requests>=2.28.0` - Client HTTP synchrone
- `beautifulsoup4>=4.11.0` - Parsing HTML/XML
- `lxml>=4.9.0` - Parser XML performant
- `pypdf>=3.0.0` - Extraction PDF
- `python-docx>=0.8.11` - Traitement documents Word
- `openpyxl>=3.1.0` - Traitement fichiers Excel

### 📦 Installation standalone

```bash
# Créer un environnement virtuel
python -m venv .venv
source .venv/bin/activate  # Sur Linux/Mac
# ou
.venv\Scripts\activate     # Sur Windows

# Installer les dépendances avec requirements.txt
pip install -r requirements.txt

# Pour le développement (inclut les outils de test)
pip install -r requirements-dev.txt

# Installation complète
pip install -r requirements.txt
```

> **📋 Gestion des dépendances** : Ce projet utilise `requirements.txt` pour la gestion des dépendances, en cohérence
> avec l'architecture kbot-load-scheduler.

## Configuration

### 🔗 Configuration pour kbot-load-scheduler (Recommandé)

La configuration se fait via les variables d'environnement et les fichiers de configuration de kbot-load-scheduler :

1. **Variables d'environnement** dans le fichier `.env` du projet :

```bash
# Configuration Confluence
CONFLUENCE_URL=https://your-instance.atlassian.net
CONFLUENCE_PAT_TOKEN=your_pat_token
```

2. **Configuration de source** via l'API ou les fichiers de configuration :

```json
{
  "src_type": "confluence",
  "configuration": {
    "spaces": [
      "DOCS",
      "TECH"
    ],
    "max_results": 1000,
    "include_attachments": true,
    "content_types": [
      "page",
      "blogpost"
    ]
  }
}
```

### 📦 Configuration standalone

Pour une utilisation standalone, personnalisez les critères de recherche :

```json
{
  "spaces": [
    "VOTRE_ESPACE"
  ],
  "labels": [
    "documentation",
    "important"
  ],
  "types": [
    "page",
    "blogpost"
  ],
  "date_min": "2023-01-01",
  "date_max": "2023-12-31",
  "creators": [
    "user1",
    "user2"
  ],
  "keywords": [
    "architecture",
    "microservices"
  ],
  "exclude_labels": [
    "obsolete",
    "archived"
  ],
  "max_results": 100,
  "include_attachments": true,
  "attachment_types": [
    "pdf",
    "docx",
    "xlsx",
    "pptx"
  ],
  "include_children": true,
  "max_children_depth": 3
}
```

Les paramètres `include_children` et `max_children_depth` contrôlent la récupération récursive des pages enfants :

- `include_children` : Active ou désactive la récupération des pages enfants (défaut: true)
- `max_children_depth` : Définit la profondeur maximale de récupération (1 = enfants directs uniquement, 2 = enfants et
  petits-enfants, etc.)

4. Configuration de la stratégie de mise à jour dans le fichier `.env` :

```
# Configuration de traitement
CHUNK_SIZE=1000                # Taille des chunks de texte
OVERLAP_SIZE=200               # Chevauchement entre les chunks
MAX_PARALLEL_DOWNLOADS=5       # Nombre maximum de téléchargements simultanés
MAX_THREAD_WORKERS=5           # Nombre maximum de workers pour le traitement des fichiers

# Configuration de pagination parallèle
ENABLE_PARALLEL_PAGINATION=true             # Activer/désactiver la pagination parallèle
MAX_PARALLEL_REQUESTS=3                     # Nombre maximum de requêtes simultanées
PARALLEL_PAGINATION_THRESHOLD=200          # Seuil minimum de résultats pour activer la parallélisation

# Configuration de stockage
STORAGE_TYPE=filesystem        # filesystem ou gcs
OUTPUT_DIR=output_data_dir
INCLUDE_ATTACHMENTS=true
ATTACHMENT_EXTENSIONS_TO_CONVERT=.pdf,.docx,.txt
MAX_ATTACHMENT_SIZE_MB=50

# Configuration de suivi
SYNC_REPORT_PATH=last_sync_report.json
```

## Utilisation

### 🔗 Utilisation intégrée dans kbot-load-scheduler

#### Via l'API REST

```bash
# Lister les documents Confluence
curl -X POST "http://localhost:8080/loader/list/{perimeter_code}" \
  -H "Content-Type: application/json" \
  -d '{"get_list_file": "path/to/confluence_source_config.json"}'

# Récupérer un document spécifique
curl -X POST "http://localhost:8080/loader/document/{perimeter_code}" \
  -H "Content-Type: application/json" \
  -d '{"document_get_file": "path/to/document_config.json"}'
```

#### Via le code Python

```python
from kbotloadscheduler.dependency.container import Container
from kbotloadscheduler.bean.beans import SourceBean

# Initialiser le container
container = Container()
loader_manager = container.loader_manager()

# Récupérer le loader Confluence
confluence_loader = loader_manager.get_loader("confluence")

# Configuration de la source
source = SourceBean(
    src_type="confluence",
    configuration='{"spaces": ["DOCS"], "max_results": 100}',
    # ... autres champs
)

# Utilisation
documents = confluence_loader.get_document_list(source)
metadata = confluence_loader.get_document(source, document, output_path)
```

**Configuration de source pour kbot-load-scheduler** :

```json
{
  "src_type": "confluence",
  "configuration": {
    "spaces": [
      "DOCS",
      "TECH"
    ],
    "max_results": 1000,
    "include_attachments": true,
    "content_types": [
      "page",
      "blogpost"
    ],
    "last_modified_days": 30,
    "labels": [
      "public"
    ],
    "exclude_labels": [
      "draft"
    ]
  }
}
```

Voir [docs/INTEGRATION_README.md](docs/INTEGRATION_README.md) pour la documentation complète de l'intégration.

### 📦 Utilisation standalone

#### Synchronisation de base

```python
# Exemple de script de synchronisation
from kbotloadscheduler.loader.confluence import (
    SyncOrchestrator, ConfluenceConfig, SearchCriteria, StorageConfig
)

config = ConfluenceConfig(
    url="https://your-instance.atlassian.net",
    pat_token="your_pat_token"
)

criteria = SearchCriteria(
    spaces=["DOCS"],
    max_results=100,
    include_attachments=True
)

storage = StorageConfig(
    storage_type="filesystem",
    output_dir="./confluence_data"
)

orchestrator = SyncOrchestrator(config, criteria, storage)
result = orchestrator.run()
```

### Utilisation avec Google Cloud Storage

#### Pour stocker les données sur GCS

Configurez les variables suivantes dans votre fichier `.env` :

```
STORAGE_TYPE=gcs
GCS_BUCKET_NAME=votre-bucket-gcs
GCS_BASE_PREFIX=confluence
```

#### Configuration GCS dans kbot-load-scheduler

La configuration GCS se fait via les variables d'environnement du projet :

```bash
# Configuration GCS
GCS_BUCKET_NAME=votre-bucket-gcs
GCS_BASE_PREFIX=confluence-data

# Configuration Confluence
STORAGE_TYPE=gcs
```

### Utilisation du module Python

```python
from kbotloadscheduler.loader.confluence import (
    SyncConfluenceClient, ConfluenceConfig, SearchCriteria
)

# Configuration
config = ConfluenceConfig(
    url="https://your-confluence.atlassian.net",
    pat_token="your-pat-token"
)

criteria = SearchCriteria(
    spaces=["SPACE1", "SPACE2"],
    content_types=["page"],
    max_results=100
)

# Utilisation synchrone - plus simple !
with SyncConfluenceClient(config) as client:
    results = client.search_content(criteria)
    for page in results:
        print(f"Page: {page.title}")
```

📖 **Pour une utilisation complète**, consultez :

- **[docs/INTEGRATION_README.md](docs/INTEGRATION_README.md)** - Guide d'intégration avec kbot-load-scheduler
- **[docs/SYNC_CLIENT_IMPLEMENTATION.md](docs/SYNC_CLIENT_IMPLEMENTATION.md)** - Détails techniques de l'architecture
  synchrone
- **[docs/MIGRATION_COMPLETE.md](docs/MIGRATION_COMPLETE.md)** - Guide de la migration terminée

Ces guides couvrent :

- Configuration étape par étape
- Utilisation programmatique avancée
- Architecture synchrone
- Optimisations de performance
- Intégration avec kbot-load-scheduler

## 🚀 Optimisations de Performance

Le système intègre plusieurs optimisations avancées pour maximiser les performances :

### Pagination Parallèle

Pour les gros volumes de données (>200 résultats), le système utilise automatiquement la pagination parallèle :

```python
# Configuration optimisée pour gros volumes
ENABLE_PARALLEL_PAGINATION = true
MAX_PARALLEL_REQUESTS = 3
PARALLEL_PAGINATION_THRESHOLD = 200
```

**Avantages** :

- 40-70% d'amélioration des performances pour les gros volumes
- Récupération simultanée de plusieurs pages
- Fallback automatique vers pagination séquentielle en cas d'erreur

### Session HTTP Réutilisable

Le client Confluence synchrone utilise une session HTTP réutilisable pour optimiser les connexions :

```python
# Utilisation recommandée avec context manager
with SyncConfluenceClient(config) as client:
    results = client.search_content(criteria)
    # Session fermée automatiquement
```

**Avantages** :

- Réduction de l'overhead de connexion
- Amélioration du débit pour les recherches multiples
- Gestion automatique des ressources
- Plus simple sans gestion asyncio

### Configuration de Performance

Ajustez les paramètres selon vos besoins :

| Paramètre                       | Valeur Conservative | Valeur Équilibrée | Valeur Agressive |
|---------------------------------|---------------------|-------------------|------------------|
| `MAX_PARALLEL_REQUESTS`         | 2                   | 3                 | 5                |
| `PARALLEL_PAGINATION_THRESHOLD` | 300                 | 200               | 100              |
| `MAX_PARALLEL_DOWNLOADS`        | 3                   | 5                 | 8                |

### Tests de Performance

```bash
# Tests unitaires synchrones
cd src/kbotloadscheduler/loader/confluence/tests
python -m pytest test_sync_client.py -v
python -m pytest test_sync_orchestrator.py -v
python -m pytest test_performance_optimization.py -v

# Tests d'intégration
python -m pytest test_sync_integration.py -v
```

## 🏥 Health Checks et Surveillance

Le système intègre un système complet de health checks pour surveiller l'état opérationnel de tous les composants
critiques.

### Configuration des Health Checks

Configurez les health checks dans votre fichier `.env` :

```bash
# Activation des health checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Seuils d'alerte
HEALTH_MEMORY_THRESHOLD=85.0
HEALTH_DISK_THRESHOLD=90.0
HEALTH_ERROR_RATE_THRESHOLD=10.0

# Configuration des checks spécifiques
HEALTH_CHECK_CONFLUENCE=true
HEALTH_CHECK_STORAGE=true
HEALTH_CHECK_CIRCUIT_BREAKERS=true
HEALTH_CHECK_THREAD_POOLS=true
HEALTH_CHECK_SYSTEM_RESOURCES=true
```

### Types de Health Checks

#### 🖥️ Ressources Système

- **Mémoire** : Surveillance de l'utilisation RAM avec seuils configurables
- **Disque** : Monitoring de l'espace disque disponible
- **Performance** : Mesure des temps de réponse

#### 🧵 Thread Pools

- **Utilisation** : Pourcentage d'utilisation des pools de threads
- **Surcharge** : Détection automatique des pools surchargés (>90%)
- **Statistiques** : Threads actifs, taille des queues

#### 🌐 API Confluence

- **Connectivité** : Test de connexion avec timeout configurable
- **Authentification** : Validation des tokens d'accès
- **Latence** : Mesure des temps de réponse

#### 💾 Stockage

- **Lecture/Écriture** : Tests d'accès au stockage
- **Performance** : Mesure des temps d'accès
- **Compatibilité** : Support FileSystem et Google Cloud Storage

#### ⚡ Circuit Breakers

- **État** : Monitoring des états CLOSED/OPEN/HALF_OPEN
- **Taux d'erreur** : Surveillance des échecs et récupération
- **Statistiques** : Compteurs de succès/échecs par service

### Endpoints de Health Check

Le système expose plusieurs endpoints pour différents cas d'usage :

```bash
# Health check complet
curl http://localhost:8000/health

# Readiness check (Kubernetes)
curl http://localhost:8000/ready

# Liveness check (Kubernetes)
curl http://localhost:8000/live

# Health check détaillé (force nouveau check)
curl http://localhost:8000/health/detailed
```

### Réponse Health Check

```json
{
  "overall_status": "healthy",
  "timestamp": "2023-12-07T10:30:00Z",
  "checks": [
    {
      "name": "system_resources",
      "status": "healthy",
      "message": "Ressources normales: Mémoire 45.2%, Disque 23.1%",
      "details": {
        "memory": {
          "used_percent": 45.2,
          "available_gb": 8.7,
          "total_gb": 16.0
        },
        "disk": {
          "used_percent": 23.1,
          "free_gb": 120.5,
          "total_gb": 156.8
        }
      },
      "duration_ms": 12.5
    }
  ],
  "summary": {
    "total_checks": 5,
    "healthy": 4,
    "degraded": 1,
    "unhealthy": 0,
    "unknown": 0
  }
}
```

### États de Santé

- **🟢 healthy** : Tous les composants fonctionnent normalement
- **🟡 degraded** : Fonctionnement avec performance réduite
- **🔴 unhealthy** : Composants critiques en échec
- **⚪ unknown** : État indéterminable

### Intégration Kubernetes

```yaml
# Liveness Probe
livenessProbe:
  httpGet:
    path: /live
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10

# Readiness Probe
readinessProbe:
  httpGet:
    path: /ready
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
```

### Utilisation Programmatique

```python
from confluence.health_check import HealthChecker
from confluence.config import get_config

# Configuration
config = get_config()
health_checker = HealthChecker(config.health_check)

# Exécution d'un health check
report = await health_checker.check_system_health(
    confluence_config=config.confluence,
    storage_provider=storage_provider,
    circuit_breaker=circuit_breaker
)

# Analyse du résultat
if report.overall_status.value == "healthy":
    print("✅ Système en bonne santé")
elif report.overall_status.value == "degraded":
    print("⚠️ Système dégradé - surveillance requise")
else:
    print("❌ Système défaillant - intervention nécessaire")
```

### Tests des Health Checks

```bash
# Test complet du système de health checks
python test_health_checks.py

# Test des endpoints via l'API de monitoring
python monitoring_app.py
# Puis tester: curl http://localhost:8000/health
```

Voir [docs/HEALTH_CHECK_GUIDE.md](docs/HEALTH_CHECK_GUIDE.md) pour la documentation complète.

## 🔒 Sécurité

Le système intègre plusieurs mécanismes de sécurité :

- **Authentification sécurisée** : Support des Personal Access Tokens (PAT) et API tokens
- **Protection des logs** : Masquage automatique des tokens et informations sensibles dans les logs
- **Filtrage de sécurité** : Interception et nettoyage de tous les messages de log
- **Validation des entrées** : Vérification des paramètres de configuration
- **Gestion des erreurs** : Exceptions personnalisées avec informations contextuelles nettoyées
- **Timeouts configurables** : Protection contre les requêtes bloquées
- **Retry avec backoff** : Évite la surcharge des serveurs en cas d'erreur

### Sécurité des logs

Le système protège automatiquement les informations sensibles dans les logs :

```python
# Les tokens sont automatiquement masqués
logger.error("Erreur avec token ABC123XYZ456789")
# Résultat: "Erreur avec token ***TOKEN***"

# Les headers d'authentification sont nettoyés
logger.debug("Authorization: Bearer SECRET_TOKEN")
# Résultat: "Authorization: Bearer ***TOKEN***"
```

Voir [docs/securite_logs.md](docs/securite_logs.md) pour plus de détails.

## Tests

### 🔗 Tests d'intégration kbot-load-scheduler

```bash
# Tests du ConfluenceLoader intégré
pytest tests/loader/test_confluence_loader.py -v

# Tests complets du projet avec Confluence
pytest tests/ -v
```

### 📦 Tests du module standalone

```bash
# Tests unitaires
python -m pytest tests/ -v

# Tests de performance
cd src/kbotloadscheduler/loader/confluence/tests
python -m pytest test_performance_optimization.py -v

# Tests d'intégration avec vraie instance Confluence
python -m pytest test_real_confluence_integration.py -v
```

## Documentation

Consultez le répertoire `docs/` pour la documentation détaillée :

### 📖 Guides d'Utilisation

- **[docs/INTEGRATION_README.md](docs/INTEGRATION_README.md)** - Guide d'intégration avec kbot-load-scheduler
- **[docs/MIGRATION_COMPLETE.md](docs/MIGRATION_COMPLETE.md)** - Guide de la migration terminée

### 🏗️ Architecture et Traitement

- **[docs/SYNC_CLIENT_IMPLEMENTATION.md](docs/SYNC_CLIENT_IMPLEMENTATION.md)** - Détails techniques de l'architecture
  synchrone
- **[processing/README.md](processing/README.md)** - Documentation du module processing synchrone
- **[API Reference - Traitement](docs/PROCESSING_API_REFERENCE.md)** - Documentation complète de l'API
- **[Guide de Développement - Traitement](docs/PROCESSING_DEVELOPMENT_GUIDE.md)** - Développement et extension des
  processeurs
- [Guide de pagination parallèle](docs/PARALLEL_PAGINATION_GUIDE.md)
- [Optimisations de performance](docs/PERFORMANCE_OPTIMIZATION.md)
- [Gestion des pools de threads](docs/THREAD_POOL_OPTIMIZATION.md)

### 🏥 Monitoring et Sécurité

- [Guide des Health Checks](docs/HEALTH_CHECK_GUIDE.md)
- [Journalisation structurée](docs/journalisation_structuree.md)
- [Sécurité des logs](docs/securite_logs.md)
- [Circuit Breaker](docs/circuit_breaker.md)

## Exemples

Consultez la documentation pour des exemples d'utilisation :

- **[docs/MIGRATION_COMPLETE.md](docs/MIGRATION_COMPLETE.md)** - Exemples d'utilisation synchrone
- **[docs/INTEGRATION_README.md](docs/INTEGRATION_README.md)** - Exemples d'intégration avec kbot-load-scheduler
- **Tests d'intégration** - Exemples pratiques dans `tests/test_real_confluence_integration.py`

## Version

Version actuelle : 1.2.0

## Scripts utilitaires

Le répertoire `scripts/` (à la racine du projet) contient des outils de développement :

- **`setup_confluence_secrets.py`** - Configuration des secrets Confluence pour les tests

```bash
# Configuration interactive
python scripts/setup_confluence_secrets.py --interactive

# Configuration depuis variables d'environnement
python scripts/setup_confluence_secrets.py --from-env

# Vérification des secrets
python scripts/setup_confluence_secrets.py --verify
```

## Rapports

Le répertoire `reports/` contient les rapports d'analyse :

## Contribution

Pour contribuer à ce module, veuillez consulter la documentation de développement dans `docs/`.
