#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour valider les health checks du système RAG Confluence.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime

from dotenv import load_dotenv
from ..health_check import <PERSON><PERSON>he<PERSON>, HealthStatus
from ..config import get_config, HealthCheckConfig
from ..storage import get_storage_provider

# Ajouter le répertoire racine du projet au path pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".."))

# Configuration du logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_health_checker_basic():
    """Test basique du health checker."""
    logger.info("=== Test basique du Health Checker ===")

    try:
        # Configuration minimale pour les tests
        config = HealthCheckConfig(
            enabled=True,
            check_interval_seconds=10,
            timeout_seconds=5,
            check_confluence_api=False,  # Désactiver pour le test basique
            check_storage=False,
            check_circuit_breakers=False,
        )

        health_checker = HealthChecker(config)

        # Exécuter les health checks
        report = await health_checker.check_system_health()

        # Afficher les résultats
        logger.info(f"Statut global: {report.overall_status.value}")
        logger.info(f"Nombre de checks: {len(report.checks)}")

        for check in report.checks:
            status_icon = (
                "✅"
                if check.status == HealthStatus.HEALTHY
                else "⚠️" if check.status == HealthStatus.DEGRADED else "❌"
            )
            logger.info(
                f"  {status_icon} {check.name}: {check.message} ({check.duration_ms:.1f}ms)"
            )

        return report.overall_status != HealthStatus.UNHEALTHY

    except Exception as e:
        logger.error(f"Erreur lors du test basique: {e}")
        return False


def test_health_checker_full():
    """Test complet du health checker avec configuration réelle."""
    logger.info("=== Test complet du Health Checker ===")

    try:
        # Charger la configuration complète
        app_config = get_config()
        health_checker = HealthChecker(app_config.health_check)

        # Initialiser le stockage si configuré
        storage_provider = None
        try:
            if hasattr(app_config, "storage") and app_config.storage:
                storage_provider = get_storage_provider(
                    "filesystem", base_dir="test_output"
                )
            else:
                # Configuration par défaut pour les tests
                storage_provider = get_storage_provider(
                    "filesystem", base_dir="test_output"
                )
        except Exception as e:
            logger.warning(f"Impossible d'initialiser le stockage: {e}")

        # Exécuter les health checks complets
        report = await health_checker.check_system_health(
            confluence_config=app_config.confluence, storage_provider=storage_provider
        )

        # Afficher les résultats détaillés
        logger.info(f"Statut global: {report.overall_status.value}")
        logger.info(f"Timestamp: {report.timestamp}")
        logger.info(f"Nombre de checks: {len(report.checks)}")

        print("\n" + "=" * 60)
        print(f"RAPPORT DE SANTÉ - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print(f"Statut global: {report.overall_status.value.upper()}")
        print(f"Checks effectués: {len(report.checks)}")
        print("-" * 60)

        for check in report.checks:
            if check.status == HealthStatus.HEALTHY:
                status_icon = "✅ SAIN"
            elif check.status == HealthStatus.DEGRADED:
                status_icon = "⚠️  DÉGRADÉ"
            elif check.status == HealthStatus.UNHEALTHY:
                status_icon = "❌ DÉFAILLANT"
            else:
                status_icon = "❓ INCONNU"

            print(f"{status_icon:12} | {check.name:20} | {check.message}")
            print(f"{'':12} | {'':20} | Durée: {check.duration_ms:.1f}ms")

            if check.details:
                for key, value in check.details.items():
                    if isinstance(value, dict):
                        print(f"{'':12} | {'':20} | {key}:")
                        for sub_key, sub_value in value.items():
                            print(f"{'':12} | {'':20} |   {sub_key}: {sub_value}")
                    else:
                        print(f"{'':12} | {'':20} | {key}: {value}")
            print("-" * 60)

        # Résumé
        summary = report.to_dict()["summary"]
        print(
            f"RÉSUMÉ: {summary['healthy']} sains, {summary['degraded']} dégradés, "
            f"{summary['unhealthy']} défaillants, {summary['unknown']} inconnus"
        )
        print("=" * 60)

        return report.overall_status != HealthStatus.UNHEALTHY

    except Exception as e:
        logger.error(f"Erreur lors du test complet: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_health_checker_cache():
    """Test du système de cache des health checks."""
    logger.info("=== Test du cache des Health Checks ===")

    try:
        config = HealthCheckConfig(
            enabled=True,
            check_interval_seconds=5,  # Cache de 5 secondes
            timeout_seconds=3,
        )

        health_checker = HealthChecker(config)

        # Premier check
        logger.info("Premier health check...")
        start_time = datetime.now()
        await health_checker.check_system_health()  # On exécute sans stocker le résultat
        duration1 = (datetime.now() - start_time).total_seconds()

        # Deuxième check immédiat (devrait utiliser le cache)
        logger.info("Deuxième health check (cache attendu)...")
        start_time = datetime.now()
        report2 = health_checker.get_cached_report()
        duration2 = (datetime.now() - start_time).total_seconds()

        if report2:
            logger.info(
                f"✅ Cache utilisé - Durée: {duration2:.3f}s vs {duration1:.3f}s"
            )
            return True
        else:
            logger.warning("⚠️ Cache non utilisé")
            return False

    except Exception as e:
        logger.error(f"Erreur lors du test de cache: {e}")
        return False


def main():
    """Fonction principale de test."""
    logger.info("🚀 Démarrage des tests de Health Check")

    # Charger les variables d'environnement
    load_dotenv()

    results = []

    # Test 1: Health checker basique
    logger.info("\n" + "=" * 50)
    result1 = await test_health_checker_basic()
    results.append(("Health Checker Basique", result1))

    # Test 2: Health checker complet
    logger.info("\n" + "=" * 50)
    result2 = await test_health_checker_full()
    results.append(("Health Checker Complet", result2))

    # Test 3: Cache
    logger.info("\n" + "=" * 50)
    result3 = await test_health_checker_cache()
    results.append(("Cache Health Checks", result3))

    # Résumé des tests
    logger.info("\n" + "=" * 50)
    logger.info("RÉSUMÉ DES TESTS")
    logger.info("=" * 50)

    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        logger.info(f"{status:10} | {test_name}")
        if not result:
            all_passed = False

    logger.info("=" * 50)

    if all_passed:
        logger.info("🎉 Tous les tests sont passés avec succès!")
        return 0
    else:
        logger.error("💥 Certains tests ont échoué")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Tests interrompus par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erreur inattendue: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
