#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Implémentation du Circuit Breaker Pattern pour le système RAG Confluence.
"""

import functools
import logging
import time
from datetime import datetime
from enum import Enum
from typing import Callable, TypeVar, Any, Optional, Dict, Union, Type, cast, Awaitable

from .config import CircuitBreakerConfig
from .exceptions import CircuitOpenError

# Type variables pour le décorateur
T = TypeVar("T")
F = TypeVar("F", bound=Callable[..., Awaitable[Any]])
ExceptionType = Union[Type[Exception], tuple[Type[Exception], ...]]


class CircuitState(Enum):
    """États possibles du circuit."""

    CLOSED = "CLOSED"  # Circuit fermé, les appels sont autorisés
    OPEN = "OPEN"  # Circuit ouvert, les appels sont bloqués
    HALF_OPEN = (
        "HALF_OPEN"  # Circuit semi-ouvert, quelques appels sont autorisés pour tester
    )


class CircuitBreaker:
    """
    Implémentation du Circuit Breaker Pattern.

    Le Circuit Breaker protège les systèmes contre les défaillances en cascade en
    détectant les échecs et en bloquant temporairement les appels à un service défaillant.
    """

    def __init__(self, config: CircuitBreakerConfig):
        """
        Initialise le Circuit Breaker avec la configuration spécifiée.

        Args:
            config: Configuration du Circuit Breaker
        """
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        self.success_count = 0
        self.last_state_change_time = time.time()
        self.logger = logging.getLogger(__name__)

        # Dictionnaire pour stocker les statistiques par service/méthode
        self.stats: Dict[str, Dict[str, Any]] = {}

    def record_success(self, service_name: str = "default") -> None:
        """
        Enregistre un appel réussi.

        Args:
            service_name: Nom du service ou de la méthode appelée
        """
        if service_name not in self.stats:
            self.stats[service_name] = {
                "success_count": 0,
                "failure_count": 0,
                "last_call_time": 0,
                "last_failure_time": 0,
            }

        self.stats[service_name]["success_count"] += 1
        self.stats[service_name]["last_call_time"] = time.time()

        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.reset_threshold:
                self._change_state(CircuitState.CLOSED)
                self.failure_count = 0
                self.success_count = 0

    def record_failure(self, service_name: str = "default") -> None:
        """
        Enregistre un appel échoué.

        Args:
            service_name: Nom du service ou de la méthode appelée
        """
        if service_name not in self.stats:
            self.stats[service_name] = {
                "success_count": 0,
                "failure_count": 0,
                "last_call_time": 0,
                "last_failure_time": 0,
            }

        self.stats[service_name]["failure_count"] += 1
        self.stats[service_name]["last_failure_time"] = time.time()
        self.stats[service_name]["last_call_time"] = time.time()

        current_time = time.time()
        self.last_failure_time = current_time

        if self.state == CircuitState.CLOSED:
            self.failure_count += 1
            if self.failure_count >= self.config.failure_threshold:
                self._change_state(CircuitState.OPEN)
        elif self.state == CircuitState.HALF_OPEN:
            self._change_state(CircuitState.OPEN)

    def allow_request(self) -> bool:
        """
        Détermine si une requête doit être autorisée en fonction de l'état du circuit.

        Returns:
            True si la requête est autorisée, False sinon
        """
        current_time = time.time()

        if self.state == CircuitState.OPEN:
            # Vérifier si le temps d'attente est écoulé pour passer en état semi-ouvert
            if current_time - self.last_state_change_time >= self.config.reset_timeout:
                self._change_state(CircuitState.HALF_OPEN)
                self.success_count = 0
                return True
            return False

        if self.state == CircuitState.HALF_OPEN:
            # En état semi-ouvert, limiter le nombre de requêtes
            return self.success_count < self.config.reset_threshold

        # En état fermé, toutes les requêtes sont autorisées
        return True

    def _change_state(self, new_state: CircuitState) -> None:
        """
        Change l'état du circuit et enregistre le changement.

        Args:
            new_state: Nouvel état du circuit
        """
        if self.state != new_state:
            self.logger.info(
                f"Circuit Breaker: changement d'état de {self.state.value} à {new_state.value}"
            )
            self.state = new_state
            self.last_state_change_time = time.time()

    def get_state(self) -> CircuitState:
        """
        Retourne l'état actuel du circuit.

        Returns:
            État actuel du circuit
        """
        # Mettre à jour l'état si nécessaire avant de le retourner
        if self.state == CircuitState.OPEN:
            current_time = time.time()
            if current_time - self.last_state_change_time >= self.config.reset_timeout:
                self._change_state(CircuitState.HALF_OPEN)
                self.success_count = 0

        return self.state

    def get_stats(self) -> Dict[str, Any]:
        """
        Retourne les statistiques du Circuit Breaker.

        Returns:
            Dictionnaire contenant les statistiques
        """
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_state_change": datetime.fromtimestamp(
                self.last_state_change_time
            ).isoformat(),
            "services": self.stats,
        }

    def call(
        self,
        func: Callable[[], T],
        exceptions_to_trip: ExceptionType = (Exception,),
        service_name: str = "default",
    ) -> Callable[[], T]:
        """
        Méthode pour appliquer le Circuit Breaker à une fonction synchrone.

        Args:
            func: Fonction à protéger par le Circuit Breaker
            exceptions_to_trip: Exceptions qui déclenchent l'ouverture du circuit
            service_name: Nom du service ou de la méthode

        Returns:
            Fonction wrappée avec protection Circuit Breaker
        """

        def wrapper() -> T:
            # Vérifier si la requête est autorisée
            if not self.allow_request():
                self.logger.warning(
                    f"Circuit Breaker ouvert pour {service_name}, requête bloquée"
                )
                raise CircuitOpenError(
                    f"Circuit Breaker ouvert pour {service_name}",
                    service_name=service_name,
                    reset_timeout=self.config.reset_timeout,
                    time_remaining=self.config.reset_timeout
                    - (time.time() - self.last_state_change_time),
                )

            try:
                result = func()
                self.record_success(service_name)
                return result
            except exceptions_to_trip as e:
                self.record_failure(service_name)
                self.logger.error(
                    f"Circuit Breaker: échec pour {service_name}: {str(e)}"
                )
                raise

        return wrapper

    @classmethod
    def circuit_breaker(
        cls,
        circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
        service_name: str = "default",
        exceptions_to_trip: ExceptionType = (Exception,),
        logger: Optional[logging.Logger] = None,
    ):
        """
        Décorateur pour appliquer le Circuit Breaker à une fonction asynchrone.

        Args:
            circuit_breaker_config: Configuration du Circuit Breaker
            service_name: Nom du service ou de la méthode
            exceptions_to_trip: Exceptions qui déclenchent l'ouverture du circuit
            logger: Logger à utiliser

        Returns:
            Décorateur pour fonctions asynchrones
        """

        def decorator(func: F) -> F:
            # Créer une instance du Circuit Breaker pour cette fonction
            config = circuit_breaker_config or CircuitBreakerConfig()
            breaker = cls(config)
            log = logger or logging.getLogger(__name__)

            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                # Vérifier si la requête est autorisée
                if not breaker.allow_request():
                    log.warning(
                        f"Circuit Breaker ouvert pour {service_name}, requête bloquée"
                    )
                    raise CircuitOpenError(
                        f"Circuit Breaker ouvert pour {service_name}",
                        service_name=service_name,
                        reset_timeout=config.reset_timeout,
                        time_remaining=config.reset_timeout
                        - (time.time() - breaker.last_state_change_time),
                    )

                try:
                    result = await func(*args, **kwargs)
                    breaker.record_success(service_name)
                    return result
                except exceptions_to_trip as e:
                    breaker.record_failure(service_name)
                    log.error(f"Circuit Breaker: échec pour {service_name}: {str(e)}")
                    raise

            # Ajouter une référence au Circuit Breaker pour permettre l'accès aux statistiques
            wrapper.circuit_breaker = breaker  # type: ignore

            return cast(F, wrapper)

        return decorator
