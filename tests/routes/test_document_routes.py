import json
from data.kbot_embedding_api_test_data import KbotEmbeddingApiTestData

from tests.testutils.mock_gcs import MockGcs
from kbotloadscheduler.bean.beans import Metadata


class TestDocumentRoutes:

    def test_compare_list(self, mocker, requests_mock, client):
        requests_mock.real_http = True
        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        perimeter_code = kbot_embedding_api_test_data.PERIMETER_CODE
        domain_code = kbot_embedding_api_test_data.DOMAIN_CODE
        source_code = kbot_embedding_api_test_data.SOURCE_CODE

        load_date = "202409121430"
        work_bucket = f'mon_bucket-{perimeter_code}'
        document_list_file_name = f'{load_date}/{domain_code}/{source_code}/list/list.json'
        document_list_file = f'gs://{work_bucket}/{document_list_file_name}'
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(work_bucket)
        blob_content = json.dumps({
            'source': kbot_embedding_api_test_data.SOURCE,
            'documents': kbot_embedding_api_test_data.get_repo_documents()
        })
        my_mock_gcs.add_blob(work_bucket, document_list_file_name, True,
                             blob_content, len(blob_content))

        response = client.post(f'/document/compare/{perimeter_code}',
                               json={'repo_document_list_file': document_list_file})
        assert response.status_code == 200
        actual_document_list = response.json()

        adapter = kbot_embedding_api_test_data.adapters.get(kbot_embedding_api_test_data.get_get_docs_url())
        assert adapter.called
        assert adapter.call_count == 1

        documents_to_get = kbot_embedding_api_test_data.get_documents_to_get()

        for i in range(5, 8):
            blob_get_doc_name = f'{load_date}/{domain_code}/{source_code}/getdoc/' \
                                f'_dir_{source_code}_{domain_code}_doc_{i}.txt.getdoc.json'
            blob_get_doc = my_mock_gcs.return_blob(work_bucket, blob_get_doc_name, get_blob_call=True)
            assert blob_get_doc is not None
            actual_blob_get_doc_content = json.loads(blob_get_doc.download_as_string())
            assert actual_blob_get_doc_content == {
                'source': kbot_embedding_api_test_data.SOURCE,
                'document': documents_to_get[i-5]
            }

        documents_to_remove = kbot_embedding_api_test_data.get_documents_to_remove()
        blob_remove_doc_name = f'{load_date}/{domain_code}/{source_code}/removedoc/removedoc.json'
        blob_remove_doc = my_mock_gcs.return_blob(work_bucket, blob_remove_doc_name, get_blob_call=True)
        assert blob_remove_doc is not None
        actual_blob_remove_doc_content = json.loads(blob_remove_doc.download_as_string())
        assert actual_blob_remove_doc_content == {
            'source': kbot_embedding_api_test_data.SOURCE,
            'documents': documents_to_remove
        }

        assert actual_document_list == documents_to_get

    def test_embedd_document_ok(self, mocker, requests_mock, client):
        requests_mock.real_http = True
        # perimOK to have success response from api
        perimeter_code = 'perimOK'
        perimeter_bucket = 'mon_bucket-perimOK'

        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(perimeter_bucket)

        relative_file_path = KbotEmbeddingApiTestData.DOCUMENT_NAME
        document_path = f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}'
        content = 'Just for test'
        my_mock_gcs.add_blob(perimeter_bucket, document_path, True,
                             content, len(content))

        metadata_serialized = {**KbotEmbeddingApiTestData.METADATA_SERIALIZED,
                               Metadata.LOCATION: f'gs://{perimeter_bucket}/{document_path}'}
        metadata_json = json.dumps(metadata_serialized)
        my_mock_gcs.add_blob(perimeter_bucket, document_path + '.metadata.json', True,
                             metadata_json, len(metadata_json))
        metadata_file = f'gs://{perimeter_bucket}/{document_path}.metadata.json'

        response = client.post(f'/document/embedd/{perimeter_code}',
                               json={'embedd_document_metadata_file': metadata_file})
        assert response.status_code == 200
        actual_result = response.json()

        adapter = kbot_embedding_api_test_data.adapters.get('/embedded_documents/perimOK')
        assert adapter.called
        assert adapter.call_count == 1

        expected_document = {**kbot_embedding_api_test_data.DOCUMENT}
        assert actual_result == expected_document

        expected_document_path = f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_done/{relative_file_path}'
        expected_document_blob = my_mock_gcs.return_blob(perimeter_bucket,
                                                         expected_document_path, True)
        assert expected_document_blob is not None
        assert expected_document_blob.download_as_string() == content

        expected_metadata_path = \
            f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_done/{relative_file_path}.metadata.json'
        expected_metadata_blob = my_mock_gcs.return_blob(perimeter_bucket,
                                                         expected_metadata_path, True)
        assert expected_metadata_blob is not None
        assert json.loads(expected_metadata_blob.download_as_string()) == metadata_serialized

        origin_document_blob = my_mock_gcs.return_blob(perimeter_bucket, document_path, True)
        assert origin_document_blob is None
        origin_metadata_blob = my_mock_gcs.return_blob(perimeter_bucket,
                                                       document_path + '.metadata.json', True)
        assert origin_metadata_blob is None

    def test_embedd_document_ko(self, mocker, requests_mock, client):
        requests_mock.real_http = True
        # perimKO to have error response from api
        perimeter_code = 'perimKO'
        perimeter_bucket = 'mon_bucket-perimKO'

        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)

        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(perimeter_bucket)

        relative_file_path = KbotEmbeddingApiTestData.DOCUMENT_NAME
        document_path = f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs/{relative_file_path}'
        content = 'Just for test'
        my_mock_gcs.add_blob(perimeter_bucket, document_path, True,
                             content, len(content))

        metadata_serialized = {**KbotEmbeddingApiTestData.METADATA_SERIALIZED,
                               Metadata.LOCATION: f'gs://{perimeter_bucket}/{document_path}'}
        metadata_json = json.dumps(metadata_serialized)
        my_mock_gcs.add_blob(perimeter_bucket, document_path + '.metadata.json', True,
                             metadata_json, len(metadata_json))
        metadata_file = f'gs://{perimeter_bucket}/{document_path}.metadata.json'
        client.post(f'/document/embedd/{perimeter_code}',
                    json={'embedd_document_metadata_file': metadata_file})
        adapter = kbot_embedding_api_test_data.adapters.get('/embedded_documents/perimKO')
        assert adapter.called
        assert adapter.call_count == 1

        error_message = ("https://kbot-embedding-api:8081/embedded_documents/perimKO with metadata file "
                         "gs://mon_bucket-perimKO/202409121430/domB/src5/docs/src1/domainA/chemin/"
                         "doc_for_test.txt.metadata.json => status_code is 505")

        expected_document_path = f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_error/{relative_file_path}'
        expected_document_blob = my_mock_gcs.return_blob(perimeter_bucket,
                                                         expected_document_path, True)
        assert expected_document_blob is not None
        assert expected_document_blob.download_as_string() == content

        expected_metadata_path = \
            f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_error/{relative_file_path}.metadata.json'
        expected_metadata_blob = my_mock_gcs.return_blob(perimeter_bucket,
                                                         expected_metadata_path, True)
        assert expected_metadata_blob is not None
        assert json.loads(expected_metadata_blob.download_as_string()) == metadata_serialized

        expected_error_path = f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/docs_error/{relative_file_path}.error.log'
        expected_error_blob = my_mock_gcs.return_blob(perimeter_bucket, expected_error_path, True)
        assert expected_error_blob is not None
        assert json.loads(expected_error_blob.download_as_string()) == \
            {**metadata_serialized, 'error': error_message}

        origin_document_blob = my_mock_gcs.return_blob(perimeter_bucket, document_path, True)
        assert origin_document_blob is None
        origin_metadata_blob = my_mock_gcs.return_blob(perimeter_bucket,
                                                       document_path + '.metadata.json', True)
        assert origin_metadata_blob is None

    def test_remove_documents(self, mocker, requests_mock, client):
        requests_mock.real_http = True
        # perimKO to have error response from api
        perimeter_code = 'perimOK'
        perimeter_bucket = 'mon_bucket-perimOK'

        kbot_embedding_api_test_data = KbotEmbeddingApiTestData()
        kbot_embedding_api_test_data.set_test_data(requests_mock)
        domain_code = kbot_embedding_api_test_data.DOMAIN_CODE
        source_code = kbot_embedding_api_test_data.SOURCE_CODE

        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(perimeter_bucket)

        document_list_file = f'{KbotEmbeddingApiTestData.RELATIVE_OUTPUT}/removedoc/removedoc.json'
        docs_to_remove = {
            'source': KbotEmbeddingApiTestData.SOURCE,
            'documents': kbot_embedding_api_test_data.get_documents_to_remove()
        }
        content = json.dumps(docs_to_remove)
        my_mock_gcs.add_blob(perimeter_bucket, document_list_file, True,
                             content, len(content))
        document_list_file_path = f'gs://{perimeter_bucket}/{document_list_file}'

        response = client.post(f'/document/remove/{perimeter_code}',
                               json={'remove_document_file': document_list_file_path})
        assert response.status_code == 200
        actual_document_list = response.json()

        for index in range(1, 3):
            name = f'/dir/{source_code}/{domain_code}/doc_{str(index)}.txt'
            document_id = f'{domain_code}|{source_code}|{name}'
            adapter = kbot_embedding_api_test_data.adapters.get(f'/embedded_documents'
                                                                f'?perimeter=perimOK&document_id={document_id}')
            assert adapter.called
            assert adapter.call_count == 1

        expected_documents = kbot_embedding_api_test_data.get_documents_to_remove()
        assert actual_document_list == expected_documents
