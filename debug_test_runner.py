#!/usr/bin/env python3
"""
Debug script to run the failing test directly
"""

import sys
import os

# Add paths
sys.path.append('src')
sys.path.append('tests')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

try:
    print("Starting test import...")
    
    # Import dependencies
    import json
    from datetime import datetime, timezone
    from unittest.mock import MagicMock, patch
    import pytest
    
    print("Basic imports successful")
    
    # Import beans
    from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
    print("Beans imported successfully")
    
    # Import loader
    from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
    print("ConfluenceLoader imported successfully")
    
    # Import test utils
    from testutils.mock_confluence import (
        MockConfluence,
        create_mock_confluence_client,
        setup_sample_confluence_data,
    )
    print("Test utils imported successfully")
    
    # Create test instance
    from tests.loader.test_confluence_with_mock_confluence import TestConfluenceLoaderWithMockConfluence
    print("Test class imported successfully")
    
    # Run a basic test
    test_instance = TestConfluenceLoaderWithMockConfluence()
    print("Test instance created successfully")
    
    print("All imports successful - ready to run test")

except Exception as e:
    print(f"Error during import: {e}")
    import traceback
    traceback.print_exc()
