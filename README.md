# kbot-load-scheduler

![](https://img.shields.io/badge/version-0.17.3-blue.svg)

![](https://img.shields.io/badge/python-3.12+-green.svg)

![](https://img.shields.io/badge/license-Proprietary-orange.svg)

`kbot-load-scheduler` est un composant central de l'écosystème Knowledge Bot, conçu pour orchestrer et automatiser le chargement, la synchronisation et la préparation des données provenant de diverses sources de connaissance. Son objectif est de garantir que le Knowledge Bot dispose toujours d'informations à jour et pertinentes, en gérant de manière robuste et efficace le cycle de vie des documents.

Ce système vise à :

- **Automatiser** le processus de mise à jour des connaissances.
- Assurer un **chargement fiable et résilient** des données.
- Permettre une **intégration facile** de nouvelles sources.
- Optimiser les **performances** de chargement.
- Fournir une **traçabilité** claire des opérations.

## 📚 Documentation Approfondie

Pour une analyse complète de l'architecture, des composants, des flux de données et des décisions de conception, veuillez consulter notre [Documentation Détaillée du Système](./docs/SYSTEM_OVERVIEW.md).

## 📋 Fonctionnalités Clés

- **Architecture modulaire de chargement** : Système extensible avec interface `AbstractLoader`.
- **Support de sources multiples** :
  - Confluence (pages, blogs, pièces jointes)
  - SharePoint (documents, sites)
  - Google Cloud Storage (fichiers et documents)
  - Sources personnalisées via le loader "Basic"
- **Orchestration des chargements** : Planification automatisée, gestion d'étapes granulaires et reprise sur erreur via GCS.
- **REST API** : Interface complète pour l'intégration avec d'autres systèmes (kbot-back, kbot-embedding).
- **Sécurité** : Gestion centralisée des secrets (Google Secret Manager) et authentification aux API.
- **Optimisations (notamment pour Confluence)** : Pagination parallèle, retry, Circuit Breaker, suivi des changements.
- **Déploiement Cloud Native** : Conçu pour Cloud Run avec planification via Cloud Scheduler.

## 🏗️ Architecture Simplifiée

![Architecture Simplifiée](./docs/assets/simplify-architecture-diagram-02-06-2025-17_48_36.png)

*Pour un diagramme d'architecture détaillé, consultez la [Documentation Détaillée du Système](./docs/SYSTEM_OVERVIEW.md).*

## 🚀 Installation

### Prérequis

- Python 3.12+
- Accès à Google Cloud Platform (GCP)
- Accès aux instances de service (Confluence, SharePoint, etc.) selon les besoins

### Installation recommandée

Le projet utilise `pipenv` pour la gestion des dépendances et des environnements virtuels. La manière la plus simple de configurer votre environnement est d'utiliser la cible `init` du `Makefile`, qui s'occupera d'installer `pipenv` si nécessaire, de configurer l'environnement virtuel et d'installer toutes les dépendances (y compris celles pour le développement et les tests).

```bash
# Cloner le dépôt
git clone <https://gitlab.tech.orange/knowledge-bot/kbot-load-scheduler.git>
cd kbot-load-scheduler

# Initialiser l'environnement et installer toutes les dépendances
make init

```

Cela exécutera les étapes nécessaires, y compris `pipenv --python 3.12` (ou la version spécifiée dans le `Makefile`) et `pipenv run pip install -r requirements.txt -r tests/test-requirements.txt`.

## 🔧 Configuration

1. **Variables d'environnement** : Voir `.env.example` pour la structure. Créez un fichier `.env` pour votre configuration.
2. **Secrets d'accès aux sources** : Gérés via Google Secret Manager. Voir la documentation dans `src/kbotloadscheduler/secret/secret_manager.py` et le guide de configuration des secrets locaux pour les tests dans `conf/etc/secrets/tests/README_SECRETS.md`.

   Exemples de clés de secrets :

  - `{perimeter_code}-confluence-credentials`
  - `{perimeter_code}-sharepoint-credentials`

## 📊 Utilisation

### Démarrer le service

Pour démarrer le service localement pour le développement (nécessite un fichier `.env` configuré et que l'environnement `pipenv` soit activé ou que les commandes soient préfixées par `pipenv run`), utilisez :

```bash
# Démarrage local pour développement (nécessite un .env configuré)
make start
```

Cela exécutera `uvicorn` dans l'environnement `pipenv` avec les variables d'environnement définies dans la cible `start` du `Makefile`.

Alternativement, si vous avez activé l'environnement `pipenv` (avec `pipenv shell`) et configuré les variables d'environnement manuellement :

```bash
uvicorn src.kbotloadscheduler.main:app --host 0.0.0.0 --port 8080 --reload
# Note: le port 8080 est un exemple, le Makefile utilise ${LOAD_SCHEDULER_PORT} qui est 8092

```

### Utilisation de l'API

La documentation OpenAPI (Swagger UI) est disponible sur `http://localhost:8092/docs` (ou l'URL et le port de votre service déployé, selon la configuration de `make start`) lorsque le service est en cours d'exécution.

### Exemples d'appels (Gestion des sources)

```bash
# Créer une nouvelle source (adaptez le port si vous n'utilisez pas make start)
curl -X POST "<http://localhost:8092/sources/>" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "confluence_docs",
    "label": "Documentation Confluence",
    "src_type": "confluence",
    "configuration": "{\"spaces\": [\"DOCS\", \"TECH\"], \"max_results\": 1000}",
    "domain_code": "engineering",
    "perimeter_code": "main"
  }'

# Lister les sources (adaptez le port si vous n'utilisez pas make start)
curl -X GET "<http://localhost:8092/sources/>"
```

*Consultez la [Documentation Détaillée du Système](./docs/SYSTEM_OVERVIEW.md) ou la Swagger UI pour la liste complète des endpoints et leurs détails.*

### Utilisation de Docker

```bash
# Construire l'image
docker build -t kbot-load-scheduler .

# Exécuter le conteneur (exemple minimal)
docker run -p 8080:8080 \
  -e ENV=local \
  -e PATH_TO_SECRET_CONFIG=/app/conf/etc/secrets/local \
  # ... autres variables d'environnement nécessaires ...
  kbot-load-scheduler

```

## 🧪 Tests

### Exécuter les tests

Les tests sont exécutés en utilisant `pytest` dans l'environnement `pipenv`.

```bash
# Exécuter tous les tests unitaires et d'intégration (nécessite configuration d'environnement de test, voir Makefile)
make unit-tests

# Alternativement, pour exécuter pytest directement après avoir activé l'environnement pipenv (pipenv shell)
# et configuré les variables d'environnement de test manuellement :
# pytest

# Tests avec couverture (make unit-tests génère déjà la couverture)
# pytest --cov=src

# Tests spécifiques à un module (ex: Confluence)
# (Assurez-vous que les variables d'environnement de test sont définies)
# pipenv run pytest tests/loader/test_confluence_end_to_end.py -v

```

### Analyse de Sécurité (Bandit)

```bash
# Exécuter l'analyse de sécurité
make bandit
# ou via le script directement (si vous êtes dans l'environnement pipenv)
# ./scripts/bandit_report.sh

```

### 🔧 Améliorations Récentes des Tests

✅ **Corrections apportées** :

- Tests end-to-end Confluence améliorés.
- Mocks robustes (`MockConfluenceAPI`, `MockGCSClient`).
- Meilleure isolation des tests et validation Pydantic dans les fixtures.

📚 **Documentation des tests** :

- Guide des secrets pour les tests : `conf/etc/secrets/tests/README_SECRETS.md`
- Tests Confluence : `src/kbotloadscheduler/loader/confluence/tests/README.md` et `src/kbotloadscheduler/loader/confluence/docs/TESTING_MOCKS.md`

## 📖 Documentation Complémentaire

- **Documentation API** : Disponible via Swagger UI sur `/docs` lorsque le service est lancé.
- [**Documentation Détaillée du Système**](./docs/SYSTEM_OVERVIEW.md) : Architecture, flux, composants.
- **Guides des Loaders** :
  - Confluence : `src/kbotloadscheduler/loader/confluence/README.md`
  - SharePoint : `TODO`
  - GCS : `TODO`
- **Gestion des Secrets pour Tests** : `conf/etc/secrets/tests/README_SECRETS.md`