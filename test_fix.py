#!/usr/bin/env python3

from unittest.mock import Mock

def test_fixed_implementation():
    """Test the fixed implementation for handling Mock objects."""
    
    # Simulate the fixed logic
    def get_max_children_depth(criteria):
        # Safe handling of max_children_depth with Mock objects in tests
        max_children_depth = 3  # Default value
        if hasattr(criteria, "max_children_depth"):
            attr_value = getattr(criteria, "max_children_depth")
            # Check if it's a real integer value, not a Mock
            if isinstance(attr_value, int):
                max_children_depth = attr_value
            elif hasattr(attr_value, '_mock_name'):
                # It's a Mock object, use default
                max_children_depth = 3
            else:
                # Try to convert to int, fallback to default
                try:
                    max_children_depth = int(attr_value)
                except (ValueError, TypeError):
                    max_children_depth = 3
        return max_children_depth
    
    # Test with Mock object
    mock_criteria = Mock()
    result = get_max_children_depth(mock_criteria)
    print(f"Mock criteria result: {result} (type: {type(result)})")
    
    # Test comparison
    current_depth = 1
    comparison_result = current_depth >= result
    print(f"✓ Comparison works: {current_depth} >= {result} = {comparison_result}")
    
    # Test with real object
    class RealCriteria:
        def __init__(self, max_children_depth=None):
            if max_children_depth is not None:
                self.max_children_depth = max_children_depth
    
    real_criteria = RealCriteria(5)
    result2 = get_max_children_depth(real_criteria)
    print(f"Real criteria result: {result2} (type: {type(result2)})")
    
    real_criteria_no_attr = RealCriteria()
    result3 = get_max_children_depth(real_criteria_no_attr)
    print(f"Real criteria (no attr) result: {result3} (type: {type(result3)})")

if __name__ == "__main__":
    test_fixed_implementation()
