#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import unittest
from unittest.mock import Mock, patch

print("Starting manual test...")

# Import the test class
from kbotloadscheduler.loader.confluence.tests.test_content_retriever import TestContentRetriever

# Create a test instance
test_instance = TestContentRetriever()

# Set up the test
test_instance.setUp()

# Try to run the initialization test
try:
    print("Running test_content_retriever_initialization...")
    with patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor") as mock_attachment_processor_class:
        mock_attachment_processor = Mock()
        mock_attachment_processor_class.return_value = mock_attachment_processor
        
        test_instance.test_content_retriever_initialization(mock_attachment_processor_class)
        print("✓ test_content_retriever_initialization passed!")
        
except Exception as e:
    print(f"✗ test_content_retriever_initialization failed: {e}")
    import traceback
    traceback.print_exc()

print("Manual test completed.")
