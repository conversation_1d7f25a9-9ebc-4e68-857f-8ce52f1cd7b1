#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour la sécurité des logs.
"""

import io
import logging
import unittest

from ..logging_utils import SecurityFilter


# from kbotloadscheduler.loader.confluence.config import ConfluenceConfig # Not used in this specific test file


# Helper to create LogRecord consistently for tests
def create_log_record(message_string, level=logging.INFO, args=()):
    """Crée un LogRecord pour les tests."""
    return logging.LogRecord(
        name="test_logger",
        level=level,
        pathname="test_path",
        lineno=1,
        msg=message_string,
        args=args,
        exc_info=None,
        func="test_func",
    )


class TestLogSecurity(unittest.TestCase):
    """Tests pour la sécurisation des logs."""

    def setUp(self):
        """Configuration des tests."""
        self.security_filter = SecurityFilter()

    def test_sanitize_pat_token(self):
        """Test du masquage des Personal Access Tokens."""
        test_cases = [
            ("Error with token ABC123XYZ456789012345", "Error with token ***TOKEN***"),
            (
                "Authentication failed: ABCDEFGHIJKLMNOPQRSTUVWXYZ123456",
                "Authentication failed: ***TOKEN***",
            ),
            (
                "Multiple tokens: ABC123 and XYZ789012345678901234",
                "Multiple tokens: ***TOKEN*** and ***TOKEN***",
            ),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()

                self.assertEqual(result, expected)
                # Specific checks for original token absence (optional if 'expected' is comprehensive)
                if "ABC123XYZ456789012345" in original:
                    self.assertNotIn("ABC123XYZ456789012345", result)
                if "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456" in original:
                    self.assertNotIn("ABCDEFGHIJKLMNOPQRSTUVWXYZ123456", result)
                # Ensure the placeholder is present
                self.assertIn("***TOKEN***", result)

    def test_sanitize_api_token_with_email(self):
        """Test du masquage des API tokens avec email."""
        test_cases = [
            ("Auth: <EMAIL>:ABCD1234567890123456", "Auth: ***EMAIL:TOKEN***"),
            (
                "<NAME_EMAIL>:XYZ9876543210987654",
                "Failed login ***EMAIL:TOKEN***",
            ),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()

                self.assertEqual(result, expected)
                if "<EMAIL>:ABCD1234567890123456" in original:
                    self.assertNotIn("<EMAIL>:ABCD1234567890123456", result)
                self.assertIn("***EMAIL:TOKEN***", result)

    def test_sanitize_authorization_headers(self):
        """Test du masquage des headers Authorization."""
        test_cases = [
            ("Authorization: Bearer ABC123XYZ456", "Authorization: Bearer ***TOKEN***"),
            (
                "Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==",
                "Authorization: Basic ***TOKEN***",
            ),
            (
                "Request failed: Authorization: Bearer TOKEN123",
                "Request failed: Authorization: Bearer ***TOKEN***",
            ),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()

                self.assertEqual(result, expected)
                self.assertIn("Authorization:", result)  # Preserved part
                self.assertIn("***TOKEN***", result)  # Masked part
                if "ABC123XYZ456" in original:
                    self.assertNotIn("ABC123XYZ456", result)
                if "QWxhZGRpbjpvcGVuIHNlc2FtZQ==" in original:
                    self.assertNotIn("QWxhZGRpbjpvcGVuIHNlc2FtZQ==", result)

    def test_sanitize_url_passwords(self):
        """Test du masquage des mots de passe dans les URLs."""
        test_cases = [
            (
                "https://user:<EMAIL>/api",
                "https://***USER***:***PASS***@example.com/api",
            ),
            (
                "Connection to ftp://admin:<EMAIL> failed",
                "Connection to ftp://***USER***:***PASS***@server.com failed",
            ),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()

                self.assertEqual(result, expected)
                # Check specific password strings if they were in original
                if ":password@" in original:
                    self.assertNotIn(":password@", result)
                if ":secret123@" in original:
                    self.assertNotIn(":secret123@", result)
                self.assertIn(
                    "://***USER***:***PASS***@", result
                )  # Check for the masked pattern

    def test_sanitize_url_parameters(self):
        """Test du masquage des paramètres sensibles dans les URLs."""
        # Structure: (original_message, expected_sanitized_message,
        # sensitive_values_to_check_absence, parts_to_check_presence)
        test_cases = [
            (
                "GET /api?token=ABC123&user=john",
                "GET /api?token=***&user=john",
                ["=ABC123"],  # Check for absence of '=VALUE'
                ["?token=***", "&user=john"],
            ),
            (
                "Request: /auth?password=secret&key=XYZ789",
                "Request: /auth?password=***&key=***",
                ["=secret", "=XYZ789"],
                ["?password=***", "&key=***"],
            ),
            (
                "URL: /api?secret=hidden&public=visible",
                "URL: /api?secret=***&public=visible",
                ["=hidden"],
                ["?secret=***", "&public=visible"],
            ),
        ]

        for (
            original,
            expected_msg,
            sensitive_value_parts_absent,
            parts_present,
        ) in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()

                self.assertEqual(result, expected_msg)

                for (
                    val_part
                ) in (
                    sensitive_value_parts_absent
                ):  # e.g., checks that "=hidden" is not in result
                    self.assertNotIn(val_part, result)
                for part in parts_present:
                    self.assertIn(part, result)

                if "secret=hidden" in original:  # Original specific check
                    self.assertNotIn(
                        "secret=hidden", result
                    )  # The exact key-value pair is gone

    def test_sanitize_json_keys(self):
        """Test du masquage des clés sensibles dans le JSON."""
        # Structure: (original_message, expected_sanitized_message, raw_sensitive_value_to_check_absence)
        test_cases = [
            (
                '{"api_token": "ABC123", "user": "john"}',
                '{"api_token": "***", "user": "john"}',
                '"ABC123"',
            ),  # The sensitive value as it appears in JSON string
            ('Config: "pat_token": "XYZ789"', 'Config: "pat_token": "***"', '"XYZ789"'),
            (
                '{"password": "secret", "public": "data"}',
                '{"password": "***", "public": "data"}',
                '"secret"',
            ),
        ]

        for original, expected, sensitive_value_json_str in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()

                self.assertEqual(result, expected)
                self.assertIn('"***"', result)  # Check the placeholder value is present
                self.assertNotIn(
                    sensitive_value_json_str, result
                )  # Check original sensitive value string is gone

    def test_confluence_client_sanitize_error(self):
        """Test de la méthode de nettoyage du ConfluenceClient."""
        from ..security import SecurityUtils

        # This test calls a protected static method of ConfluenceClient.
        # As per prompt, only SecurityFilter._sanitize_message calls were flagged.
        # If this line also causes warnings, add: # noinspection PyProtectedMember
        test_error = "Authentication failed with token ABC123XYZ456789012345"
        sanitized = SecurityUtils.sanitize_error_message(test_error)

        self.assertNotIn("ABC123XYZ456789012345", sanitized)
        self.assertIn("***TOKEN***", sanitized)
        self.assertIn("Authentication failed", sanitized)

    def test_security_filter_with_log_record(self):
        """Test du filtre de sécurité avec un vrai LogRecord."""
        # This test already uses the public interface correctly.
        logger = logging.getLogger(
            "test_security_filter_log_record"
        )  # Unique name for logger

        # Créer un nouveau stream pour capturer la sortie de log
        log_stream = io.StringIO()

        # Supprimer tous les handlers existants pour éviter les interférences
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # Ajouter un nouveau handler avec notre stream et notre filtre
        handler = logging.StreamHandler(log_stream)
        handler.addFilter(self.security_filter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)

        sensitive_message = "Error: token ABC123XYZ456789012345 failed"
        logger.info(sensitive_message)

        log_output = log_stream.getvalue()
        self.assertNotIn("ABC123XYZ456789012345", log_output)
        self.assertIn("***TOKEN***", log_output)
        # Clean up handler to avoid interference if tests run in same process serially without full reset
        # This is more robust for repeated test runs.
        logger.removeHandler(handler)

    def test_security_filter_with_args(self):
        """Test du filtre de sécurité avec des arguments de message."""
        # This test already uses the public interface correctly.
        record = logging.LogRecord(
            name="test",
            level=logging.ERROR,
            pathname="",
            lineno=0,
            msg="Error with token %s and user %s",
            args=("ABC123XYZ456789012345", "john"),
            exc_info=None,
        )

        self.security_filter.filter(record)

        # Check sanitized args
        self.assertNotIn("ABC123XYZ456789012345", str(record.args))
        self.assertIn(
            "***TOKEN***", str(record.args[0])
        )  # Check the first arg specifically
        self.assertEqual(record.args[1], "john")  # User argument should be unchanged

        # Check the fully formatted message
        final_message = record.getMessage()
        self.assertEqual(final_message, "Error with token ***TOKEN*** and user john")

    def test_preserve_non_sensitive_data(self):
        """Test que les données non sensibles sont préservées."""
        test_cases = [
            "Normal log message without sensitive data",
            "User john logged in successfully",
            "Processing 100 items in 5.2 seconds",
            "Configuration loaded from /path/to/config.json",
        ]

        for original in test_cases:
            with self.subTest(original=original):
                record = create_log_record(original)
                self.security_filter.filter(record)
                result = record.getMessage()
                self.assertEqual(original, result)

    def test_complex_mixed_content(self):
        """Test avec du contenu mixte complexe."""
        original = (
            "Request failed: POST https://user:<EMAIL>/auth "
            "with headers {'Authorization': 'Bearer ABC123XYZ456789012345'} "
            'and payload {"api_token": "SECRET789", "user": "john"}'
        )
        # Define the expected sanitized string based on assumed filter rules.
        # This needs to match precisely what SecurityFilter's rules would produce.
        expected = (
            "Request failed: POST https://***USER***:***PASS***@api.example.com/auth "
            "with headers {'Authorization': 'Bearer ***TOKEN***'} "
            'and payload {"api_token": "***", "user": "john"}'
        )

        record = create_log_record(original)
        self.security_filter.filter(record)
        result = record.getMessage()

        self.assertEqual(result, expected)

        # Further detailed checks (optional if 'expected' string is fully verified)
        self.assertNotIn(":pass@", result)
        self.assertNotIn("ABC123XYZ456789012345", result)
        self.assertNotIn('"SECRET789"', result)  # Check for JSON string value

        self.assertIn("Request failed", result)
        self.assertIn("POST", result)
        self.assertIn("https://***USER***:***PASS***@api.example.com/auth", result)
        self.assertIn("'Authorization': 'Bearer ***TOKEN***'", result)
        self.assertIn('{"api_token": "***", "user": "john"}', result)
        self.assertIn("john", result)  # Non-sensitive part within JSON


if __name__ == "__main__":
    unittest.main()
