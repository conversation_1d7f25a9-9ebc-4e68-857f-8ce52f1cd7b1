#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour générer des fichiers d'exemple pour les tests.
"""

import json
from pathlib import Path


def create_sample_drawio():
    """Crée un fichier Draw.io d'exemple."""
    drawio_content = """<mxfile host="app.diagrams.net" modified="2023-01-01T12:00:00.000Z" agent="5.0" version="20.8.16">
  <diagram name="Page-1" id="test-diagram">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="API Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="340" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Service A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="200" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Service B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="480" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="2" target="3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="370" as="sourcePoint" />
            <mxPoint x="440" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="2" target="4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="370" as="sourcePoint" />
            <mxPoint x="440" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>"""
    return drawio_content


def create_sample_json():
    """Crée un fichier JSON d'exemple."""
    data = {
        "api_version": "2.0",
        "endpoints": [
            {
                "path": "/users",
                "methods": ["GET", "POST"],
                "description": "Gestion des utilisateurs",
            },
            {
                "path": "/users/{id}",
                "methods": ["GET", "PUT", "DELETE"],
                "description": "Opérations sur un utilisateur spécifique",
            },
        ],
        "authentication": {"type": "Bearer", "token_endpoint": "/auth/token"},
    }
    return json.dumps(data, indent=2, ensure_ascii=False)


def generate_files():
    """Génère tous les fichiers d'exemple."""
    current_dir = Path(__file__).parent
    sample_files_dir = current_dir / "sample_files"

    # Créer le répertoire s'il n'existe pas
    sample_files_dir.mkdir(exist_ok=True)

    # Fichier Draw.io
    drawio_file = sample_files_dir / "sample.drawio"
    with open(drawio_file, "w", encoding="utf-8") as f:
        f.write(create_sample_drawio())
    print(f"✓ Créé : {drawio_file}")

    # Fichier JSON
    json_file = sample_files_dir / "api_config.json"
    with open(json_file, "w", encoding="utf-8") as f:
        f.write(create_sample_json())
    print(f"✓ Créé : {json_file}")

    # Fichier CSV d'exemple
    csv_file = sample_files_dir / "sample.csv"
    csv_content = """nom,email,role
Alice Martin,<EMAIL>,admin
Bob Dupont,<EMAIL>,user
Claire Bernard,<EMAIL>,moderator"""

    with open(csv_file, "w", encoding="utf-8") as f:
        f.write(csv_content)
    print(f"✓ Créé : {csv_file}")

    # Créer des fichiers binaires factices (pour simuler PDF, DOCX, etc.)
    binary_files = {
        "sample.pdf": b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF",
        "sample.docx": b"PK\x03\x04\x14\x00\x00\x00\x08\x00\x00\x00!\x00Mock DOCX content for testing",
        "sample.xlsx": b"PK\x03\x04\x14\x00\x00\x00\x08\x00\x00\x00!\x00Mock XLSX content for testing",
        "sample.png": b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82",
    }

    for filename, content in binary_files.items():
        file_path = sample_files_dir / filename
        with open(file_path, "wb") as f:
            f.write(content)
        print(f"✓ Créé : {file_path}")

    print(f"\n✅ Tous les fichiers d'exemple ont été générés dans {sample_files_dir}")


if __name__ == "__main__":
    generate_files()
