# Documentation du Module Confluence

Ce répertoire contient la documentation technique détaillée du module Confluence.

## 📁 Structure de la Documentation

### 📖 Guides d'Utilisation

- **[INTEGRATION_README.md](INTEGRATION_README.md)** - Guide d'intégration avec kbot-load-scheduler
    - Configuration des secrets
    - Utilisation via l'API REST
    - Mapping des données
    - Troubleshooting

- **[MIGRATION_COMPLETE.md](MIGRATION_COMPLETE.md)** - Guide de la migration vers l'architecture synchrone
    - Résumé de la migration asyncio → synchrone
    - Nouveaux composants disponibles
    - Exemples d'utilisation
    - Instructions de migration

### 🏗️ Documentation Technique

- **[CHANGE_TRACKING_OPTIMIZATION.md](CHANGE_TRACKING_OPTIMIZATION.md)** - Optimisation du suivi des changements
    - Problématique Cloud Run
    - Solution implémentée
    - Configuration du change tracking
    - Impact sur les performances

- **[TESTING_MOCKS.md](TESTING_MOCKS.md)** - Documentation des mocks pour les tests
    - Stratégie de test
    - Mocks disponibles
    - Utilisation dans les tests

- **[CHANGELOG.md](CHANGELOG.md)** - Historique des changements
    - Nouvelles fonctionnalités
    - Breaking changes
    - Améliorations de performance
    - Migration depuis les versions précédentes

## 🔗 Liens vers d'Autres Documentations

### Documentation du Module Principal

- **[../README.md](../README.md)** - Documentation principale du module Confluence
- **[../processing/README.md](../processing/README.md)** - Documentation du module de traitement

### Tests et Développement

- **[../tests/README.md](../tests/README.md)** - Documentation des tests
- **[../tests/README_TESTING_STRATEGY.md](../tests/README_TESTING_STRATEGY.md)** - Stratégie de test

## 🚀 Démarrage Rapide

### Pour l'Intégration dans kbot-load-scheduler

1. Consultez [INTEGRATION_README.md](INTEGRATION_README.md)
2. Configurez les secrets Confluence
3. Utilisez l'API REST standardisée

### Pour l'Utilisation Standalone

1. Consultez [../README.md](../README.md)
2. Suivez les exemples de [MIGRATION_COMPLETE.md](MIGRATION_COMPLETE.md)
3. Configurez l'architecture synchrone

## 📝 Contribution

Lors de l'ajout de nouvelle documentation :

1. **Placez les guides d'utilisation** dans ce répertoire `docs/`
2. **Placez la documentation spécialisée** dans les sous-modules appropriés
3. **Mettez à jour ce README** pour référencer les nouveaux documents
4. **Utilisez des liens relatifs** pour maintenir la portabilité

## 🔧 Scripts Utilitaires

Les scripts de développement sont maintenant dans le répertoire `scripts/` à la racine du projet :

- **`scripts/setup_confluence_secrets.py`** - Configuration des secrets pour les tests

```bash
# Utilisation
python scripts/setup_confluence_secrets.py --interactive
```

## 📊 Métriques et Rapports

Les rapports d'analyse sont disponibles dans :

- **[../reports/](../reports/)** - Rapports de performance et d'analyse

## 🆘 Support

Pour toute question sur la documentation :

1. Consultez d'abord les guides appropriés
2. Vérifiez les exemples dans les tests d'intégration
3. Consultez le [CHANGELOG.md](CHANGELOG.md) pour les changements récents
