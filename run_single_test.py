#!/usr/bin/env python3
import os
import sys
import subprocess

os.environ['PYTHONUNBUFFERED'] = '1'

print("Running pytest...")
try:
    result = subprocess.run([
        sys.executable, '-m', 'pytest', 
        'tests/routes/test_loader_routes.py::TestLoaderRoutes::test_get_document_list', 
        '-v', '--tb=short'
    ], capture_output=True, text=True, timeout=30)
    
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")  
    print(result.stderr)
    print(f"\nReturn code: {result.returncode}")
    
except subprocess.TimeoutExpired:
    print("Test timed out after 30 seconds")
except Exception as e:
    print(f"Error running test: {e}")
