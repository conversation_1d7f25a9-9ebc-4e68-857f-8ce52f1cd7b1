#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests d'intégration complets pour le ConfluenceLoader.
"""

import json
import tempfile
import unittest
from datetime import datetime
from pathlib import Path
from types import SimpleNamespace
from unittest.mock import patch, MagicMock

# Complétez vos imports avec SpaceInfo et UserInfo
from kbotloadscheduler.loader.confluence.config import (
    ConfluenceConfig,
    StorageConfig,
    HealthCheckConfig,
    SearchCriteria,
)
from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
from kbotloadscheduler.loader.confluence.models import ContentItem, SpaceInfo, UserInfo

class TestCompleteIntegration(unittest.TestCase):
    """
    Test le workflow de bout en bout, en moquant les appels réseau à Confluence.
    """

    def setUp(self):
        """Configuration du test."""
        self.temp_dir_obj = tempfile.TemporaryDirectory()
        self.storage_path = self.temp_dir_obj.name
        self.addCleanup(self.temp_dir_obj.cleanup)

        self.config = SimpleNamespace(
            confluence=ConfluenceConfig(
                url="https://mock.confluence.com",
                username="<EMAIL>",
                api_token="faketoken",
                search_criteria=SearchCriteria(
                    cql="space=TEST", space_keys=["TEST"], max_results=100
                ),
            ),
            storage=StorageConfig(type="filesystem", base_dir=self.storage_path),
            health_check=HealthCheckConfig(enabled=False),
        )

        self.mock_confluence_data = [
            ContentItem(
                id="123",
                type="page",
                status="current",
                title="Test Page 1",
                space=SpaceInfo(id="test-id", key="TEST", name="Test Space", type="global"),
                version={"number": 2},
                created=datetime(2023, 1, 1, 12, 0, 0),
                creator=UserInfo(id="user1", username="user1", display_name="Test User 1"),
                last_updated=datetime(2023, 1, 2, 12, 0, 0),
                last_updater=UserInfo(id="user1", username="user1", display_name="Test User 1"),
                content_url="https://mock.confluence.com/rest/api/content/123",
                web_ui_url="https://mock.confluence.com/spaces/TEST/pages/123",
                body_storage="<p>Content 1</p>",
                parent_id=None,
            ),
            ContentItem(
                id="456",
                type="page",
                status="current",
                title="Test Page 2",
                space=SpaceInfo(id="test-id", key="TEST", name="Test Space", type="global"),
                version={"number": 1},
                created=datetime(2023, 2, 1, 12, 0, 0),
                creator=UserInfo(id="user1", username="user1", display_name="Test User 1"),
                last_updated=datetime(2023, 2, 2, 12, 0, 0),
                last_updater=UserInfo(id="user1", username="user1", display_name="Test User 1"),
                content_url="https://mock.confluence.com/rest/api/content/456",
                web_ui_url="https://mock.confluence.com/spaces/TEST/pages/456",
                body_storage="<p>Content 2</p>",
                parent_id="123",
            ),
        ]

    @patch("kbotloadscheduler.loader.confluence.loader.ConfluenceClient")
    def test_complete_workflow_with_test_space(self, MockConfluenceClient):
        """
        Teste le workflow complet : initialisation, récupération et sauvegarde.
        """
        mock_client_instance = MockConfluenceClient.return_value
        mock_client_instance.test_connection = MagicMock(return_value=True)
        mock_client_instance.search_content_by_space = MagicMock(
            return_value=self.mock_confluence_data
        )
        mock_client_instance.close = MagicMock()

        loader = ConfluenceLoader(self.config)

        # Appel synchrone à run() au lieu d'utiliser asyncio.run()
        loader.run()

        # --- Assertions ---
        mock_client_instance.search_content_by_space.assert_called_once_with(
            space_key="TEST"
        )

        space_storage_path = Path(self.storage_path) / "TEST"
        self.assertTrue(space_storage_path.exists())

        created_files = list(space_storage_path.glob("*.json"))
        self.assertEqual(len(created_files), len(self.mock_confluence_data))

        expected_file_path = space_storage_path / "123.json"
        with open(expected_file_path, "r", encoding="utf-8") as f:
            saved_data = json.load(f)
        self.assertEqual(saved_data["id"], "123")

        mock_client_instance.close.assert_called_once()


if __name__ == "__main__":
    unittest.main()
