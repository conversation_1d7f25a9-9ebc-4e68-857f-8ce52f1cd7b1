#!/usr/bin/env python3

import sys
import os

# Add the src and tests directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, 'src')
tests_path = os.path.join(current_dir, 'tests')

sys.path.insert(0, src_path)
sys.path.insert(0, tests_path)

def test_imports():
    """Test if we can import all the required modules"""
    try:
        # Test basic imports
        print("Testing basic imports...")
        import json
        from datetime import datetime, timezone
        from unittest.mock import MagicMock, patch
        import pytest
        print("✅ Basic imports successful")
        
        # Test kbotloadscheduler imports
        print("Testing project imports...")
        from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        print("✅ Project imports successful")
        
        # Test testutils imports
        print("Testing test utils imports...")
        from testutils.mock_confluence import (
            MockConfluence,
            create_mock_confluence_client,
            setup_sample_confluence_data,
        )
        print("✅ Test utils imports successful")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality without pytest fixtures"""
    try:
        print("\nTesting basic mock creation...")
        
        # Import what we need
        from testutils.mock_confluence import MockConfluence, setup_sample_confluence_data
        from unittest.mock import MagicMock
        
        # Create mock config
        mock_config = MagicMock()
        mock_config.get_confluence_credentials.return_value = {
            "pat_token": "mock_confluence_pat_token"
        }
        print("✅ Mock config created")
        
        # Create mock confluence
        mock_conf = MockConfluence("https://mock-confluence.example.com")
        mock_conf = setup_sample_confluence_data(mock_conf)
        print("✅ Mock confluence created")
        
        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Running basic diagnostics...")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test basic functionality
    basic_ok = test_basic_functionality()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Imports: {'✅ OK' if imports_ok else '❌ FAILED'}")
    print(f"Basic functionality: {'✅ OK' if basic_ok else '❌ FAILED'}")
    
    if imports_ok and basic_ok:
        print("🎉 All basic tests passed!")
    else:
        print("⚠️  Some tests failed")
