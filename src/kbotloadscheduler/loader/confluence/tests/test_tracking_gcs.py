#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le module de suivi des changements avec support GCS.
"""

import json
import unittest
from datetime import datetime
from unittest.mock import Mock, MagicMock, patch

import pytest

from ..models import ContentItem, AttachmentDetail, SpaceInfo, UserInfo


@pytest.mark.gcs
@pytest.mark.tracking
class TestConfluenceChangeTrackerGCS(unittest.TestCase):
    """Tests pour la classe ConfluenceChangeTrackerGCS."""

    def setUp(self):
        """Configuration des tests."""
        # Mock des objets de test
        self.space_info = SpaceInfo(
            id="123", key="TEST", name="Test Space", type="global"
        )

        self.user_info = UserInfo(
            id="user123",
            username="testuser",
            display_name="Test User",
            email="<EMAIL>",
        )

        self.content_item = ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space_info,
            version={"number": 1},
            created=datetime.now(),
            creator=self.user_info,
            last_updated=datetime.now(),
            last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123",
            body_storage="<p>Test content</p>",
            body_plain="Test content",
            attachments=[],
        )

        self.attachment = AttachmentDetail(
            id="att123",
            title="test.pdf",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            created=datetime.now(),
            creator=self.user_info,
            download_url="https://example.com/download/test.pdf",
            content_id="123",
        )

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.gcs_storage")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", True)
    def test_init_with_valid_config(self, mock_gcs_storage):
        """Test d'initialisation avec une configuration valide."""
        # Mock du client GCS
        mock_client = Mock()
        mock_bucket = Mock()
        mock_bucket.exists.return_value = True
        mock_client.bucket.return_value = mock_bucket
        mock_gcs_storage.Client.return_value = mock_client

        from ..tracking_gcs import ConfluenceChangeTrackerGCS

        tracker = ConfluenceChangeTrackerGCS(
            bucket_name="test-bucket", base_prefix="test/tracking"
        )

        self.assertEqual(tracker.bucket_name, "test-bucket")
        self.assertEqual(tracker.base_prefix, "test/tracking")
        mock_gcs_storage.Client.assert_called_once()
        self.assertEqual(tracker._client, mock_client)
        self.assertEqual(tracker._bucket, mock_bucket)

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", False)
    def test_init_without_gcs_library(self):
        """Test d'initialisation sans la bibliothèque GCS."""
        from ..tracking_gcs import ConfluenceChangeTrackerGCS

        with self.assertRaises(ImportError) as context:
            ConfluenceChangeTrackerGCS("test-bucket")

        self.assertIn("google-cloud-storage", str(context.exception))

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.gcs_storage")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", True)
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.get_thread_pool_manager")
    def test_has_content_changed_new_content(
        self, mock_thread_manager, mock_gcs_storage
    ):
        """Test de détection de changement pour un nouveau contenu."""
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()

        mock_bucket.exists.return_value = True
        mock_bucket.blob.return_value = mock_blob
        mock_client.bucket.return_value = mock_bucket
        mock_gcs_storage.Client.return_value = mock_client

        # Mock thread pool manager
        mock_thread_pool = Mock()
        mock_thread_pool.run_in_io_pool = MagicMock()
        mock_thread_manager.return_value = mock_thread_pool

        # Le blob n'existe pas (nouveau contenu)
        mock_thread_pool.run_in_io_pool.return_value = False

        from ..tracking_gcs import ConfluenceChangeTrackerGCS

        tracker = ConfluenceChangeTrackerGCS("test-bucket")

        # Test
        result = await tracker.has_content_changed(self.content_item)

        self.assertTrue(result)

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.gcs_storage")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", True)
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.get_thread_pool_manager")
    def test_has_content_changed_unchanged_content(
        self, mock_thread_manager, mock_gcs_storage
    ):
        """Test de détection de changement pour un contenu inchangé."""
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()

        mock_bucket.exists.return_value = True
        mock_bucket.blob.return_value = mock_blob
        mock_client.bucket.return_value = mock_bucket
        mock_gcs_storage.Client.return_value = mock_client

        # Mock thread pool manager
        mock_thread_pool = Mock()
        mock_thread_pool.run_in_io_pool = MagicMock()
        mock_thread_manager.return_value = mock_thread_pool

        from ..tracking_gcs import ConfluenceChangeTrackerGCS

        tracker = ConfluenceChangeTrackerGCS("test-bucket")

        # Générer le hash attendu
        expected_hash = tracker._generate_content_hash(self.content_item)
        hash_data = {
            "id": "123",
            "hash": expected_hash,
            "timestamp": datetime.now().isoformat(),
        }

        # Le blob existe et contient le même hash
        mock_thread_pool.run_in_io_pool.side_effect = [
            True,  # blob.exists()
            json.dumps(hash_data),  # blob.download_as_text()
        ]

        # Test
        result = await tracker.has_content_changed(self.content_item)

        self.assertFalse(result)

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.gcs_storage")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", True)
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.get_thread_pool_manager")
    def test_has_attachment_changed_new_attachment(
        self, mock_thread_manager, mock_gcs_storage
    ):
        """Test de détection de changement pour une nouvelle pièce jointe."""
        # Setup mocks similaire au test précédent
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()

        mock_bucket.exists.return_value = True
        mock_bucket.blob.return_value = mock_blob
        mock_client.bucket.return_value = mock_bucket
        mock_gcs_storage.Client.return_value = mock_client

        mock_thread_pool = Mock()
        mock_thread_pool.run_in_io_pool = MagicMock()
        mock_thread_manager.return_value = mock_thread_pool

        # Le blob n'existe pas (nouvelle pièce jointe)
        mock_thread_pool.run_in_io_pool.return_value = False

        from ..tracking_gcs import ConfluenceChangeTrackerGCS

        tracker = ConfluenceChangeTrackerGCS("test-bucket")

        # Test
        result = await tracker.has_attachment_changed(self.attachment)

        self.assertTrue(result)

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.gcs_storage")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", True)
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.get_thread_pool_manager")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.CorrelationContext.get_correlation_id")
    def test_record_sync(
        self, mock_correlation, mock_thread_manager, mock_gcs_storage
    ):
        """Test d'enregistrement d'une synchronisation."""
        mock_correlation.return_value = "test-correlation-123"

        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()

        mock_bucket.exists.return_value = True
        mock_bucket.blob.return_value = mock_blob
        mock_client.bucket.return_value = mock_bucket
        mock_gcs_storage.Client.return_value = mock_client

        mock_thread_pool = Mock()
        mock_thread_pool.run_in_io_pool = MagicMock()
        mock_thread_manager.return_value = mock_thread_pool

        from ..tracking_gcs import ConfluenceChangeTrackerGCS

        tracker = ConfluenceChangeTrackerGCS("test-bucket")

        # Créer un contenu simple
        simple_content = Mock()
        simple_content.id = "123"
        simple_content.title = "Test Page"
        simple_content.type = "page"
        simple_content.space = Mock()
        simple_content.space.key = "TEST"
        simple_content.attachments = []

        content_items = [simple_content]

        # Test
        result = await tracker.record_sync(content_items)

        # Vérifications
        self.assertIsNotNone(result)
        self.assertIn("sync_id", result)
        self.assertIn("correlation_id", result)
        self.assertEqual(result["correlation_id"], "test-correlation-123")
        self.assertEqual(result["total_content_items"], 1)


@pytest.mark.unit
@pytest.mark.tracking
class TestChangeTrackerFactory(unittest.TestCase):
    """Tests pour la factory de création de trackers."""

    @patch("kbotloadscheduler.loader.confluence.tracking.ConfluenceChangeTracker")
    def test_get_change_tracker_filesystem(self, mock_tracker_class):
        """Test de création d'un tracker filesystem."""
        from ..tracking_gcs import get_change_tracker

        get_change_tracker("filesystem", storage_dir="./test")

        mock_tracker_class.assert_called_once_with(storage_dir="./test")

    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.ConfluenceChangeTrackerGCS")
    @patch("kbotloadscheduler.loader.confluence.tracking_gcs.GCS_AVAILABLE", True)
    def test_get_change_tracker_gcs(self, mock_tracker_class):
        """Test de création d'un tracker GCS."""
        from ..tracking_gcs import get_change_tracker

        tracker = get_change_tracker(
            "gcs", bucket_name="test-bucket", base_prefix="test/prefix"
        )

        self.assertIsNotNone(tracker)
        mock_tracker_class.assert_called_once_with(
            bucket_name="test-bucket", base_prefix="test/prefix"
        )

    def test_get_change_tracker_invalid_type(self):
        """Test de création avec un type invalide."""
        from ..tracking_gcs import get_change_tracker

        with self.assertRaises(ValueError) as context:
            get_change_tracker("invalid_type")

        self.assertIn("Type de stockage non supporté", str(context.exception))

    def test_get_change_tracker_gcs_missing_bucket(self):
        """Test de création GCS sans bucket_name."""
        from ..tracking_gcs import get_change_tracker

        with self.assertRaises(ValueError) as context:
            get_change_tracker("gcs")

        self.assertIn("bucket_name", str(context.exception))


if __name__ == "__main__":
    # Pour les tests async
    unittest.main()
