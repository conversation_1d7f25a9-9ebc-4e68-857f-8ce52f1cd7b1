#!/usr/bin/env python3

import sys
sys.path.insert(0, 'src')

from unittest.mock import Mock
from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
from kbotloadscheduler.loader.confluence.exceptions import ContentProcessingError

def test_mock_handling():
    """Test that Mock objects are handled correctly in max_children_depth comparison."""
    print("Testing Mock object handling...")
    
    # Create mock config with Mock max_children_depth
    config = Mock()
    config.max_children_depth = Mock()
    config.chunk_size = 1000
    config.overlap_size = 200
    config.max_parallel_downloads = 5
    
    # Mock thread pool config
    thread_pool_config = Mock()
    thread_pool_config.io_thread_workers = 5
    thread_pool_config.document_processing_workers = 3
    thread_pool_config.api_thread_workers = 4
    config.thread_pool_config = thread_pool_config
    
    # Mock client
    mock_client = Mock()
    
    try:
        # Create retriever
        retriever = SyncContentRetriever(mock_client, config)
        
        # Test the problematic method
        processed_ids = set()
        stats = {}
        mock_content = Mock()
        mock_content.children = []
        
        # This should not raise a TypeError about Mock comparison
        result = retriever._retrieve_children_recursively(
            mock_content, 0, processed_ids, stats
        )
        print("✓ Mock handling test passed - no TypeError")
        return True
        
    except TypeError as e:
        if "'<=' not supported between instances of 'Mock' and 'int'" in str(e):
            print(f"✗ Mock handling test failed: {e}")
            return False
        else:
            print(f"✓ Different TypeError (not the one we're fixing): {e}")
            return True
    except Exception as e:
        print(f"✓ Different exception (expected): {e}")
        return True

def test_error_handling():
    """Test that ContentProcessingError is raised correctly."""
    print("Testing error handling...")
    
    # Create mock config
    config = Mock()
    config.chunk_size = 1000
    config.overlap_size = 200
    config.max_parallel_downloads = 5
    config.max_children_depth = 3
    
    # Mock thread pool config
    thread_pool_config = Mock()
    thread_pool_config.io_thread_workers = 5
    thread_pool_config.document_processing_workers = 3
    thread_pool_config.api_thread_workers = 4
    config.thread_pool_config = thread_pool_config
    
    # Mock client that raises an exception
    mock_client = Mock()
    mock_client.get_content.side_effect = Exception("API Error")
    
    try:
        # Create retriever
        retriever = SyncContentRetriever(mock_client, config)
        
        # This should raise ContentProcessingError
        retriever.retrieve_content("123")
        print("✗ Error handling test failed - no exception raised")
        return False
        
    except ContentProcessingError as e:
        print(f"✓ Error handling test passed - ContentProcessingError raised: {e}")
        return True
    except Exception as e:
        print(f"✗ Error handling test failed - wrong exception type: {type(e).__name__}: {e}")
        return False

if __name__ == "__main__":
    print("Running fix validation tests...\n")
    
    mock_test_passed = test_mock_handling()
    print()
    error_test_passed = test_error_handling()
    print()
    
    if mock_test_passed and error_test_passed:
        print("✓ All tests passed! Fixes are working correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Check the issues above.")
        sys.exit(1)
