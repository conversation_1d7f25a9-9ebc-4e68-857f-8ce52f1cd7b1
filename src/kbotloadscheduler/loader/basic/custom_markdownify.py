from markdownify import MarkdownConverter
from bs4 import BeautifulSoup, Tag
import re


def clean_markdown(markdown_text):
    """
    Cleans a Markdown string by removing HTML tags while preserving their inner text.

    Parameters:
        markdown_text (str): The Markdown string containing HTML tags.

    Returns:
        str: The cleaned Markdown string without HTML tags.
    """
    # Parse the Markdown text with BeautifulSoup
    soup = BeautifulSoup(markdown_text, 'html.parser')

    for el in soup.find_all("strong"):
        el.replace_with(f"**{el.text.strip()}**")

    # Extract text without HTML tags
    cleaned_text = soup.get_text()
    return cleaned_text


class CustomMarkdownConverter(MarkdownConverter):
    def convert_h1(self, el, text, convert_as_inline):
        return f'# {text}\n\n'

    def convert_h2(self, el, text, convert_as_inline):
        return f'## {text}\n\n'

    def convert_h3(self, el, text, convert_as_inline):
        return f'### {text}\n\n'

    def convert_h4(self, el, text, convert_as_inline):
        return f'#### {text}\n\n'

    def convert_h5(self, el, text, convert_as_inline):
        return f'##### {text}\n\n'

    def convert_strong(self, el, text, convert_as_inline):
        return f' **{text.strip()}** '

    def convert_em(self, el, text, convert_as_inline):
        return f' *{text.strip()}* '

    def convert_span(self, el, text, convert_as_inline):
        # Optionally, handle specific styles within <span> tags
        # For now, we'll just return the text without any additional formatting
        return text

    def convert_table(self, el: Tag, text: str, convert_as_inline: bool) -> str:
        """
        Override the default table conversion with a custom implementation.
        """
        return html_table_to_markdown(str(el))

    def convert_ul(self, el: Tag, text: str, convert_as_inline: bool) -> str:
        """
        Override the default unordered list conversion to ensure proper formatting.
        """
        return handle_unordered_list(el)

    def convert_ol(self, el: Tag, text: str, convert_as_inline: bool) -> str:
        """
        Override the default ordered list conversion to ensure proper formatting.
        """
        return handle_ordered_list(el)

    def convert_li(self, el: Tag, text: str, convert_as_inline: bool) -> str:
        """
        Override the default list item conversion to handle nested lists and formatting.
        """
        # Determine the list type (ordered or unordered)
        parent = el.find_parent(['ul', 'ol'])
        if parent and parent.name == 'ol':
            # Ordered list
            prefix = el.index
            # Process the list item content, including links and nested lists
            item = handle_list_item(el, ordered=True, index=prefix)
        else:
            # Unordered list
            prefix = '- '
            item = handle_list_item(el)

        return f'{prefix}{item}'

    def convert_p(self, el: Tag, text: str, convert_as_inline: bool) -> str:
        """
        Override the default paragraph conversion to detect and handle list-like paragraphs.
        """
        # Detect if the paragraph contains list markers
        # e.g., lines starting with '-', '*', or numbered items
        list_pattern = re.compile(r'^[-*]\s+')
        lines = text.split('\n')
        is_list = all(list_pattern.match(line.strip()) for line in lines if line.strip())

        if is_list:
            # Convert each line into a list item
            markdown = ""
            for line in lines:
                line = line.strip()
                if list_pattern.match(line):
                    # Remove the existing list marker to prevent duplication
                    item = list_pattern.sub('', line)
                    markdown += f"- {item}\n"
            return markdown
        else:
            # Default paragraph conversion
            return super().convert_p(el, text, convert_as_inline)


def handle_unordered_list(el: Tag) -> str:
    """
    Convert <ul> elements to Markdown unordered lists, handling <a> tags and formatting.
    """
    markdown = ""
    for li in el.find_all('li', recursive=False):
        item = handle_list_item(li)
        markdown += f"- {item}\n"
    return markdown


def handle_ordered_list(el: Tag) -> str:
    """
    Convert <ol> elements to Markdown ordered lists, handling <a> tags and formatting.
    """
    markdown = ""
    index = 1
    for li in el.find_all('li', recursive=False):
        item = handle_list_item(li, ordered=True, index=index)
        markdown += f"{item}\n"
        index += 1
    return markdown


def html_table_to_markdown(html):
    soup = BeautifulSoup(html, 'html.parser')
    table = soup.find('table')
    if not table:
        return ''

    # Récupération des informations d'entête
    header_trs = get_header_trs(table)
    headers, alignments, max_cols = compute_headers_info(header_trs)

    # Extract data rows
    data_trs = get_data_trs(table, header_trs)
    data_rows, max_cols = compute_data_rows(data_trs, max_cols)

    # Prepare Markdown table
    markdown_table = []

    if headers:
        build_markdown_header_from_headers_rows(markdown_table, headers, alignments, max_cols)
    else:
        # No headers detected; treat first row as header
        build_markdown_header_from_first_data_row(markdown_table, data_rows, max_cols)

    # Add data rows to markdown
    for row in data_rows:
        while len(row) < max_cols:
            row.append('')
        markdown_table.append('| ' + ' | '.join(row[:max_cols]) + ' |')

    res = '\n'.join(markdown_table) + '\n'

    return res


def build_markdown_header_from_first_data_row(markdown_table, data_rows, max_cols):
    if data_rows:
        first_row = data_rows.pop(0)
        while len(first_row) < max_cols:
            first_row.append('')
        markdown_table.append('| ' + ' | '.join(first_row[:max_cols]) + ' |')
        alignment_row = '| ' + ' | '.join(['---'] * max_cols) + ' |'
        markdown_table.append(alignment_row)


def build_markdown_header_from_headers_rows(markdown_table, headers, alignments, max_cols):
    for header_row in headers:
        while len(header_row) < max_cols:
            header_row.append('')
    for i, header_row in enumerate(headers):
        markdown_table.append('| ' + ' | '.join(header_row[:max_cols]) + ' |')
        if i == len(headers) - 1:
            while len(alignments) < max_cols:
                alignments.append('---')
            alignment_row = '| ' + ' | '.join(alignments[:max_cols]) + ' |'
            markdown_table.append(alignment_row)


def get_header_trs(table):
    # Detect headers: use <thead> if present, else treat first row as header
    thead = table.find('thead')
    if thead:
        header_trs = thead.find_all('tr', recursive=False)
    else:
        header_trs = []
        first_tr = table.find('tr', recursive=False)
        if first_tr:
            header_trs = [first_tr]
    return header_trs


def compute_headers_info(header_trs):
    headers = []  # List of header rows
    alignments = []  # Single alignment row
    max_cols = 0  # To track the maximum number of columns
    # Process header rows
    for tr in header_trs:
        header_row, row_alignments = extract_header_info_from_single_tr(tr)
        # Update max_cols
        if len(header_row) > max_cols:
            max_cols = len(header_row)
        headers.append(header_row)
        alignments = row_alignments  # Take alignment from last header row

    return headers, alignments, max_cols


def extract_header_info_from_single_tr(tr):
    header_row = []
    row_alignments = []
    cells = tr.find_all(['th', 'td'], recursive=False)
    for cell in cells:
        alignment = build_md_cell_alignement(cell)

        colspan = int(cell.get('colspan', 1))
        cell_text = get_cell_text(cell)

        # Insert the same text for all horizontally spanned columns
        for _ in range(colspan):
            header_row.append(cell_text)
            row_alignments.append(alignment)
    return header_row, row_alignments


def get_data_trs(table, header_rows):
    all_rows = table.find_all('tr', recursive=True)
    data_trs = [tr for tr in all_rows if tr not in header_rows]
    return data_trs


def compute_data_rows(data_trs, max_cols):
    # List of data rows
    data_rows = []
    # spans dict will track both vertical and possibly horizontal spans
    # Format: spans = {col_index: {'remaining': <rows>, 'text': <text>}}
    # If a cell covers multiple columns, we set an entry for each column it covers.
    spans = {}
    for tr in data_trs:
        row = []
        cells = tr.find_all(['th', 'td'], recursive=False)
        col_index = 0

        # Before placing new cells, fill in spans (if current columns are spanned)
        # We don't do this all upfront because we need to place actual cells first.
        # Instead, we integrate this logic as we place each new cell.

        for cell in cells:
            # If a this col index, previous lines have a cell with rowspan remaining
            # we insert the value of the upper cell
            col_index = insert_col_value_from_upper_rowspanned_cols(spans, row, col_index)
            # we inserted all spanned values, so current cell value can be inserted
            # we also insert cell current value in following col if current cell has a colspan
            # we update spans informations if current cell has a rowspan
            col_index = insert_cell_value_with_span_info_if_any_span(spans, row, cell, col_index)

        # After placing all cells, if there are still spans extending over empty columns
        insert_remaining_values_from_upper_rowspanned_cols(spans, row, col_index)

        # Update max_cols if necessary
        if len(row) > max_cols:
            max_cols = len(row)

        data_rows.append(row)
    return data_rows, max_cols


def insert_col_value_from_upper_rowspanned_cols(spans, row, col_index):
    # Move past columns already occupied by active spans
    while col_index in spans and spans[col_index]['remaining'] > 0:
        row.append(spans[col_index]['text'])
        spans[col_index]['remaining'] -= 1
        if spans[col_index]['remaining'] == 0:
            del spans[col_index]
        col_index += 1
    return col_index


def insert_cell_value_with_span_info_if_any_span(spans, row, cell, col_index):
    colspan = int(cell.get('colspan', 1))
    rowspan = int(cell.get('rowspan', 1))
    cell_text = get_cell_text(cell)
    # Insert the cell text for all horizontally spanned columns in this row
    for _ in range(colspan):
        # As we already insert spanned values, current col index can take current cell value
        row.append(cell_text)
        # If there's a vertical span, add/update spans for these columns
        if rowspan > 1:
            spans[col_index] = {
                'remaining': rowspan - 1,
                'text': cell_text
            }
        col_index += 1
    return col_index


def insert_remaining_values_from_upper_rowspanned_cols(spans, row, col_index):
    while col_index in spans and spans[col_index]['remaining'] > 0:
        row.append(spans[col_index]['text'])
        spans[col_index]['remaining'] -= 1
        if spans[col_index]['remaining'] == 0:
            del spans[col_index]
        col_index += 1


def get_cell_text(cell: Tag) -> str:
    """
    Convert cell contents to Markdown.
    """
    # Replace <br> with space
    for br in cell.find_all('br'):
        br.replace_with(' ')
    # Convert images
    for img in cell.find_all('img'):
        alt = img.get('alt', '')
        src = img.get('src', '')
        img_markdown = f'![{alt}]({src})'
        img.replace_with(img_markdown)
    # Get text
    text = cell.get_text(separator=' ').strip()
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    return text


def get_alignment(cell: Tag) -> str:
    """
    Get alignment from cell's style or align attributes.
    """
    align = cell.get('align', '').lower()
    if align:
        return align
    style = cell.get('style', '').lower()
    if 'text-align: left' in style:
        return 'left'
    elif 'text-align: right' in style:
        return 'right'
    elif 'text-align: center' in style:
        return 'center'
    return ''


def build_md_cell_alignement(cell):
    align = get_alignment(cell)
    if align == 'left':
        alignment = ':---'
    elif align == 'right':
        alignment = '---:'
    elif align == 'center':
        alignment = ':---:'
    else:
        alignment = '---'
    return alignment


def handle_anchor_tags_in_list(li: Tag, ordered=False, index=1) -> str:
    """
    Converts <a> tags to Markdown links within a list item.
    """
    for a in li.find_all('a'):
        href = a.get('href', '').strip()
        text = a.get_text(strip=True)
        if href:
            markdown_link = f'[{text}]({href})\t\n'
        else:
            markdown_link = f'[{text}]()\t\n'
        a.replace_with(markdown_link)

    # Use decode_contents without separator to preserve Markdown links and formatting
    if ordered:
        return f"{index}. {li.decode_contents().strip()}"

    return li.decode_contents().strip()


def handle_list_item(li: Tag, ordered=False, index=1) -> str:
    """
    Process <li> elements, handling nested lists and formatting.
    """
    # print(li, ordered, index)
    # Handle <a> tags and other inline elements
    content = handle_anchor_tags_in_list(li, ordered, index)

    # Process nested lists if any
    nested_markdown = ""
    for child in li.contents:
        if isinstance(child, Tag):
            if child.name == 'ul':
                nested = handle_unordered_list(child)
                # Indent nested list
                nested = re.sub(r'^', '  ', nested, flags=re.MULTILINE)
                nested_markdown += f"\n{nested}"
            elif child.name == 'ol':
                nested = handle_ordered_list(child)
                # Indent nested list
                nested = re.sub(r'^', '  ', nested, flags=re.MULTILINE)
                nested_markdown += f"\n{nested}"

    return content + nested_markdown


# Shorthand conversion function using the custom converter
def md(html, **options):
    converter = CustomMarkdownConverter(**options)
    markdown_str = converter.convert(html)
    clean_markdown_str = clean_markdown(markdown_str)
    return clean_markdown_str
