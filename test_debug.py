#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, 'src')

try:
    # Test the import that's failing
    from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
    print("✅ ConfluenceLoader imported successfully")
    
    # Test the SyncOrchestrator import
    from kbotloadscheduler.loader.confluence.orchestrator import SyncOrchestrator
    print("✅ SyncOrchestrator imported successfully")
    
    # Test the patch path
    print("✅ All imports successful - the patch path should work")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ Other error: {e}")
    import traceback
    traceback.print_exc()
    
    from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
    print("✓ SyncContentRetriever imported")
    
    print("\nTesting basic functionality...")
    
    # Test mock comparison issue
    mock_criteria = Mock()
    max_depth = getattr(mock_criteria, "max_children_depth", 3)
    print(f"✓ Mock getattr works, got: {max_depth} (type: {type(max_depth)})")
    
    # Test comparison
    try:
        result = 1 >= max_depth
        print(f"✓ Comparison works: 1 >= {max_depth} = {result}")
    except Exception as e:
        print(f"✗ Comparison failed: {e}")
    
    print("\nAll tests passed!")
    
except Exception as e:
    print(f"✗ Import failed: {e}")
    import traceback
    traceback.print_exc()
