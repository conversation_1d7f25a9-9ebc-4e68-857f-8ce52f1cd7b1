import requests
import urllib3
from requests.auth import HTTP<PERSON>asicAuth
from kbotloadscheduler.loader.basic.utils import failed, retry
from kbotloadscheduler.loader.basic.preprocess import preprocess_items
from tqdm import tqdm
from markdownify import markdownify

APPLICATION_JSON = "application/json"

# suppress warning about SSL certificate verification
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ProcedureSheetClient:
    def __init__(
        self,
        url_service,
        okapi_url,
        client_id,
        client_secret,
        service_scope,
        ca_certificate,
        timeout,
        env
    ):
        self.okapi_url = okapi_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.service_scope = service_scope
        self.ca_certificate = ca_certificate
        self.timeout = timeout
        self.url_service = url_service
        self.headers = self.get_new_token_headers()
        self.sheet_status = (
            "published"  # One of ["published", "draft", "archive", None]
        )
        self.failed = []
        self.empty = []
        self.env = env

        env_to_status_ba = {
            "local": [4, 5],
            "tests": [4, 5],
            "dev": [4, 5],
            "ppr": [2, 3, 4, 5],
            "prd": [4, 5],
            # None: [4, 5]
        }
        self.status_ba_list = env_to_status_ba[env]

    def get_new_token_headers(self):
        r = requests.post(
            self.okapi_url,
            auth=HTTPBasicAuth(self.client_id, self.client_secret),
            timeout=300,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": APPLICATION_JSON,
            },
            data={"grant_type": "client_credentials", "scope": self.service_scope},
            verify=self.ca_certificate,
        )
        r.raise_for_status()
        token = r.json()["access_token"]
        headers = {
            "Content-Type": APPLICATION_JSON,
            "Accept": APPLICATION_JSON,
            "Accept-Language": "fr",
            "Authorization": f"Bearer {token}",
        }
        return headers

    def fetch_section_by_slug(self, slug):
        slug_id = slug.split("/")[-1]
        url = self.url_service + f"/sections/{slug_id}"
        resp = self.request_url(url)
        return resp

    def request_sections_from_slugs(self, slugs):
        return [self.fetch_section_by_slug(slug).json() for slug in slugs]

    @failed
    @retry(max_retries=2, backoff_factor=2)
    def request_url(self, url):
        response = requests.get(
            url=url,
            headers=self.headers,
            verify=self.ca_certificate,
            timeout=self.timeout,
        )
        if not response.ok:
            raise BasicApiException(url, response)
        else:
            return response

    def request_sheet_infos(self, num_items=10_000):
        print("Acquiring sheets info")
        url = (
            self.url_service
            + f"/procedure_sheets?itemsPerPage={num_items}"
        )
        if self.sheet_status:
            url += f"&status={self.sheet_status}"
        resp = self.request_url(url)
        return resp

    def get_list_proc_ids(self):
        sheet_infos = self.request_sheet_infos().json()
        proc_ids = [sheet_info["id"] for sheet_info in sheet_infos]

        return proc_ids

    def request_sheet_info_by_content_id(self, content_id):
        url = (
            self.url_service
            + f"/procedure_sheets/get_indexables?content.id={content_id}"
        )
        if self.sheet_status:
            url += f"&status={self.sheet_status}"
        resp = self.request_url(url)
        return resp

    def request_sheet_by_proc_id(self, proc_id):
        url = self.url_service + f"/procedure_sheets/{proc_id}"
        resp = self.request_url(url)
        return resp

    def request_contents_by_status_ba(self):
        result = []

        page = 1
        url = self.url_service + "/contents?contentType.id=2"
        for status_ba in self.status_ba_list:
            url += f"&statusBa[]={status_ba}"
        resp = self.request_url(f"{url}&page={page}").json()

        while resp:
            result.extend(resp)
            print(len(result))
            page += 1
            resp = self.request_url(f"{url}&page={page}").json()
            # pprint(resp)
        return [e["id"] for e in result]

    def expand_sheet(self, sheet, sheet_info):
        proc_id = sheet["id"]
        cid = sheet["content"]["id"]
        current_url = (
            f"https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/{proc_id}"
        )
        current_url_content = (
            f"https://newbasic.sso.francetelecom.fr/public/content/{cid}"
        )
        private_modification_date = sheet_info["content"]["privateModificationDate"]
        generalities = self.get_generalities(sheet, current_url)
        short_info_markdown = self.get_short_info_markdown(sheet)
        tabs = self.get_tabs(sheet, current_url)
        cases = self.get_casses(sheet, current_url, current_url_content)

        return {
            **sheet_info,
            **sheet,
            "url": current_url,
            "url_content": current_url_content,
            "privateModificationDate": private_modification_date,
            "generalities": generalities,
            "shortInfoMarkdown": short_info_markdown,
            "tabs": tabs,
            "cases": cases,
        }

    def get_generalities(self, sheet, current_url):
        generalities = sheet.get("generalities")
        if generalities:
            generalities = self.request_sections_from_slugs(generalities)
            generalities = preprocess_items(generalities, current_url)
        return generalities

    @staticmethod
    def get_short_info_markdown(sheet):
        short_info = sheet.get("shortInfo")
        if short_info:
            short_info_markdown = markdownify(short_info)
        else:
            short_info_markdown = ""
        return short_info_markdown

    def get_tabs(self, sheet, current_url):
        tabs = sheet["tabs"]
        if tabs:
            tabs = preprocess_items(tabs, current_url)
        return tabs

    def get_casses(self, sheet, current_url, current_url_content):
        cases = sheet["cases"]
        if cases:
            new_cases = []

            for _case in tqdm(cases, desc="Acquiring sections", leave=False):
                new_case = self.prepare_case(_case, current_url, current_url_content)
                new_cases.append(new_case)
            cases = new_cases
        return cases

    def prepare_case(self, _case, current_url, current_url_content):
        prerequisites = self.get_case_prerequisites(_case, current_url)
        key_points = self.get_case_key_points(_case, current_url)
        steps = self.get_case_steps(_case, current_url)
        case_contexts = _case["caseContexts"]
        case_contexts = preprocess_items(case_contexts, current_url)
        anchor = _case["anchor"]
        new_case = {
            **_case,
            "url": f"{current_url_content}#{anchor}",
            "title": _case["caseTitle"],
            "prerequisites": prerequisites,
            "keyPoints": key_points,
            "steps": steps,
            "caseContexts": case_contexts,
        }
        return new_case

    def get_case_prerequisites(self, _case, current_url):
        prerequisites = _case["prerequisites"]
        if prerequisites:
            prerequisites = self.request_sections_from_slugs(prerequisites)
            prerequisites = preprocess_items(prerequisites, current_url)
        return prerequisites

    def get_case_key_points(self, _case, current_url):
        key_points = _case["keyPoints"]
        if key_points:
            key_points = self.request_sections_from_slugs(key_points)
            key_points = preprocess_items(key_points, current_url)
        return key_points

    def get_case_steps(self, _case, current_url):
        steps = _case["steps"]
        if steps:
            steps = self.request_sections_from_slugs(steps)
            steps = preprocess_items(steps, current_url)
        return steps

    def log_empty(self, item_id, item_id_type, item, item_type):
        self.empty.append(
            {"id": item_id, "id_type": item_id_type, "item": item, "item_type": item_type}
        )

    def get_sheets_from_content_ids(self, content_ids):
        sheets = []
        sheet_infos = []
        for content_id in tqdm(content_ids, desc="Acquiring specific sheet info"):
            infos = self.request_sheet_info_by_content_id(content_id).json()
            if not infos:
                self.log_empty(content_id, "content_id", infos, "infos")
            sheet_infos.extend(infos)

        print(f"{len(sheet_infos)} sheets detected")
        for sheet_info in tqdm(sheet_infos, desc="Acquiring sheets"):
            proc_id = sheet_info["id"]

            sheet = self.request_sheet_by_proc_id(proc_id).json()
            if sheet:
                sheet = self.expand_sheet(sheet, sheet_info)
            else:
                self.log_empty(proc_id, "proc_id", sheet, "sheet")
                raise RuntimeError("Empty !!!!")

            sheets.append(sheet)

        return sheets


class BasicApiException(Exception):
    status_code: int

    def __init__(self, url, response):
        error_message = "Basic api call on url %s failed: " % url
        error_message += "[critical] status_code is %s" % response.status_code
        super().__init__(error_message)
        self.status_code = response.status_code
