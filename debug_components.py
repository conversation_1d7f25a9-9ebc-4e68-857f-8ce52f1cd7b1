#!/usr/bin/env python3
import sys
import os
import signal

def timeout_handler(signum, frame):
    raise TimeoutError("<PERSON><PERSON><PERSON> timed out")

# Set a timeout of 30 seconds
signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm(30)

# Add the src directory to path
sys.path.insert(0, 'src')
sys.path.insert(0, 'tests')

# Set required environment variables
os.environ['CONFLUENCE_URL'] = 'https://test-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'TEST'

print("Starting test debugging...")

try:
    print("1. Importing test modules...")
    from tests.routes.test_loader_routes import TestLoaderRoutes
    print("✓ Test class imported successfully")
    
    print("2. Importing dependency container...")
    from kbotloadscheduler.dependency.container import Container
    print("✓ Container imported successfully")
    
    print("3. Testing container configuration...")
    container = Container()
    print("✓ Container created successfully")
    
    print("4. Testing loader manager...")
    loader_manager = container.loader_manager()
    print("✓ Loader manager created successfully")
    
    print("5. Testing confluence loader...")
    confluence_loader = container.confluence_loader()
    print("✓ ConfluenceLoader created successfully")
    
    print("All components loaded successfully!")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
