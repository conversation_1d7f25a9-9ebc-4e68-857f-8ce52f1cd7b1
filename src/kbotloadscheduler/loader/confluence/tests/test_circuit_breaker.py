#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le module Circuit Breaker.
"""

import time
import unittest

from ..circuit_breaker import CircuitBreaker, CircuitState
from ..config import CircuitBreakerConfig
from ..exceptions import CircuitOpenError


class TestCircuitState(unittest.TestCase):
    """Tests pour l'énumération CircuitState."""

    def test_circuit_state_values(self):
        """Test des valeurs de l'énumération CircuitState."""
        self.assertEqual(CircuitState.CLOSED.value, "CLOSED")
        self.assertEqual(CircuitState.OPEN.value, "OPEN")
        self.assertEqual(CircuitState.HALF_OPEN.value, "HALF_OPEN")

    def test_circuit_state_members(self):
        """Test des membres de l'énumération CircuitState."""
        states = list(CircuitState)
        self.assertEqual(len(states), 3)
        self.assertIn(CircuitState.CLOSED, states)
        self.assertIn(CircuitState.OPEN, states)
        self.assertIn(CircuitState.HALF_OPEN, states)


class TestCircuitBreaker(unittest.TestCase):
    """Tests pour la classe CircuitBreaker."""

    def setUp(self):
        """Configuration des tests."""
        self.config = CircuitBreakerConfig(
            failure_threshold=3, reset_timeout=60, reset_threshold=2
        )
        self.circuit_breaker = CircuitBreaker(self.config)

    def test_circuit_breaker_initialization(self):
        """Test de l'initialisation du Circuit Breaker."""
        self.assertEqual(self.circuit_breaker.config, self.config)
        self.assertEqual(self.circuit_breaker.state, CircuitState.CLOSED)
        self.assertEqual(self.circuit_breaker.failure_count, 0)
        self.assertEqual(self.circuit_breaker.success_count, 0)
        self.assertIsNotNone(self.circuit_breaker.logger)
        self.assertEqual(self.circuit_breaker.stats, {})

    def test_record_success_closed_state(self):
        """Test d'enregistrement de succès en état fermé."""
        service_name = "test_service"

        self.circuit_breaker.record_success(service_name)

        self.assertEqual(self.circuit_breaker.state, CircuitState.CLOSED)
        self.assertIn(service_name, self.circuit_breaker.stats)
        self.assertEqual(self.circuit_breaker.stats[service_name]["success_count"], 1)
        self.assertEqual(self.circuit_breaker.stats[service_name]["failure_count"], 0)

    def test_record_failure_closed_state(self):
        """Test d'enregistrement d'échec en état fermé."""
        service_name = "test_service"

        self.circuit_breaker.record_failure(service_name)

        self.assertEqual(self.circuit_breaker.state, CircuitState.CLOSED)
        self.assertEqual(self.circuit_breaker.failure_count, 1)
        self.assertIn(service_name, self.circuit_breaker.stats)
        self.assertEqual(self.circuit_breaker.stats[service_name]["failure_count"], 1)

    def test_circuit_opens_after_threshold_failures(self):
        """Test que le circuit s'ouvre après le seuil d'échecs."""
        service_name = "test_service"

        # Enregistrer des échecs jusqu'au seuil
        for i in range(self.config.failure_threshold):
            self.circuit_breaker.record_failure(service_name)

        self.assertEqual(self.circuit_breaker.state, CircuitState.OPEN)
        self.assertEqual(
            self.circuit_breaker.failure_count, self.config.failure_threshold
        )

    def test_allow_request_closed_state(self):
        """Test que les requêtes sont autorisées en état fermé."""
        self.assertTrue(self.circuit_breaker.allow_request())

    def test_allow_request_open_state(self):
        """Test que les requêtes sont bloquées en état ouvert."""
        # Forcer l'état ouvert
        self.circuit_breaker.state = CircuitState.OPEN
        self.circuit_breaker.last_state_change_time = time.time()

        self.assertFalse(self.circuit_breaker.allow_request())

    def test_transition_to_half_open_after_timeout(self):
        """Test de transition vers l'état semi-ouvert après timeout."""
        # Forcer l'état ouvert avec un timestamp ancien
        self.circuit_breaker.state = CircuitState.OPEN
        self.circuit_breaker.last_state_change_time = (
            time.time() - self.config.reset_timeout - 1
        )

        # La requête devrait déclencher la transition vers HALF_OPEN
        self.assertTrue(self.circuit_breaker.allow_request())
        self.assertEqual(self.circuit_breaker.state, CircuitState.HALF_OPEN)

    def test_half_open_state_limits_requests(self):
        """Test que l'état semi-ouvert limite les requêtes."""
        self.circuit_breaker.state = CircuitState.HALF_OPEN
        self.circuit_breaker.success_count = 0

        # Les premières requêtes devraient être autorisées
        for i in range(self.config.reset_threshold):
            self.assertTrue(self.circuit_breaker.allow_request())
            # Simuler un succès pour incrémenter success_count
            self.circuit_breaker.record_success("test_service")

        # Maintenant success_count >= reset_threshold, donc le circuit devrait être fermé
        # Mais testons d'abord avec success_count juste en dessous du seuil
        self.circuit_breaker.state = CircuitState.HALF_OPEN
        self.circuit_breaker.success_count = self.config.reset_threshold

        # Les requêtes supplémentaires devraient être bloquées
        self.assertFalse(self.circuit_breaker.allow_request())

    def test_circuit_closes_after_successful_half_open(self):
        """Test que le circuit se ferme après des succès en état semi-ouvert."""
        self.circuit_breaker.state = CircuitState.HALF_OPEN
        self.circuit_breaker.success_count = 0

        # Enregistrer des succès jusqu'au seuil
        for i in range(self.config.reset_threshold):
            self.circuit_breaker.record_success("test_service")

        self.assertEqual(self.circuit_breaker.state, CircuitState.CLOSED)
        self.assertEqual(self.circuit_breaker.failure_count, 0)
        self.assertEqual(self.circuit_breaker.success_count, 0)

    def test_circuit_reopens_on_failure_in_half_open(self):
        """Test que le circuit se rouvre en cas d'échec en état semi-ouvert."""
        self.circuit_breaker.state = CircuitState.HALF_OPEN

        self.circuit_breaker.record_failure("test_service")

        self.assertEqual(self.circuit_breaker.state, CircuitState.OPEN)

    def test_get_state_updates_state(self):
        """Test que get_state met à jour l'état si nécessaire."""
        # Forcer l'état ouvert avec un timestamp ancien
        self.circuit_breaker.state = CircuitState.OPEN
        self.circuit_breaker.last_state_change_time = (
            time.time() - self.config.reset_timeout - 1
        )

        state = self.circuit_breaker.get_state()

        self.assertEqual(state, CircuitState.HALF_OPEN)

    def test_get_stats(self):
        """Test de récupération des statistiques."""
        service_name = "test_service"
        self.circuit_breaker.record_success(service_name)
        self.circuit_breaker.record_failure(service_name)

        stats = self.circuit_breaker.get_stats()

        self.assertIn("state", stats)
        self.assertIn("failure_count", stats)
        self.assertIn("success_count", stats)
        self.assertIn("last_state_change", stats)
        self.assertIn("services", stats)
        self.assertIn(service_name, stats["services"])

    async def test_circuit_breaker_decorator_success(self):
        """Test du décorateur Circuit Breaker avec succès."""

        @CircuitBreaker.circuit_breaker(
            circuit_breaker_config=self.config,
            service_name="test_service",
            exceptions_to_trip=(ValueError,),
        )
        async def test_function():
            return "success"

        result = await test_function()
        self.assertEqual(result, "success")

        # Vérifier que le succès a été enregistré
        breaker = test_function.circuit_breaker
        self.assertIn("test_service", breaker.stats)
        self.assertEqual(breaker.stats["test_service"]["success_count"], 1)

    async def test_circuit_breaker_decorator_failure(self):
        """Test du décorateur Circuit Breaker avec échec."""

        @CircuitBreaker.circuit_breaker(
            circuit_breaker_config=self.config,
            service_name="test_service",
            exceptions_to_trip=(ValueError,),
        )
        async def test_function():
            raise ValueError("Test error")

        with self.assertRaises(ValueError):
            await test_function()

        # Vérifier que l'échec a été enregistré
        breaker = test_function.circuit_breaker
        self.assertIn("test_service", breaker.stats)
        self.assertEqual(breaker.stats["test_service"]["failure_count"], 1)

    async def test_circuit_breaker_decorator_opens_circuit(self):
        """Test que le décorateur ouvre le circuit après plusieurs échecs."""

        @CircuitBreaker.circuit_breaker(
            circuit_breaker_config=self.config,
            service_name="test_service",
            exceptions_to_trip=(ValueError,),
        )
        async def test_function():
            raise ValueError("Test error")

        # Provoquer des échecs jusqu'au seuil
        for i in range(self.config.failure_threshold):
            with self.assertRaises(ValueError):
                await test_function()

        # Le circuit devrait maintenant être ouvert
        breaker = test_function.circuit_breaker
        self.assertEqual(breaker.state, CircuitState.OPEN)

        # La prochaine tentative devrait lever CircuitOpenError
        with self.assertRaises(CircuitOpenError):
            await test_function()

    async def test_circuit_breaker_decorator_ignores_other_exceptions(self):
        """Test que le décorateur ignore les exceptions non configurées."""

        @CircuitBreaker.circuit_breaker(
            circuit_breaker_config=self.config,
            service_name="test_service",
            exceptions_to_trip=(ValueError,),
        )
        async def test_function():
            raise RuntimeError("Different error")

        with self.assertRaises(RuntimeError):
            await test_function()

        # L'échec ne devrait pas être enregistré car RuntimeError n'est pas dans exceptions_to_trip
        breaker = test_function.circuit_breaker
        self.assertEqual(breaker.failure_count, 0)

    def test_multiple_services_tracking(self):
        """Test du suivi de plusieurs services."""
        service1 = "service1"
        service2 = "service2"

        self.circuit_breaker.record_success(service1)
        self.circuit_breaker.record_failure(service2)

        self.assertIn(service1, self.circuit_breaker.stats)
        self.assertIn(service2, self.circuit_breaker.stats)
        self.assertEqual(self.circuit_breaker.stats[service1]["success_count"], 1)
        self.assertEqual(self.circuit_breaker.stats[service2]["failure_count"], 1)

    def test_default_service_name(self):
        """Test avec nom de service par défaut."""
        self.circuit_breaker.record_success()

        self.assertIn("default", self.circuit_breaker.stats)
        self.assertEqual(self.circuit_breaker.stats["default"]["success_count"], 1)

    def test_call_method_success(self):
        """Test de la méthode call avec succès."""

        def test_function():
            return "success"

        wrapped_function = self.circuit_breaker.call(
            test_function, service_name="test_service"
        )
        result = wrapped_function()

        self.assertEqual(result, "success")
        self.assertIn("test_service", self.circuit_breaker.stats)
        self.assertEqual(self.circuit_breaker.stats["test_service"]["success_count"], 1)

    def test_call_method_failure(self):
        """Test de la méthode call avec échec."""

        def test_function():
            raise ValueError("Test error")

        wrapped_function = self.circuit_breaker.call(
            test_function, exceptions_to_trip=(ValueError,), service_name="test_service"
        )

        with self.assertRaises(ValueError):
            wrapped_function()

        self.assertIn("test_service", self.circuit_breaker.stats)
        self.assertEqual(self.circuit_breaker.stats["test_service"]["failure_count"], 1)

    def test_call_method_opens_circuit(self):
        """Test que la méthode call ouvre le circuit après plusieurs échecs."""

        def test_function():
            raise ValueError("Test error")

        wrapped_function = self.circuit_breaker.call(
            test_function, exceptions_to_trip=(ValueError,), service_name="test_service"
        )

        # Provoquer des échecs jusqu'au seuil
        for i in range(self.config.failure_threshold):
            with self.assertRaises(ValueError):
                wrapped_function()

        # Le circuit devrait maintenant être ouvert
        self.assertEqual(self.circuit_breaker.state, CircuitState.OPEN)

        # La prochaine tentative devrait lever CircuitOpenError
        with self.assertRaises(CircuitOpenError):
            wrapped_function()


if __name__ == "__main__":
    unittest.main()
