#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour exécuter les tests d'intégration Confluence avec une vraie instance.

Usage:
    python run_integration_tests.py --help
    python run_integration_tests.py --check-config
    python run_integration_tests.py --health-check
    python run_integration_tests.py --run-all
    python run_integration_tests.py --run-specific test_confluence_connection
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path

from dotenv import load_dotenv

# Ajouter le chemin du projet au PYTHONPATH
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


def check_configuration():
    """Vérifie la configuration pour les tests d'intégration."""
    print("🔍 Vérification de la configuration...")

    # Charger les variables d'environnement
    load_dotenv()

    required_vars = ["CONFLUENCE_URL", "CONFLUENCE_PAT_TOKEN"]
    optional_vars = ["CONFLUENCE_TEST_SPACE", "CONFLUENCE_TIMEOUT"]

    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            print(
                f"✅ {var}: {value[:20]}..."
                if len(value) > 20
                else f"✅ {var}: {value}"
            )

    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"⚠️  {var}: non défini (optionnel)")

    if missing_vars:
        print(f"\n❌ Variables manquantes: {', '.join(missing_vars)}")
        print("💡 Créez un fichier .env basé sur .env.example")
        return False

    print("\n✅ Configuration valide pour les tests d'intégration")
    return True


def run_health_check():
    """Exécute uniquement le test de health check."""
    print("🏥 Exécution du health check...")

    if not check_configuration():
        return False

    test_path = (
        "tests/loader/confluence/test_real_confluence_integration.py::"
        "TestRealConfluenceIntegration::test_health_check_real"
    )
    cmd = [
        "python",
        "-m",
        "pytest",
        test_path,
        "-v",
        "-s",
        "--tb=short",
    ]

    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ Health check réussi")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Health check échoué: {e}")
        return False


def run_specific_test(test_name):
    """Exécute un test spécifique."""
    print(f"🧪 Exécution du test: {test_name}")

    if not check_configuration():
        return False

    cmd = [
        "python",
        "-m",
        "pytest",
        f"tests/loader/confluence/test_real_confluence_integration.py::TestRealConfluenceIntegration::{test_name}",
        "-v",
        "-s",
        "--tb=short",
    ]

    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print(f"✅ Test {test_name} réussi")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Test {test_name} échoué: {e}")
        return False


def run_all_tests():
    """Exécute tous les tests d'intégration."""
    print("🚀 Exécution de tous les tests d'intégration...")

    if not check_configuration():
        return False

    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/loader/confluence/test_real_confluence_integration.py",
        "-v",
        "-s",
        "--tb=short",
        "-m",
        "integration",
    ]

    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ Tous les tests d'intégration réussis")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Certains tests d'intégration ont échoué: {e}")
        return False


def run_fast_tests():
    """Exécute uniquement les tests rapides (sans le marqueur 'slow')."""
    print("⚡ Exécution des tests rapides...")

    if not check_configuration():
        return False

    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/loader/confluence/test_real_confluence_integration.py",
        "-v",
        "-s",
        "--tb=short",
        "-m",
        "integration and not slow",
    ]

    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ Tests rapides réussis")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Certains tests rapides ont échoué: {e}")
        return False


def run_performance_tests():
    """Exécute les tests de performance."""
    print("📊 Exécution des tests de performance...")

    if not check_configuration():
        return False

    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/loader/confluence/test_performance_integration.py",
        "-v",
        "-s",
        "--tb=short",
        "-m",
        "performance and not slow",
    ]

    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ Tests de performance réussis")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Certains tests de performance ont échoué: {e}")
        return False


def run_all_performance_tests():
    """Exécute tous les tests de performance (y compris les lents)."""
    print("📊 Exécution de tous les tests de performance...")

    if not check_configuration():
        return False

    cmd = [
        "python",
        "-m",
        "pytest",
        "tests/loader/confluence/test_performance_integration.py",
        "-v",
        "-s",
        "--tb=short",
        "-m",
        "performance",
    ]

    try:
        subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ Tous les tests de performance réussis")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Certains tests de performance ont échoué: {e}")
        return False


def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(
        description="Script pour exécuter les tests d'intégration Confluence"
    )

    parser.add_argument(
        "--check-config", action="store_true", help="Vérifier la configuration"
    )

    parser.add_argument(
        "--health-check",
        action="store_true",
        help="Exécuter uniquement le health check",
    )

    parser.add_argument(
        "--run-all", action="store_true", help="Exécuter tous les tests d'intégration"
    )

    parser.add_argument(
        "--run-fast", action="store_true", help="Exécuter uniquement les tests rapides"
    )

    parser.add_argument(
        "--run-performance",
        action="store_true",
        help="Exécuter les tests de performance rapides",
    )

    parser.add_argument(
        "--run-all-performance",
        action="store_true",
        help="Exécuter tous les tests de performance",
    )

    parser.add_argument(
        "--run-specific",
        type=str,
        help="Exécuter un test spécifique (nom de la méthode)",
    )

    args = parser.parse_args()

    if args.check_config:
        check_configuration()
    elif args.health_check:
        run_health_check()
    elif args.run_all:
        run_all_tests()
    elif args.run_fast:
        run_fast_tests()
    elif args.run_performance:
        run_performance_tests()
    elif args.run_all_performance:
        run_all_performance_tests()
    elif args.run_specific:
        run_specific_test(args.run_specific)
    else:
        print("🤖 Script de tests d'intégration Confluence")
        print("\nCommandes disponibles:")
        print("  --check-config         : Vérifier la configuration")
        print("  --health-check         : Test de connexion rapide")
        print("  --run-fast             : Tests d'intégration rapides")
        print("  --run-all              : Tous les tests d'intégration")
        print("  --run-performance      : Tests de performance rapides")
        print("  --run-all-performance  : Tous les tests de performance")
        print("  --run-specific         : Test spécifique")
        print("\nExemples:")
        print("  python run_integration_tests.py --check-config")
        print("  python run_integration_tests.py --health-check")
        print("  python run_integration_tests.py --run-fast")
        print("  python run_integration_tests.py --run-performance")
        print(
            "  python run_integration_tests.py --run-specific test_confluence_connection"
        )


if __name__ == "__main__":
    main()
