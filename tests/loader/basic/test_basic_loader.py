# test_basic_loader.py
import os
from unittest.mock import MagicMock

from kbotloadscheduler.loader.basic.basic_loader import BasicLoader
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean, Metadata
from datetime import datetime
from data.basic_repo_test_data import BasicRepoTestData
from testutils.mock_gcs import MockGcs  # Ensure this path is correct
from dependency_injector import providers
import re

from src.kbotloadscheduler.secret.secret_manager import ConfigWithSecret


class TestBasicLoader:

    def test_get_document_list(self, mocker, requests_mock):
        # Initialize MockGcs
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        BasicRepoTestData.write_get_list(my_mock_gcs)
        BasicRepoTestData.prepare_gcs_repo(my_mock_gcs)

        # Mock environment variables
        mocker.patch.dict(os.environ, {
            "PATH_TO_SECRET_CONFIG": "/path/to/secrets",
            "URL_SERVICE_BASIC": "https://mockservice.com",
            "OKAPI_URL_BASIC": "https://mockokapi.com",
            "SERVICE_SCOPE_BASIC": "basic_scope",
            "TIMEOUT_BASIC": "24",
            "GCP_ENV": "tests",
            "ENV": "tests"
        })

        # Mock the POST request for obtaining the authorization token (your app's token)
        requests_mock.post("https://mockokapi.com/", json={"access_token": "mock_token"})

        requests_mock.get(
            "https://mockservice.com/contents?contentType.id=2&statusBa[]=4&statusBa[]=5&page=1",
            # "https://mockservice.com/contents?contentType.id=2&statusBa%5B%5D=4&statusBa%5B%5D=5",
            json=[
                {
                    "id": 1,
                    "contentType": {
                        "id": 2,
                        "identifier": "ProcedureSheet"
                    },
                    "statusBa": "04",
                    "legalSensitivity": False
                },
                {
                    "id": 2,
                    "contentType": {
                        "id": 2,
                        "identifier": "ProcedureSheet"
                    },
                    "statusBa": "04",
                    "legalSensitivity": False
                }
            ]
        )

        requests_mock.get(
            "https://mockservice.com/contents?contentType.id=2&statusBa[]=4&statusBa[]=5&page=2",
            # "https://mockservice.com/contents?contentType.id=2&statusBa%5B%5D=4&statusBa%5B%5D=5",
            json=[]
        )

        # Mock the ProcedureSheetClient API request for fetching documents
        requests_mock.get(
            "https://mockservice.com/procedure_sheets?itemsPerPage=10000&status=published",
            json=[
                {
                    "id": "proc_1",
                    "content": {
                        "id": "1",
                        "title": "Test Sheet 1",
                        "tags": [{"name": "tag1"}, {"name": "tag2"}],
                        "privateModificationDate": "2025-10-15T12:00:00+0000",
                    },
                    "modificationDate": "2024-10-15T12:00:00+0000",
                    "universe": "universe1",
                    "channel": "channel1",
                    "market": "market1",
                    "customerNeed": "need1",
                    "geographicZone": "zone1",
                    "creationDate": "2024-01-01T10:00:00+0000"
                },
                {
                    "id": "proc_2",
                    "content": {
                        "id": "2",
                        "title": "Test Sheet 2",
                        "tags": [{"name": "tag3"}],
                        "privateModificationDate": "2025-10-15T12:00:00+0000",
                    },
                    "modificationDate": "2024-10-16T15:30:00+0000",
                    "universe": "universe2",
                    "channel": "channel2",
                    "market": "market2",
                    "customerNeed": "need2",
                    "geographicZone": "zone2",
                    "creationDate": "2024-02-01T11:00:00+0000"
                }
            ]
        )

        requests_mock.get(
            "https://mockservice.com/procedure_sheets/get_indexables?content.id=1&status=published",
            json=[
                {
                    "id": "proc_1",
                    "content": {
                        "id": "1",
                        "title": "Test Sheet 1",
                        "tags": [{"name": "tag1"}, {"name": "tag2"}],
                        "privateModificationDate": "2025-10-15T12:00:00+0000",
                    },
                    "cases": [],
                    "tabs": [],
                    "universe": "universe1",
                    "channel": "channel1",
                    "market": "market1",
                    "customerNeed": "need1",
                    "geographicZone": "zone1",
                    "creationDate": "2024-01-01T10:00:00+0000",
                    "modificationDate": "2024-10-15T12:00:00+0000",
                    "status": "published",
                    "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
                }
            ]
        )

        requests_mock.get(
            "https://mockservice.com/procedure_sheets/get_indexables?content.id=2&status=published",
            json=[
                {
                    "id": "proc_2",
                    "content": {
                        "id": "2",
                        "title": "Test Sheet 2",
                        "tags": [{"name": "tag1"}, {"name": "tag2"}],
                        "privateModificationDate": "2025-10-15T12:00:00+0000",
                    },
                    "cases": [],
                    "tabs": [],
                    "universe": "universe2",
                    "channel": "channel2",
                    "market": "market2",
                    "customerNeed": "need2",
                    "geographicZone": "zone2",
                    "creationDate": "2024-01-01T10:00:00+0000",
                    "modificationDate": "2024-10-16T15:30:00+0000",
                    "status": "published",
                    "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
                }
            ]
        )

        # Mock the preprocess_sheet function
        mock_preprocess_sheet = mocker.patch(
            "kbotloadscheduler.loader.basic.preprocess.preprocess_sheet"
        )
        mock_preprocess_sheet.return_value = ("mock_content", {"metadata_key": "mock_value"})

        # Initialize the BasicLoader
        loader = BasicLoader(self.__get_config_with_secret__(mocker))
        source = SourceBean(**BasicRepoTestData.SOURCE)

        # Call get_document_list
        actual_document_list = loader.get_document_list(source)

        # Expected data
        expected_document_list = [
            DocumentBean(
                id="domA/srcA1/cid_1",
                path="https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/proc_1",
                name="Test Sheet 1",
                modification_time=datetime.strptime(
                    "2025-10-15T12:00:00+0000", "%Y-%m-%dT%H:%M:%S%z"
                )

            ),
            DocumentBean(
                id="domA/srcA1/cid_2",
                path="https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/proc_2",
                name="Test Sheet 2",
                modification_time=datetime.strptime(
                    "2025-10-15T12:00:00+0000", "%Y-%m-%dT%H:%M:%S%z"
                )
            )
        ]

        # Assertions
        assert actual_document_list == expected_document_list

    def test_get_document(self, mocker, requests_mock):
        # Initialize MockGcs
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{BasicRepoTestData.WORK_BUCKET}')
        BasicRepoTestData.prepare_gcs_repo(my_mock_gcs)

        # Mock environment variables
        mocker.patch.dict(os.environ, {
            "PATH_TO_SECRET_CONFIG": "/path/to/secrets",
            "URL_SERVICE_BASIC": "https://mockservice.com",
            "OKAPI_URL_BASIC": "https://mockokapi.com",
            "SERVICE_SCOPE_BASIC": "basic_scope",
            "TIMEOUT_BASIC": "30"
        })

        # Mock the POST request for obtaining the authorization token (your app's token)
        requests_mock.post("https://mockokapi.com/", json={"access_token": "mock_token"})

        # Mock the ProcedureSheetClient API request for get_sheets_from_content_ids
        requests_mock.get(
            "https://mockservice.com/procedure_sheets/get_indexables?content.id=1&status=published",
            json=[
                {
                    "id": "proc_1",
                    "content": {
                        "id": "1",
                        "title": "Test Sheet 1",
                        "tags": [{"name": "tag1"}, {"name": "tag2"}],
                        "privateModificationDate": "2025-10-15T12:00:00+0000",
                    },
                    "cases": [],
                    "tabs": [],
                    "universe": "universe1",
                    "channel": "channel1",
                    "market": "market1",
                    "customerNeed": "need1",
                    "geographicZone": "zone1",
                    "creationDate": "2024-01-01T10:00:00+0000",
                    "modificationDate": "2024-10-15T12:00:00+0000",
                    "status": "published",
                    "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
                }
            ]
        )

        # Mock the ProcedureSheetClient API request for fetching individual procedure sheet
        requests_mock.get(
            "https://mockservice.com/procedure_sheets/proc_1",
            json={
                "id": "proc_1",
                "content": {
                    "id": "1",
                    "title": "Test Sheet 1",
                    "tags": [{"name": "tag1"}, {"name": "tag2"}],
                    "privateModificationDate": "2025-10-15T12:00:00+0000",
                },
                "cases": [],
                "tabs": [],
                "universe": "universe1",
                "channel": "channel1",
                "market": "market1",
                "customerNeed": "need1",
                "geographicZone": "zone1",
                "creationDate": "2024-01-01T10:00:00+0000",
                "modificationDate": "2024-10-15T12:00:00+0000",
                "status": "published",
                "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
            }
        )

        # Correctly Mock the preprocess_sheet function in basic_loader's namespace
        mock_preprocess_sheet = mocker.patch(
            "kbotloadscheduler.loader.basic.basic_loader.preprocess_sheet"
        )
        mock_preprocess_sheet.return_value = ("mock_content", {"metadata_key": "mock_value"})

        # Mock create_file_with_content to prevent actual GCS operations
        mocker.patch(
            "kbotloadscheduler.gcs.gcs_utils.create_file_with_content",
            return_value=None  # Assuming no return value is needed
        )

        # Initialize the BasicLoader after setting up all mocks
        loader = BasicLoader(self.__get_config_with_secret__(mocker))
        source = SourceBean(**BasicRepoTestData.SOURCE)
        document = DocumentBean(
            id="cid_1",
            path="https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/proc_1",
            name="Test Sheet 1",
            modification_time=datetime.strptime(
                "2024-10-15T12:00:00+0000", "%Y-%m-%dT%H:%M:%S%z"
            )
        )
        output_path = "gs://mock-bucket/output-path"

        # Call get_document
        actual_metadata = loader.get_document(source, document, output_path)

        # Debugging: Print actual metadata
        print("Actual Metadata:", actual_metadata)

        # Expected metadata
        expected_metadata = {
            "metadata_key": "mock_value",
            Metadata.DOCUMENT_ID: 'cid_1',
            Metadata.DOCUMENT_NAME: 'Test Sheet 1',
            Metadata.LOCATION: "gs://mock-bucket/output-path/cid_1.basic",
            Metadata.DOMAIN_CODE: source.domain_code,
            Metadata.SOURCE_CODE: source.code
        }

        # Assertions
        assert actual_metadata == expected_metadata

        # # Verify that the blob was uploaded correctly in MockGcs
        # assert my_mock_gcs.return_blob(
        #     BasicRepoTestData.WORK_BUCKET,
        #     f'{BasicRepoTestData.RELATIVE_OUTPUT}/docs/cid_1',
        #     get_blob_call=True
        # ) is not None

    def test_get_documents(self, mocker, requests_mock):
        # Initialize MockGcs
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{BasicRepoTestData.WORK_BUCKET}')
        BasicRepoTestData.prepare_gcs_repo(my_mock_gcs)

        # Mock environment variables
        mocker.patch.dict(os.environ, {
            "PATH_TO_SECRET_CONFIG": "/path/to/secrets",
            "URL_SERVICE_BASIC": "https://mockservice.com",
            "OKAPI_URL_BASIC": "https://mockokapi.com",
            "SERVICE_SCOPE_BASIC": "basic_scope",
            "TIMEOUT_BASIC": "30"
        })

        # Mock the POST request for obtaining the authorization token (your app's token)
        requests_mock.post("https://mockokapi.com/", json={"access_token": "mock_token"})

        # Define a function to handle `get_indexables` responses
        def match_get_indexables(request):
            qs = request.qs  # Query string parameters as a dict
            content_ids = qs.get('content.id', [])
            status = qs.get('status', [])
            if not content_ids or not status:
                return []

            content_id = content_ids[0]
            status_value = status[0]

            if content_id == '1' and status_value == 'published':
                return [
                    {
                        "id": "proc_1",
                        "content": {
                            "id": "1",
                            "title": "Test Sheet 1",
                            "tags": [{"name": "tag1"}, {"name": "tag2"}],
                            "privateModificationDate": "2025-10-15T12:00:00+0000",
                        },
                        "cases": [],
                        "tabs": [],
                        "universe": "universe1",
                        "channel": "channel1",
                        "market": "market1",
                        "customerNeed": "need1",
                        "geographicZone": "zone1",
                        "creationDate": "2024-01-01T10:00:00+0000",
                        "modificationDate": "2024-10-15T12:00:00+0000",
                        "status": "published",
                        "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
                    }
                ]
            elif content_id == '2' and status_value == 'published':
                return [
                    {
                        "id": "proc_2",
                        "content": {
                            "id": "2",
                            "title": "Test Sheet 2",
                            "tags": [{"name": "tag3"}],
                            "privateModificationDate": "2025-10-15T12:00:00+0000",
                        },
                        "cases": [],
                        "tabs": [],
                        "universe": "universe2",
                        "channel": "channel2",
                        "market": "market2",
                        "customerNeed": "need2",
                        "geographicZone": "zone2",
                        "creationDate": "2024-02-01T11:00:00+0000",
                        "modificationDate": "2024-10-16T15:30:00+0000",
                        "status": "published",
                        "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
                    }
                ]
            else:
                return []

        # Define a function that matches the signature expected by `requests_mock`
        def get_indexables_response(request, context):
            return match_get_indexables(request)

        # Use the function as the `json` parameter
        requests_mock.get(
            re.compile(r"https://mockservice\.com/procedure_sheets/get_indexables.*"),
            json=get_indexables_response
        )

        # Mock the ProcedureSheetClient API requests for fetching individual procedure sheets
        requests_mock.get(
            "https://mockservice.com/procedure_sheets/proc_1",
            json={
                "id": "proc_1",
                "content": {
                    "id": "1",
                    "title": "Test Sheet 1",
                    "tags": [{"name": "tag1"}, {"name": "tag2"}]
                },
                "cases": [],
                "tabs": [],
                "universe": "universe1",
                "channel": "channel1",
                "market": "market1",
                "customerNeed": "need1",
                "geographicZone": "zone1",
                "creationDate": "2024-01-01T10:00:00+0000",
                "modificationDate": "2024-10-15T12:00:00+0000",
                "status": "published",
                "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
            }
        )
        requests_mock.get(
            "https://mockservice.com/procedure_sheets/proc_2",
            json={
                "id": "proc_2",
                "content": {
                    "id": "2",
                    "title": "Test Sheet 2",
                    "tags": [{"name": "tag3"}]
                },
                "cases": [],
                "tabs": [],
                "universe": "universe2",
                "channel": "channel2",
                "market": "market2",
                "customerNeed": "need2",
                "geographicZone": "zone2",
                "creationDate": "2024-02-01T11:00:00+0000",
                "modificationDate": "2024-10-16T15:30:00+0000",
                "status": "published",
                "endCommercialValidityDate": "2024-10-15T10:35:41.657Z"
            }
        )

        # Mock the preprocess_sheet function with side effects for multiple documents
        mock_preprocess_sheet = mocker.patch(
            "kbotloadscheduler.loader.basic.basic_loader.preprocess_sheet"
        )
        mock_preprocess_sheet.side_effect = [
            ("mock_content_1", {"metadata_key_1": "mock_value_1"}),
            ("mock_content_2", {"metadata_key_2": "mock_value_2"})
        ]

        # Mock create_file_with_content to prevent actual GCS operations
        # mock_create_file = mocker.patch(
        #     "kbotloadscheduler.gcs.gcs_utils.create_file_with_content",
        #     return_value=None  # Assuming no return value is needed
        # )

        # Initialize the BasicLoader
        loader = BasicLoader(self.__get_config_with_secret__(mocker))
        source = SourceBean(**BasicRepoTestData.SOURCE)
        document_list = [
            DocumentBean(
                id="cid_1",
                path="https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/proc_1.basic",
                name="Test Sheet 1",
                modification_time=datetime.strptime(
                    "2024-10-15T12:00:00+0000", "%Y-%m-%dT%H:%M:%S%z"
                )
            ),
            DocumentBean(
                id="cid_2",
                path="https://newbasic.sso.francetelecom.fr/public/ProcedureSheet/proc_2",
                name="Test Sheet 2",
                modification_time=datetime.strptime(
                    "2024-10-16T15:30:00+0000", "%Y-%m-%dT%H:%M:%S%z"
                )
            )
        ]

        output_path = BasicRepoTestData.BASE_OUTPUT

        # Call get_documents
        documents_with_metadata = []
        for i in range(len(document_list)):
            documents_with_metadata.append(loader.get_document(source, document_list[i], output_path))

        for i, doc in enumerate(document_list):
            # Expected output
            relative_file_path = "%s.basic" % doc.id
            expected_metadata = {
                f"metadata_key_{i+1}": f"mock_value_{i+1}",
                Metadata.DOCUMENT_ID: f'cid_{i+1}',
                Metadata.DOCUMENT_NAME: f'Test Sheet {i+1}',
                Metadata.LOCATION: f'gs://{BasicRepoTestData.WORK_BUCKET}/'
                                   f'{BasicRepoTestData.RELATIVE_OUTPUT}/{relative_file_path}',
                Metadata.DOMAIN_CODE: source.domain_code,
                Metadata.SOURCE_CODE: source.code
            }

            # Assertions
            assert documents_with_metadata[i] == expected_metadata
            assert my_mock_gcs.return_blob(
                BasicRepoTestData.WORK_BUCKET,
                f'{BasicRepoTestData.RELATIVE_OUTPUT}/'+relative_file_path,
                get_blob_call=True
            ) is not None
        assert len(documents_with_metadata) == 2

    def __get_config_with_secret__(self, mocker):
        config = providers.Configuration()
        config.env.from_env('ENV', 'local')
        config.gcp_project_id.from_env('GCP_PROJECT_ID', '')
        config_with_secret = ConfigWithSecret(config=config)
        mocker.patch.object(config_with_secret, "get_basic_client_id", MagicMock(return_value="fake-client-id"))
        mocker.patch.object(config_with_secret, "get_basic_client_secret", MagicMock(return_value="fake-client-pwd"))
        return config_with_secret
