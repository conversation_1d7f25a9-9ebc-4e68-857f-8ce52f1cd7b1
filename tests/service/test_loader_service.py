import json
from kbotloadscheduler.loader.gcs.gcs_loader import Gcs<PERSON>oader
from kbotloadscheduler.loader.loader_manager import LoaderManager
from kbotloadscheduler.gcs.treatment_file_manager import TreatmentFileManager
from kbotloadscheduler.service.loader_service import LoaderService
from kbotloadscheduler.bean.beans import Metadata
from testutils.mock_gcs import MockGcs
from data.gcs_repo_test_data import GcsRepoTestData
import re


class TestLoaderService:

    def test_get_document_list(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        GcsRepoTestData.write_get_list(my_mock_gcs)
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        loader_manager = LoaderManager(gcs=GcsLoader())
        loader_service = LoaderService(
            loader_manager=loader_manager,
            treatment_file_manager=TreatmentFileManager(base_bucket='gs://mon_bucket-[perimeter_code]')
        )
        get_list_file = 'gs://' + GcsRepoTestData.WORK_BUCKET + '/' + GcsRepoTestData.GET_LIST_FILE
        actual_document_list = loader_service.get_document_list(GcsRepoTestData.PERIMETER_CODE, get_list_file)

        expected_document_list = GcsRepoTestData.get_serialized_document_list_for_source()
        assert actual_document_list == expected_document_list

        expected_file_list_content = {
            'source': GcsRepoTestData.SOURCE,
            'documents': GcsRepoTestData.get_serialized_document_list_for_source()
        }
        blob_list = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, GcsRepoTestData.LIST_FILE)
        actual_file_list_content = json.loads(blob_list.download_as_string())
        assert actual_file_list_content == expected_file_list_content

        blob_done = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, GcsRepoTestData.GET_LIST_FILE_DONE,
                                            get_blob_call=True)
        assert blob_done is not None

    def test_get_document(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)
        document = GcsRepoTestData.get_serialized_document_list_for_source()[0]
        document_get_file = GcsRepoTestData.write_document_get_file(my_mock_gcs, document)

        loader_manager = LoaderManager(gcs=GcsLoader())
        loader_service = LoaderService(
            loader_manager=loader_manager,
            treatment_file_manager=TreatmentFileManager(base_bucket='gs://mon_bucket-[perimeter_code]')
        )
        actual_doc_with_md = loader_service.get_document(
            GcsRepoTestData.PERIMETER_CODE, 'gs://' + GcsRepoTestData.WORK_BUCKET + '/' + document_get_file)

        doc_id = document.get('id')
        doc_name = document.get('name')
        metadata = {
            Metadata.DOCUMENT_ID: doc_id,
            Metadata.DOCUMENT_NAME: doc_name,
            Metadata.DOMAIN_CODE: GcsRepoTestData.DOMAINE_CODE,
            Metadata.SOURCE_CODE: GcsRepoTestData.SOURCE_CODE,
            Metadata.SOURCE_TYPE: 'gcs',
            Metadata.SOURCE_CONF: json.dumps(GcsRepoTestData.CONF_SOURCE),
            Metadata.SOURCE_URL: f'gs://{GcsRepoTestData.REPO_BUCKET}/{doc_name}',
            Metadata.LOCATION:
                f'gs://{GcsRepoTestData.WORK_BUCKET}/' + f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc_name}',
            Metadata.CREATION_TIME: GcsRepoTestData.DATE_DICT.get(doc_name).get('creation_str_time'),
            Metadata.MODIFICATION_TIME: GcsRepoTestData.DATE_DICT.get(doc_name).get('modification_str_time')
        }
        expected_doc = ({'document': document, 'metadata': metadata})

        assert actual_doc_with_md == expected_doc

        doc_name = expected_doc.get('document').get('name')
        blob = my_mock_gcs.return_blob(
                GcsRepoTestData.WORK_BUCKET,
                f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc_name}',
                get_blob_call=True
            )
        assert blob is not None

        metadata_path = f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc_name}.metadata.json'
        blob_metadata = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, metadata_path)
        actual_metadata_content = json.loads(blob_metadata.download_as_string())
        assert actual_metadata_content == expected_doc.get('metadata')

        blob_done = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET,
                                            re.sub(TreatmentFileManager.JSON_REGEXP,
                                                   TreatmentFileManager.DONE, document_get_file),
                                            get_blob_call=True)
        assert blob_done is not None

    def test_get_document_error(self, mocker):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)
        document = GcsRepoTestData.get_serialized_document_list_for_source()[0]
        document['name'] = "fakedocument"
        document_get_file = GcsRepoTestData.write_document_get_file(my_mock_gcs, document)
        loader_manager = LoaderManager(gcs=GcsLoader())
        loader_service = LoaderService(
            loader_manager=loader_manager,
            treatment_file_manager=TreatmentFileManager(base_bucket='gs://mon_bucket-[perimeter_code]')
        )
        try:
            loader_service.get_document(
                GcsRepoTestData.PERIMETER_CODE, 'gs://' + GcsRepoTestData.WORK_BUCKET + '/' + document_get_file)
            assert False, "LoaderException must be raised"
        except Exception:
            assert True, "LoaderException is raised"
        file_path = re.sub(TreatmentFileManager.JSON_REGEXP, TreatmentFileManager.IN_ERROR, document_get_file)
        blob = my_mock_gcs.return_blob(
                GcsRepoTestData.WORK_BUCKET,
                file_path,
                get_blob_call=True
            )
        assert blob is not None
        assert "File gs://repo-bucket/fakedocument doesn't exists" == blob.download_as_string()
