#!/usr/bin/env python3
# -*- coding: utf-8 -*-


"""
Extraction de texte de document pour divers formats de fichier.

Ce module fournit un système complet d'extraction de document qui gère
plusieurs formats de fichier, y compris PDF, DOCX, XLSX, HTML, XML et
les fichiers texte brut. Il prend en charge le traitement spécial pour
les diagrammes draw.io et fournit des métadonnées détaillées sur le
processus d'extraction.
"""

import io
import logging
import xml.etree.ElementTree as ET
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import BinaryIO, Dict, Optional, Tuple, List

import markdownify
import openpyxl

# Document processing libraries
import pypdf
from docx import Document

from .drawio_processor import DrawIOProcessor
from .enums import MediaType, ExtractionResult
from ..utils import TextProcessor


@dataclass(frozen=True)
class ExtractorConfig:
    """Configuration for document extraction."""

    max_file_size: int = 100 * 1024 * 1024  # 100MB
    max_pdf_pages: int = 1000
    max_xlsx_rows_per_sheet: int = 10000
    encoding_fallbacks: Tuple[str, ...] = ("utf-8", "latin-1", "cp1252")
    strip_whitespace: bool = True
    include_metadata: bool = True


class BaseExtractor(ABC):
    """Abstract base class for document extractors."""

    def __init__(self, logger: logging.Logger, config: ExtractorConfig):
        self.logger = logger
        self.config = config

    @abstractmethod
    def can_handle(self, media_type: str, extension: str) -> bool:
        """Check if this extractor can handle the given media type/extension."""
        pass

    @abstractmethod
    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from the file object."""
        pass

    def _validate_file_size(
        self, content_bytes: bytes, file_name: str
    ) -> Optional[ExtractionResult]:
        """Validate file size against configured limits."""
        if len(content_bytes) > self.config.max_file_size:
            error_msg = f"File {file_name} exceeds maximum size limit ({self.config.max_file_size} bytes)"
            self.logger.warning(error_msg)
            return ExtractionResult(text="", success=False, error_message=error_msg)
        return None


class PDFExtractor(BaseExtractor):
    """Extractor for PDF documents."""

    def can_handle(self, media_type: str, extension: str) -> bool:
        return media_type == MediaType.PDF.value or extension == ".pdf"

    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from PDF file with improved error handling."""
        size_check = self._validate_file_size(content_bytes, file_name)
        if size_check:
            return size_check

        try:
            pdf_reader = pypdf.PdfReader(file_obj)
            total_pages = len(pdf_reader.pages)

            if total_pages > self.config.max_pdf_pages:
                self.logger.warning(
                    f"PDF {file_name} has {total_pages} pages, limiting to {self.config.max_pdf_pages}"
                )
                pages_to_process = self.config.max_pdf_pages
            else:
                pages_to_process = total_pages

            text_parts = []
            failed_pages = []

            for page_num in range(pages_to_process):
                try:
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_parts.append(
                            page_text.strip()
                            if self.config.strip_whitespace
                            else page_text
                        )
                except Exception as e:
                    self.logger.warning(
                        f"Failed to extract text from PDF page {page_num + 1}: {e}"
                    )
                    failed_pages.append(page_num + 1)
                    continue

            metadata = {
                "total_pages": total_pages,
                "processed_pages": pages_to_process,
                "extracted_pages": len(text_parts),
                "failed_pages": failed_pages,
            }

            if not text_parts:
                return ExtractionResult(
                    text="",
                    success=False,
                    error_message="No text could be extracted from any page",
                    metadata=metadata,
                )

            return ExtractionResult(
                text="\n\n".join(text_parts),
                success=True,
                metadata=metadata if self.config.include_metadata else {},
            )

        except Exception as e:
            error_msg = f"PDF extraction error: {e}"
            self.logger.error(f"Failed to extract from PDF {file_name}: {e}")
            return ExtractionResult(text="", success=False, error_message=error_msg)


class DOCXExtractor(BaseExtractor):
    """Extractor for DOCX documents."""

    def can_handle(self, media_type: str, extension: str) -> bool:
        return media_type == MediaType.DOCX.value or extension == ".docx"

    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from DOCX file with improved structure preservation."""
        size_check = self._validate_file_size(content_bytes, file_name)
        if size_check:
            return size_check

        try:
            doc = Document(file_obj)
            text_parts = []

            # Extract paragraph text with style information
            paragraph_count = 0
            for para in doc.paragraphs:
                if para.text and para.text.strip():
                    text = (
                        para.text.strip() if self.config.strip_whitespace else para.text
                    )
                    text_parts.append(text)
                    paragraph_count += 1

            # Extract table text with improved formatting
            table_count = 0
            for table in doc.tables:
                table_count += 1
                table_text = self._extract_table_text(table)

                if table_text:
                    text_parts.extend(
                        ["", f"--- Table {table_count} ---", *table_text, ""]
                    )

            metadata = {
                "paragraph_count": paragraph_count,
                "table_count": table_count,
                "total_tables": len(doc.tables),
            }

            return ExtractionResult(
                text="\n".join(text_parts),
                success=True,
                metadata=metadata if self.config.include_metadata else {},
            )

        except Exception as e:
            error_msg = f"DOCX extraction error: {e}"
            self.logger.error(f"Failed to extract from DOCX {file_name}: {e}")
            return ExtractionResult(text="", success=False, error_message=error_msg)

    def _extract_table_text(self, table) -> List[str]:
        """Extract text from a table with improved formatting."""
        table_text = []
        for row_idx, row in enumerate(table.rows):
            try:
                cell_texts = []
                for cell in row.cells:
                    cell_text = (
                        cell.text.strip() if self.config.strip_whitespace else cell.text
                    )
                    cell_texts.append(cell_text)

                row_text = " | ".join(cell_texts)
                if row_text.strip():
                    table_text.append(row_text)

            except Exception as e:
                self.logger.warning(f"Failed to extract table row {row_idx}: {e}")
                continue

        return table_text


class XLSXExtractor(BaseExtractor):
    """Extractor for XLSX spreadsheets."""

    def can_handle(self, media_type: str, extension: str) -> bool:
        return media_type == MediaType.XLSX.value or extension == ".xlsx"

    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from XLSX file with improved data handling."""
        size_check = self._validate_file_size(content_bytes, file_name)
        if size_check:
            return size_check

        try:
            workbook = openpyxl.load_workbook(file_obj, read_only=True, data_only=True)
            text_parts = []
            sheet_metadata = {}

            for sheet_name in workbook.sheetnames:
                try:
                    sheet = workbook[sheet_name]
                    sheet_text, row_count = self._extract_sheet_text(sheet, sheet_name)

                    if sheet_text:
                        text_parts.extend(
                            [f"--- Sheet: {sheet_name} ---", *sheet_text, ""]
                        )

                    sheet_metadata[sheet_name] = {"row_count": row_count}

                except Exception as e:
                    self.logger.warning(f"Failed to extract sheet '{sheet_name}': {e}")
                    sheet_metadata[sheet_name] = {"error": str(e)}
                    continue

            metadata = {
                "sheet_count": len(workbook.sheetnames),
                "sheets": sheet_metadata,
            }

            return ExtractionResult(
                text="\n".join(text_parts),
                success=True,
                metadata=metadata if self.config.include_metadata else {},
            )

        except Exception as e:
            error_msg = f"XLSX extraction error: {e}"
            self.logger.error(f"Failed to extract from XLSX {file_name}: {e}")
            return ExtractionResult(text="", success=False, error_message=error_msg)

    def _extract_sheet_text(self, sheet, sheet_name: str) -> Tuple[List[str], int]:
        """Extract text from a single sheet with row limits."""
        sheet_text = []
        row_count = 0

        for row_idx, row in enumerate(sheet.iter_rows(values_only=True)):
            if row_idx >= self.config.max_xlsx_rows_per_sheet:
                self.logger.warning(
                    f"Sheet '{sheet_name}' exceeds row limit, truncating at {self.config.max_xlsx_rows_per_sheet}"
                )
                break

            if any(cell is not None for cell in row):
                row_text = " | ".join(
                    (
                        str(cell).strip()
                        if cell is not None and self.config.strip_whitespace
                        else str(cell) if cell is not None else ""
                    )
                    for cell in row
                )

                if row_text.strip():
                    sheet_text.append(row_text)
                    row_count += 1

        return sheet_text, row_count


class PlainTextExtractor(BaseExtractor):
    """Extractor for plain text files."""

    def can_handle(self, media_type: str, extension: str) -> bool:
        return media_type == MediaType.TXT.value or extension in {".txt", ".md", ".csv"}

    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from plain text files with encoding detection."""
        size_check = self._validate_file_size(content_bytes, file_name)
        if size_check:
            return size_check

        text, encoding_used = self._decode_with_fallback(content_bytes)

        if text is None:
            return ExtractionResult(
                text="",
                success=False,
                error_message="Could not decode text with any supported encoding",
            )

        if self.config.strip_whitespace:
            text = text.strip()

        metadata = {
            "encoding": encoding_used,
            "size": len(content_bytes),
            "character_count": len(text),
        }

        return ExtractionResult(
            text=text,
            success=True,
            metadata=metadata if self.config.include_metadata else {},
        )

    def _decode_with_fallback(
        self, content_bytes: bytes
    ) -> Tuple[Optional[str], Optional[str]]:
        """Attempt to decode bytes with multiple encoding fallbacks."""
        for encoding in self.config.encoding_fallbacks:
            try:
                text = content_bytes.decode(encoding)
                return text, encoding
            except UnicodeDecodeError:
                continue

        # Final fallback with error replacement
        try:
            text = content_bytes.decode("utf-8", errors="replace")
            return text, "utf-8 (with replacements)"
        except Exception:
            return None, None


class HTMLExtractor(BaseExtractor):
    """Extractor for HTML documents."""

    def can_handle(self, media_type: str, extension: str) -> bool:
        return media_type == MediaType.HTML.value or extension in {".html", ".htm"}

    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from HTML content and convert to markdown."""
        size_check = self._validate_file_size(content_bytes, file_name)
        if size_check:
            return size_check

        try:
            html_content, encoding = PlainTextExtractor(
                self.logger, self.config
            )._decode_with_fallback(content_bytes)

            if html_content is None:
                return ExtractionResult(
                    text="",
                    success=False,
                    error_message="Could not decode HTML content",
                )

            # Convert HTML to markdown using markdownify
            markdown_text = markdownify.markdownify(
                html_content,
                heading_style=markdownify.ATX,  # Use # style headers
                bullets="-",  # Use - for bullet points
                strip=["script", "style"],  # Remove script and style tags
                convert=[
                    "p",
                    "div",
                    "h1",
                    "h2",
                    "h3",
                    "h4",
                    "h5",
                    "h6",
                    "ul",
                    "ol",
                    "li",
                    "strong",
                    "em",
                    "a",
                    "img",
                    "table",
                    "tr",
                    "td",
                    "th",
                    "blockquote",
                    "code",
                    "pre",
                ],
            )

            # Fallback to plain text conversion if markdownify fails or returns empty
            if not markdown_text or not markdown_text.strip():
                self.logger.warning(
                    f"Markdownify returned empty result for {file_name}, falling back to plain text"
                )
                markdown_text = TextProcessor.html_to_plain_text(html_content)
                conversion_method = "plain_text_fallback"
            else:
                conversion_method = "markdownify"

            if self.config.strip_whitespace:
                markdown_text = markdown_text.strip()

            metadata = {
                "encoding": encoding,
                "original_size": len(html_content),
                "extracted_size": len(markdown_text),
                "conversion_method": conversion_method,
            }

            return ExtractionResult(
                text=markdown_text,
                success=True,
                metadata=metadata if self.config.include_metadata else {},
            )

        except Exception as e:
            error_msg = f"HTML extraction error: {e}"
            self.logger.error(f"Failed to extract from HTML {file_name}: {e}")
            return ExtractionResult(text="", success=False, error_message=error_msg)


class XMLExtractor(BaseExtractor):
    """Extractor for XML documents."""

    def can_handle(self, media_type: str, extension: str) -> bool:
        return media_type == MediaType.XML.value or extension == ".xml"

    def extract(
        self, file_obj: BinaryIO, file_name: str, content_bytes: bytes
    ) -> ExtractionResult:
        """Extract text from generic XML files."""
        size_check = self._validate_file_size(content_bytes, file_name)
        if size_check:
            return size_check

        try:
            content, encoding = PlainTextExtractor(
                self.logger, self.config
            )._decode_with_fallback(content_bytes)

            if content is None:
                return ExtractionResult(
                    text="", success=False, error_message="Could not decode XML content"
                )

            root = ET.fromstring(content)
            text_elements = []

            self._extract_text_recursive(root, text_elements)

            extracted_text = "\n".join(text_elements)
            if self.config.strip_whitespace:
                extracted_text = extracted_text.strip()

            metadata = {
                "type": "xml",
                "encoding": encoding,
                "root_tag": root.tag,
                "element_count": len(list(root.iter())),
                "text_elements": len(text_elements),
            }

            return ExtractionResult(
                text=extracted_text,
                success=True,
                metadata=metadata if self.config.include_metadata else {},
            )

        except ET.ParseError as e:
            error_msg = f"XML parsing error: {e}"
            self.logger.error(f"Failed to parse XML {file_name}: {e}")
            return ExtractionResult(text="", success=False, error_message=error_msg)
        except Exception as e:
            error_msg = f"XML extraction error: {e}"
            self.logger.error(f"Failed to extract from XML {file_name}: {e}")
            return ExtractionResult(text="", success=False, error_message=error_msg)

    def _extract_text_recursive(
        self, element: ET.Element, text_elements: List[str]
    ) -> None:
        """Recursively extract text from XML elements."""
        if element.text and element.text.strip():
            text = (
                element.text.strip() if self.config.strip_whitespace else element.text
            )
            text_elements.append(text)

        for child in element:
            self._extract_text_recursive(child, text_elements)

        if element.tail and element.tail.strip():
            tail = (
                element.tail.strip() if self.config.strip_whitespace else element.tail
            )
            text_elements.append(tail)


class DocumentExtractor:
    """
    Enhanced document text extractor with improved architecture.

    This class orchestrates multiple specialized extractors to handle different
    document formats with better error handling, configuration, and maintainability.
    """

    def __init__(
        self, logger: logging.Logger, config: Optional[ExtractorConfig] = None
    ):
        self.logger = logger
        self.config = config or ExtractorConfig()
        self.drawio_processor = DrawIOProcessor(logger)

        # Initialize specialized extractors
        self._extractors = [
            PDFExtractor(logger, self.config),
            DOCXExtractor(logger, self.config),
            XLSXExtractor(logger, self.config),
            PlainTextExtractor(logger, self.config),
            HTMLExtractor(logger, self.config),
            XMLExtractor(logger, self.config),
        ]

        # Mapping file extensions to media types for quick lookup
        self._supported_extensions: Dict[str, str] = {
            ".pdf": MediaType.PDF.value,
            ".docx": MediaType.DOCX.value,
            ".xlsx": MediaType.XLSX.value,
            ".txt": MediaType.TXT.value,
            ".md": MediaType.TXT.value,
            ".csv": MediaType.TXT.value,
            ".html": MediaType.HTML.value,
            ".htm": MediaType.HTML.value,
            ".drawio": MediaType.DRAWIO.value,
            ".xml": MediaType.XML.value,
        }

    def can_process(self, file_name: str, media_type: str) -> bool:
        """
        Check if the document type can be processed.

        Args:
            file_name: Name of the file
            media_type: MIME type of the file

        Returns:
            True if the file can be processed, False otherwise
        """
        extension = Path(file_name).suffix.lower()

        # Check if any extractor can handle this file
        for extractor in self._extractors:
            if extractor.can_handle(media_type, extension):
                return True

        # Check for draw.io files
        if media_type == MediaType.DRAWIO.value or extension == ".drawio":
            return True

        # Check for images
        if media_type.startswith(MediaType.IMAGE.value):
            return True

        return False

    def extract_text(
        self, content_bytes: bytes, file_name: str, media_type: str
    ) -> ExtractionResult:
        """
        Extract text from document bytes based on file type.

        Args:
            content_bytes: Raw bytes of the document
            file_name: Name of the file
            media_type: MIME type of the file

        Returns:
            ExtractionResult containing extracted text and metadata
        """
        if not content_bytes:
            return ExtractionResult(
                text="", success=False, error_message="Empty file content"
            )

        try:
            extension = Path(file_name).suffix.lower()
            file_obj = io.BytesIO(content_bytes)

            # Handle draw.io files specially
            if (
                media_type == MediaType.DRAWIO.value
                or extension == ".drawio"
                or (
                    extension == ".xml"
                    and self.drawio_processor.is_drawio_xml(content_bytes)
                )
            ):
                return self._extract_drawio_text(content_bytes)

            # Try specialized extractors
            for extractor in self._extractors:
                if extractor.can_handle(media_type, extension):
                    return extractor.extract(file_obj, file_name, content_bytes)

            # Handle images
            if media_type.startswith(MediaType.IMAGE.value):
                return ExtractionResult(
                    text=f"[Image: {file_name}]",
                    success=True,
                    metadata={"type": "image", "ocr_available": False},
                )

            # Unsupported file type
            error_msg = f"Unsupported media type: {media_type}"
            self.logger.warning(f"Cannot process {file_name}: {error_msg}")
            return ExtractionResult(
                text=f"[Unsupported document: {file_name} ({media_type})]",
                success=False,
                error_message=error_msg,
            )

        except Exception as e:
            error_msg = f"Unexpected error during extraction: {e}"
            self.logger.error(
                f"Text extraction failed for {file_name}: {e}", exc_info=True
            )
            return ExtractionResult(text="", success=False, error_message=error_msg)

    def _extract_drawio_text(self, content_bytes: bytes) -> ExtractionResult:
        """Extract text and metadata from draw.io diagrams."""
        return self.drawio_processor.extract_text(content_bytes)

    def get_supported_extensions(self) -> Dict[str, str]:
        """Get a mapping of supported file extensions to media types."""
        return self._supported_extensions.copy()

    def get_config(self) -> ExtractorConfig:
        """Get the current extractor configuration."""
        return self.config
