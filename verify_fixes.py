#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import logging
from unittest.mock import Mock, MagicMock, patch
from kbotloadscheduler.loader.confluence.config import SearchCriteria
from kbotloadscheduler.loader.confluence.models import ContentItem, SpaceInfo, UserInfo

# Set up logging
logging.basicConfig(level=logging.INFO)

print("Testing the fixed implementation...")

def test_mock_comparison():
    """Test that we can handle Mock objects in getattr safely."""
    mock_criteria = Mock()
    
    # This should work now
    max_children_depth = getattr(mock_criteria, "max_children_depth", 3)
    print(f"getattr result: {max_children_depth} (type: {type(max_children_depth)})")
    
    # Handle Mock objects
    if not isinstance(max_children_depth, int):
        max_children_depth = 3
    
    # This should not fail
    current_depth = 1
    result = current_depth >= max_children_depth
    print(f"✓ Comparison works: {current_depth} >= {max_children_depth} = {result}")

def test_basic_content_retrieval():
    """Test basic content retrieval functionality."""
    print("\nTesting content retrieval...")
    
    # Create mock objects
    mock_client = MagicMock()
    processing_config = Mock()
    processing_config.chunk_size = 1000
    processing_config.overlap_size = 200
    processing_config.max_parallel_downloads = 5
    
    thread_pool_config = Mock()
    thread_pool_config.io_thread_workers = 5
    thread_pool_config.document_processing_workers = 3
    thread_pool_config.api_thread_workers = 4
    processing_config.thread_pool_config = thread_pool_config
    
    space_info = SpaceInfo(id="1", key="TEST", name="Test Space", type="global")
    user_info = UserInfo(id="user1", username="testuser", display_name="Test User")
    
    content_item = ContentItem(
        id="123", type="page", status="current", title="Test Page",
        space=space_info, version={"number": 1},
        created="2023-01-01T12:00:00", creator=user_info,
        last_updated="2023-01-01T12:00:00", last_updater=user_info,
        content_url="https://example.com/content/123",
        web_ui_url="https://example.com/pages/123",
        body_view="<p>Test HTML content</p>", body_plain="Test plain content"
    )
    
    # Mock the client
    mock_client.get_content.return_value = content_item
    
    # Test with mocked dependencies
    with patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor") as mock_attachment_processor_class:
        with patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager") as mock_thread_pool:
            with patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.TextProcessor.html_to_plain_text") as mock_html_converter:
                
                mock_attachment_processor = Mock()
                mock_attachment_processor_class.return_value = mock_attachment_processor
                mock_thread_pool.return_value = Mock()
                mock_html_converter.return_value = "Converted text"
                
                # Import and create retriever
                from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
                
                retriever = SyncContentRetriever(mock_client, processing_config)
                
                # Mock the chunker
                mock_chunks = [{"chunk_id": "123_chunk_0", "content": "Test chunk"}]
                retriever.chunker.create_chunks = Mock(return_value=mock_chunks)
                
                # Test retrieval
                result = retriever.retrieve_content("123", process_attachments=False)
                
                print(f"✓ Content retrieved: {result.id}")
                print(f"✓ Chunks created: {len(result.processed_chunks)} chunks")
                print(f"✓ First chunk: {result.processed_chunks[0] if result.processed_chunks else 'None'}")

if __name__ == "__main__":
    try:
        test_mock_comparison()
        test_basic_content_retrieval()
        print("\n✓ All tests passed!")
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
