#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de lancement des tests Confluence adaptés.

Ce script permet d'exécuter les tests adaptés depuis le système de tests
complet du module Confluence  vers l'architecture kbot-load-scheduler.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Exécute une commande et affiche le résultat."""
    print(f"\n🔄 {description}")
    print(f"Commande: {command}")
    print("-" * 60)

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ {description} - SUCCÈS")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"❌ {description} - ÉCHEC")
            if result.stderr:
                print("Erreurs:")
                print(result.stderr)
            if result.stdout:
                print("Sortie:")
                print(result.stdout)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return False


def setup_pytest_options(args):
    """Configure les options pytest selon les arguments."""
    pytest_options = []
    if args.verbose:
        pytest_options.append("-v")
    if args.coverage:
        pytest_options.extend(
            [
                "--cov=src/kbotloadscheduler/loader/confluence",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
            ]
        )
    return f"pytest {' '.join(pytest_options)}"


def run_unit_tests(pytest_base):
    """Exécute les tests unitaires adaptés."""
    commands = [
        (
            f"{pytest_base} tests/loader/confluence/test_config_adapted.py",
            "Tests de configuration adaptés",
        ),
        (
            f"{pytest_base} tests/loader/confluence/test_auth_adapted.py",
            "Tests d'authentification adaptés",
        ),
        (
            f"{pytest_base} tests/loader/confluence/test_models_adapted.py",
            "Tests de modèles adaptés",
        ),
    ]

    success_count = 0
    for command, description in commands:
        if run_command(command, description):
            success_count += 1

    return success_count, len(commands)


def run_integration_tests(pytest_base):
    """Exécute les tests d'intégration existants."""
    commands = [
        (
            f"{pytest_base} tests/loader/test_confluence_loader.py",
            "Tests d'intégration ConfluenceLoader",
        ),
        (
            f"{pytest_base} tests/loader/test_confluence_with_mock_confluence.py",
            "Tests avec MockConfluence",
        ),
        (
            f"{pytest_base} tests/loader/test_confluence_integration_with_mock_gcs.py",
            "Tests d'intégration avec MockGCS",
        ),
    ]

    success_count = 0
    for command, description in commands:
        if run_command(command, description):
            success_count += 1

    return success_count, len(commands)


def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(description="Tests Confluence adaptés")
    parser.add_argument(
        "--unit", action="store_true", help="Tests unitaires uniquement"
    )
    parser.add_argument(
        "--integration", action="store_true", help="Tests d'intégration"
    )
    parser.add_argument("--security", action="store_true", help="Tests de sécurité")
    parser.add_argument(
        "--performance", action="store_true", help="Tests de performance"
    )
    parser.add_argument("--all", action="store_true", help="Tous les tests")
    parser.add_argument(
        "--coverage", action="store_true", help="Avec couverture de code"
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="Mode verbeux")

    args = parser.parse_args()

    # Configuration de base
    base_path = Path(__file__).parent
    project_root = base_path.parent.parent.parent

    print("🧪 Tests Confluence Adaptés - kbot-load-scheduler")
    print("=" * 60)
    print(f"Répertoire de tests: {base_path}")
    print(f"Racine du projet: {project_root}")

    # Changer vers la racine du projet
    os.chdir(project_root)

    success_count = 0
    total_count = 0

    pytest_base = setup_pytest_options(args)

    # Tests unitaires adaptés
    if args.unit or args.all:
        unit_success, unit_total = run_unit_tests(pytest_base)
        success_count += unit_success
        total_count += unit_total

    # Tests d'intégration existants
    if args.integration or args.all:
        integration_success, integration_total = run_integration_tests(pytest_base)
        success_count += integration_success
        total_count += integration_total

    # Tests de sécurité (markers)
    if args.security or args.all:
        command = f"{pytest_base} -m security tests/loader/confluence/"
        total_count += 1
        if run_command(command, "Tests de sécurité"):
            success_count += 1

    # Tests de performance (si disponibles)
    if args.performance or args.all:
        command = f"{pytest_base} -m performance tests/loader/confluence/"
        total_count += 1
        if run_command(command, "Tests de performance"):
            success_count += 1

    # Si aucune option spécifique, lancer les tests de base
    if not any(
        [args.unit, args.integration, args.security, args.performance, args.all]
    ):
        print("\n📋 Lancement des tests de base (unitaires adaptés)")
        command = f"{pytest_base} tests/loader/confluence/"
        total_count += 1
        if run_command(command, "Tous les tests adaptés"):
            success_count += 1

    # Résumé final
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"✅ Tests réussis: {success_count}/{total_count}")

    if success_count == total_count:
        print("🎉 TOUS LES TESTS SONT PASSÉS !")
        return 0
    else:
        print(f"❌ {total_count - success_count} test(s) ont échoué")
        return 1


if __name__ == "__main__":
    sys.exit(main())
