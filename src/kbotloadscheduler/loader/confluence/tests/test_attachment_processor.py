#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le processeur de pièces jointes.
"""

import logging
import unittest
from unittest.mock import Mock, patch

from ..config import ProcessingConfig, StorageConfig
from ..exceptions import AttachmentProcessingError
from ..models import AttachmentDetail, UserInfo
from ..processing.sync_attachment_processor import SyncAttachmentProcessor as AttachmentProcessor
from ..processing.enums import ProcessingStatus, ExtractionResult


class TestAttachmentProcessor(unittest.TestCase):
    """Tests pour la classe AttachmentProcessor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)

        # Mock du client Confluence
        self.mock_client = Mock()

        # Configuration de traitement avec mock pour thread_pool_config
        self.processing_config = Mock()
        self.processing_config.max_parallel_downloads = 2
        self.processing_config.max_file_size = 10 * 1024 * 1024

        # Mock pour thread_pool_config avec valeurs numériques
        thread_pool_config = Mock()
        thread_pool_config.io_thread_workers = 5
        thread_pool_config.document_processing_workers = 3
        thread_pool_config.api_thread_workers = 4
        self.processing_config.thread_pool_config = thread_pool_config

        # Configuration de stockage
        self.storage_config = StorageConfig(
            attachment_extensions_to_download_raw=[".pdf", ".docx"],
            attachment_extensions_to_convert=[".txt", ".html"],
        )

        # Créer un utilisateur de test
        self.test_user = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

        # Créer une pièce jointe de test
        self.test_attachment = AttachmentDetail(
            id="att123",
            title="test.pdf",
            file_name="test.pdf",
            file_size=1024,
            media_type="application/pdf",
            download_url="https://example.com/download/test.pdf",
            created="2023-01-01T12:00:00",
            last_updated="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

    @patch("kbotloadscheduler.loader.confluence.processing.sync_attachment_processor.get_thread_pool_manager")
    def test_attachment_processor_initialization(self, mock_get_thread_pool_manager):
        """Test de l'initialisation du processeur de pièces jointes."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        processor = AttachmentProcessor(
            self.mock_client, self.processing_config, self.storage_config
        )

        self.assertEqual(processor.client, self.mock_client)
        self.assertEqual(processor.config, self.processing_config)
        self.assertEqual(processor.storage_config, self.storage_config)
        self.assertIsNotNone(processor.extractor)
        self.assertIsNotNone(processor.thread_pool_manager)

        # Vérifier les statistiques initiales
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 0)
        self.assertEqual(stats["failed"], 0)
        self.assertEqual(stats["skipped"], 0)

    def test_attachment_processor_default_config(self):
        """Test de l'initialisation avec configuration par défaut."""
        # Test que le processeur peut être initialisé sans configuration explicite
        processor = AttachmentProcessor(self.mock_client)

        # Vérifier que la configuration par défaut est utilisée
        self.assertIsNotNone(processor.config)
        self.assertIsNotNone(processor.thread_pool_manager)
        self.assertIsNotNone(processor.extractor)

        # Vérifier les statistiques initiales
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 0)
        self.assertEqual(stats["failed"], 0)
        self.assertEqual(stats["skipped"], 0)

    def test_process_attachment_raw_download(self):
        """Test de traitement d'une pièce jointe marquée pour téléchargement brut."""
        processor = AttachmentProcessor(
            self.mock_client, self.processing_config, self.storage_config
        )

        # Mock du téléchargement pour retourner des bytes
        self.mock_client.download_attachment.return_value = b"Test PDF content"

        # Mock du thread pool manager
        mock_future = Mock()
        mock_future.result.return_value = {"text": "Extracted text content"}
        processor.thread_pool_manager.submit_to_pool = Mock(return_value=mock_future)

        # Pièce jointe PDF (dans la liste des téléchargements bruts)
        pdf_attachment = AttachmentDetail(
            id="att123",
            title="document.pdf",
            file_name="document.pdf",
            file_size=1024,
            media_type="application/pdf",
            download_url="https://example.com/download/document.pdf",
            created="2023-01-01T12:00:00",
            last_updated="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        result = processor.process_attachment(pdf_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.SUCCESS.value)
        self.assertEqual(result.extracted_text, "Extracted text content")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)

    def test_process_attachment_unsupported_type(self):
        """Test de traitement d'une pièce jointe de type non supporté."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        unsupported_attachment = AttachmentDetail(
            id="att123",
            title="unknown.xyz",
            file_name="unknown.xyz",
            file_size=1024,
            media_type="application/unknown",
            download_url="https://example.com/download/unknown.xyz",
            created="2023-01-01T12:00:00",
            last_updated="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        result = processor.process_attachment(unsupported_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.SKIPPED.value)
        self.assertIn("unknown.xyz", result.extracted_text)

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["skipped"], 1)

    def test_process_attachment_successful_extraction(self):
        """Test de traitement réussi avec extraction de texte."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        test_content = b"Test PDF content"
        self.mock_client.download_attachment.return_value = test_content

        # Mock du thread pool manager
        mock_future = Mock()
        mock_future.result.return_value = {"text": "Extracted text content"}
        processor.thread_pool_manager.submit_to_pool = Mock(return_value=mock_future)

        result = processor.process_attachment(self.test_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.SUCCESS.value)
        self.assertEqual(result.extracted_text, "Extracted text content")

        # Vérifier les appels
        self.mock_client.download_attachment.assert_called_once_with(
            self.test_attachment
        )
        processor.thread_pool_manager.submit_to_pool.assert_called_once()

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)

    def test_process_attachment_extraction_failure(self):
        """Test de traitement avec échec d'extraction."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        test_content = b"Test content"
        self.mock_client.download_attachment.return_value = test_content

        # Mock du thread pool manager avec échec
        mock_future = Mock()
        mock_future.result.return_value = None  # Pas de texte extrait
        processor.thread_pool_manager.submit_to_pool = Mock(return_value=mock_future)

        result = processor.process_attachment(self.test_attachment)

        self.assertEqual(result.processing_status, ProcessingStatus.NO_TEXT.value)
        self.assertIn("test.pdf", result.extracted_text)

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)

    def test_process_attachment_download_error(self):
        """Test de traitement avec erreur de téléchargement."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement avec erreur
        self.mock_client.download_attachment.side_effect = Exception("Download failed")

        with self.assertRaises(AttachmentProcessingError):
            processor.process_attachment(self.test_attachment)

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["failed"], 1)

    def test_process_attachment_security_validation(self):
        """Test de validation de sécurité lors du traitement."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Créer une pièce jointe avec une extension dangereuse
        dangerous_attachment = AttachmentDetail(
            id="att123",
            title="malware.exe",
            file_name="malware.exe",
            file_size=1024,
            media_type="application/x-msdownload",
            download_url="https://example.com/download/malware.exe",
            created="2023-01-01T12:00:00",
            last_updated="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        with self.assertRaises(AttachmentProcessingError):
            processor.process_attachment(dangerous_attachment)

    def test_process_attachment_sequential_processing(self):
        """Test de traitement séquentiel des pièces jointes."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Mock du téléchargement
        self.mock_client.download_attachment.return_value = b"test content"

        # Mock du thread pool manager
        mock_future = Mock()
        mock_future.result.return_value = {"text": "Extracted text"}
        processor.thread_pool_manager.submit_to_pool = Mock(return_value=mock_future)

        # Créer plusieurs pièces jointes
        attachments = [
            AttachmentDetail(
                id=f"att{i}",
                title=f"test{i}.txt",
                file_name=f"test{i}.txt",
                file_size=1024,
                media_type="text/plain",
                download_url=f"https://example.com/test{i}.txt",
                created="2023-01-01T12:00:00",
                last_updated="2023-01-01T12:00:00",
                creator=self.test_user,
                content_id="content123",
            )
            for i in range(3)
        ]

        # Traiter séquentiellement
        results = [processor.process_attachment(att) for att in attachments]

        # Vérifier que tous ont été traités
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertEqual(result.processing_status, ProcessingStatus.SUCCESS.value)

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 3)

    def test_get_processing_stats(self):
        """Test de récupération des statistiques de traitement."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Modifier les statistiques internes
        processor._stats["processed"] = 5
        processor._stats["failed"] = 2
        processor._stats["skipped"] = 1

        stats = processor.get_processing_stats()

        self.assertEqual(stats["processed"], 5)
        self.assertEqual(stats["failed"], 2)
        self.assertEqual(stats["skipped"], 1)

        # Vérifier que c'est une copie (modification ne doit pas affecter l'original)
        stats["processed"] = 10
        original_stats = processor.get_processing_stats()
        self.assertEqual(original_stats["processed"], 5)

    def test_reset_stats(self):
        """Test de remise à zéro des statistiques."""
        processor = AttachmentProcessor(self.mock_client, self.processing_config)

        # Modifier les statistiques
        processor._stats["processed"] = 5
        processor._stats["failed"] = 2
        processor._stats["skipped"] = 1

        # Remettre à zéro
        processor.reset_stats()

        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 0)
        self.assertEqual(stats["failed"], 0)
        self.assertEqual(stats["skipped"], 0)

    def test_process_attachment_with_convert_and_raw_flags(self):
        """Test de traitement avec flags de conversion et téléchargement brut."""
        # Configuration avec overlap entre raw et convert
        overlap_config = StorageConfig(
            attachment_extensions_to_download_raw=[".txt", ".pdf"],
            attachment_extensions_to_convert=[".txt", ".html"],
        )

        processor = AttachmentProcessor(
            self.mock_client, self.processing_config, overlap_config
        )

        # Pièce jointe .txt (dans les deux listes)
        txt_attachment = AttachmentDetail(
            id="att123",
            title="document.txt",
            file_name="document.txt",
            file_size=1024,
            media_type="text/plain",
            download_url="https://example.com/document.txt",
            created="2023-01-01T12:00:00",
            last_updated="2023-01-01T12:00:00",
            creator=self.test_user,
            content_id="content123",
        )

        # Mock du téléchargement
        self.mock_client.download_attachment.return_value = b"test content"

        # Mock du thread pool manager
        mock_future = Mock()
        mock_future.result.return_value = {"text": "Extracted text"}
        processor.thread_pool_manager.submit_to_pool = Mock(return_value=mock_future)

        result = processor.process_attachment(txt_attachment)

        # Doit être traité (convert a priorité sur raw)
        self.assertEqual(result.processing_status, ProcessingStatus.SUCCESS.value)
        self.assertEqual(result.extracted_text, "Extracted text")

        # Vérifier les statistiques
        stats = processor.get_processing_stats()
        self.assertEqual(stats["processed"], 1)


if __name__ == "__main__":
    unittest.main()
