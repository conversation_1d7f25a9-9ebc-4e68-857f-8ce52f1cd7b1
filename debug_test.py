#!/usr/bin/env python3
"""Debug script to test the specific assertion issue."""

import tempfile
import unittest
from pathlib import Path
from types import SimpleNamespace
from unittest.mock import patch, MagicMock
from datetime import datetime

# Mock classes if they don't exist
try:
    from kbotloadscheduler.loader.confluence.config import (
        ConfluenceConfig,
        StorageConfig,
        HealthCheckConfig,
        SearchCriteria,
    )
    from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
    from kbotloadscheduler.loader.confluence.models import ContentItem, SpaceInfo, UserInfo
except ImportError as e:
    print(f"Import error: {e}")
    print("Running in environment where Confluence modules are not available")
    exit(1)

def debug_test():
    """Debug the test to see what's happening."""
    print("=== Starting debug test ===")
    
    # Setup
    temp_dir_obj = tempfile.TemporaryDirectory()
    storage_path = temp_dir_obj.name
    print(f"Temp storage path: {storage_path}")

    config = SimpleNamespace(
        confluence=ConfluenceConfig(
            url="https://mock.confluence.com",
            username="<EMAIL>",
            api_token="faketoken",
            search_criteria=SearchCriteria(
                cql="space=TEST", space_keys=["TEST"], max_results=100
            ),
        ),
        storage=StorageConfig(type="filesystem", base_dir=storage_path),
        health_check=HealthCheckConfig(enabled=False),
    )

    mock_confluence_data = [
        ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page 1",
            space=SpaceInfo(id="test-id", key="TEST", name="Test Space", type="global"),
            version={"number": 2},
            created=datetime(2023, 1, 1, 12, 0, 0),
            creator=UserInfo(id="user1", username="user1", display_name="Test User 1"),
            last_updated=datetime(2023, 1, 2, 12, 0, 0),
            last_updater=UserInfo(id="user1", username="user1", display_name="Test User 1"),
            content_url="https://mock.confluence.com/rest/api/content/123",
            web_ui_url="https://mock.confluence.com/spaces/TEST/pages/123",
            body_storage="<p>Content 1</p>",
            parent_id=None,
        ),
    ]

    print("=== Setting up mocks ===")
    with patch("kbotloadscheduler.loader.confluence.loader.ConfluenceClient") as MockConfluenceClient:
        mock_client_instance = MockConfluenceClient.return_value
        mock_client_instance.test_connection = MagicMock(return_value=True)
        mock_client_instance.search_content_by_space = MagicMock(
            return_value=mock_confluence_data
        )
        mock_client_instance.close = MagicMock()

        print("=== Creating loader ===")
        loader = ConfluenceLoader(config)
        
        print("=== Running loader ===")
        result = loader.run()
        print(f"Loader run result: {result}")

        print("=== Checking storage path ===")
        space_storage_path = Path(storage_path) / "TEST"
        print(f"Space storage path: {space_storage_path}")
        print(f"Space storage path exists: {space_storage_path.exists()}")
        
        if space_storage_path.exists():
            created_files = list(space_storage_path.glob("*.json"))
            print(f"Created files: {created_files}")
        else:
            print("Directory does not exist!")
            print(f"Storage path contents: {list(Path(storage_path).glob('*'))}")

        print("=== Checking mock calls ===")
        print(f"search_content_by_space called: {mock_client_instance.search_content_by_space.called}")
        print(f"search_content_by_space call args: {mock_client_instance.search_content_by_space.call_args}")

    temp_dir_obj.cleanup()
    print("=== Debug test completed ===")

if __name__ == "__main__":
    debug_test()
