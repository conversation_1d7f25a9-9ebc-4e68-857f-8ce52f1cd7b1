#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour la fonctionnalité de change tracking optionnel.
"""

import unittest
from datetime import datetime
from unittest.mock import patch, Mock

from pydantic import SecretStr  # Add this import

from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from ..models import ContentItem, SpaceInfo
from ..orchestrator import SyncOrchestrator


class TestOptionalChangeTracking(unittest.TestCase):
    """Tests pour le change tracking optionnel."""

    def setUp(self):
        """Configuration des tests."""
        # Les valeurs n'ont pas d'importance car tout est mocké,
        # mais il faut respecter les types pour Pydantic
        self.confluence_config = ConfluenceConfig(
            url="https://test.example.com",
            username="<EMAIL>",
            api_token=SecretStr("mock_api_token"),
        )

        self.search_criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        self.storage_config = StorageConfig(
            type="filesystem", base_dir="/tmp/test"
        )

    def test_change_tracking_enabled_by_default(self):
        """Test que le change tracking est activé par défaut."""
        processing_config = ProcessingConfig()
        self.assertTrue(processing_config.enable_change_tracking)

    def test_change_tracking_can_be_disabled(self):
        """Test que le change tracking peut être désactivé."""
        processing_config = ProcessingConfig(enable_change_tracking=False)
        self.assertFalse(processing_config.enable_change_tracking)

    @patch("kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_orchestrator_with_change_tracking_enabled(self, mock_client, mock_retriever, mock_thread_pool, mock_storage):
        """Test de l'orchestrateur avec change tracking activé."""
        # Configuration des mocks
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()

        processing_config = ProcessingConfig(enable_change_tracking=True)

        orchestrator = SyncOrchestrator(
            self.confluence_config,
            self.search_criteria,
            self.storage_config,
            processing_config,
        )

        # Vérifier que le change tracker est initialisé
        self.assertIsNotNone(orchestrator.change_tracker)

    @patch("kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_orchestrator_with_change_tracking_disabled(self, mock_client, mock_retriever, mock_thread_pool, mock_storage):
        """Test de l'orchestrateur avec change tracking désactivé."""
        # Configuration des mocks
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()

        processing_config = ProcessingConfig(enable_change_tracking=False)

        orchestrator = SyncOrchestrator(
            self.confluence_config,
            self.search_criteria,
            self.storage_config,
            processing_config,
        )

        # Vérifier que le change tracker n'est pas initialisé
        self.assertIsNone(orchestrator.change_tracker)

    @patch("kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch("kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    def test_process_content_without_change_tracking(self, mock_client, mock_retriever, mock_thread_pool, mock_storage):
        """Test du traitement de contenu sans change tracking."""
        # Configuration des mocks
        mock_storage.return_value = Mock()
        mock_thread_pool.return_value = Mock()

        processing_config = ProcessingConfig(enable_change_tracking=False)

        orchestrator = SyncOrchestrator(
            self.confluence_config,
            self.search_criteria,
            self.storage_config,
            processing_config,
        )

        # Mock du contenu
        from ..models import UserInfo

        space_info = SpaceInfo(
            id="space123", key="TEST", name="Test Space", type="global"
        )

        user_info = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

        content_item = ContentItem(
            id="123",
            title="Test Page",
            type="page",
            status="current",
            space=space_info,
            version={"number": 1},
            created=datetime.now(),
            creator=user_info,
            last_updated=datetime.now(),
            last_updater=user_info,
            content_url="https://test.atlassian.net/wiki/rest/api/content/123",
            web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/123",
            body_storage="<p>Test content</p>",
            attachments=[],
        )

        # Mock des méthodes de stockage
        with patch.object(orchestrator, "_store_content") as mock_store:
            changed_items = orchestrator._process_changed_content([content_item])

            # Vérifier que tous les contenus sont traités quand le change tracking est désactivé
            self.assertEqual(len(changed_items), 1)
            self.assertEqual(changed_items[0].id, "123")
            mock_store.assert_called_once_with(content_item)

    def test_get_sync_status_without_change_tracking(self):
        """Test du statut de synchronisation sans change tracking."""
        processing_config = ProcessingConfig(enable_change_tracking=False)

        with patch(
            "kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient"
        ), patch(
            "kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever"
        ), patch(
            "kbotloadscheduler.loader.confluence.orchestrator.get_thread_pool_manager"
        ), patch(
            "kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider"
        ):
            orchestrator = SyncOrchestrator(
                self.confluence_config,
                self.search_criteria,
                self.storage_config,
                processing_config,
            )

            status = orchestrator.get_sync_status()

            # Vérifier que le statut indique que le change tracking est désactivé
            self.assertFalse(status["change_tracking_enabled"])
            self.assertIsNone(status["last_sync"])

    def test_from_env_with_change_tracking_disabled(self):
        """Test de la configuration depuis les variables d'environnement avec change tracking désactivé."""
        with patch.dict("os.environ", {"CONFLUENCE_ENABLE_CHANGE_TRACKING": "false"}):
            config = ProcessingConfig.from_env()
            self.assertFalse(config.enable_change_tracking)

    def test_from_env_with_change_tracking_enabled(self):
        """Test de la configuration depuis les variables d'environnement avec change tracking activé."""
        with patch.dict("os.environ", {"CONFLUENCE_ENABLE_CHANGE_TRACKING": "true"}):
            config = ProcessingConfig.from_env()
            self.assertTrue(config.enable_change_tracking)


if __name__ == "__main__":
    unittest.main()
