#!/usr/bin/env python3

import unittest
from unittest.mock import Mock, patch
from src.kbotloadscheduler.loader.confluence.exceptions import ContentProcessingError
from src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
from src.kbotloadscheduler.loader.confluence.config import ProcessingConfig

class TestDebugError(unittest.TestCase):

    def setUp(self):
        self.mock_client = Mock()
        
        # Create a simple config
        self.processing_config = Mock()
        self.processing_config.chunk_size = 1000
        self.processing_config.overlap_size = 200
        self.processing_config.max_parallel_downloads = 5
        self.processing_config.max_children_depth = 3
        
        # Mock thread pool config
        thread_pool_config = Mock()
        thread_pool_config.io_thread_workers = 5
        thread_pool_config.document_processing_workers = 3
        thread_pool_config.api_thread_workers = 4
        self.processing_config.thread_pool_config = thread_pool_config
        self.processing_config.max_thread_workers = 4

    @patch("src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    @patch("src.kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    def test_simple_error_handling(self, mock_get_thread_pool_manager, mock_attachment_processor_class):
        """Simple test for error handling."""
        # Mock the thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager
        
        # Mock the attachment processor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor
        
        # Create retriever
        retriever = SyncContentRetriever(self.mock_client, self.processing_config)
        
        # Set up the mock client to raise an exception
        self.mock_client.get_content.side_effect = Exception("Test API Error")
        
        # Test that the correct exception is raised
        try:
            retriever.retrieve_content("123")
            self.fail("Expected ContentProcessingError to be raised")
        except ContentProcessingError as e:
            print(f"Successfully caught ContentProcessingError: {e}")
            self.assertIn("Test API Error", str(e))
        except Exception as e:
            self.fail(f"Expected ContentProcessingError but got {type(e).__name__}: {e}")
        
        # Check stats
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["errors"], 1)
        
        print("Test passed!")

if __name__ == "__main__":
    unittest.main()
