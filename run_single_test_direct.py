#!/usr/bin/env python3

import sys
import os

# Add src and tests to path
sys.path.insert(0, 'src')
sys.path.insert(0, 'tests')

def run_test():
    try:
        # Import the test function
        from tests.loader.test_confluence_with_mock_confluence import test_get_document_with_mock_confluence_and_gcs
        
        print("Running test_get_document_with_mock_confluence_and_gcs...")
        test_get_document_with_mock_confluence_and_gcs()
        print('✅ TEST PASSED: test_get_document_with_mock_confluence_and_gcs')
        return True
        
    except Exception as e:
        print(f'❌ TEST FAILED: {type(e).__name__}: {e}')
        import traceback
        traceback.print_exc()
        return False

def run_second_test():
    try:
        # Import the second test function
        from tests.loader.test_confluence_with_mock_confluence import test_mock_confluence_client_integration
        
        print("\nRunning test_mock_confluence_client_integration...")
        test_mock_confluence_client_integration()
        print('✅ TEST PASSED: test_mock_confluence_client_integration')
        return True
        
    except Exception as e:
        print(f'❌ TEST FAILED: {type(e).__name__}: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Running both previously failing tests...")
    test1_passed = run_test()
    test2_passed = run_second_test()
    
    print(f"\n=== SUMMARY ===")
    print(f"test_get_document_with_mock_confluence_and_gcs: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"test_mock_confluence_client_integration: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed")
