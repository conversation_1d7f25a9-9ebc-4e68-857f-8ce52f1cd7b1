# test_basic_loader.py
from unittest.mock import MagicMock

from dependency_injector import providers
from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from data.sharepoint_repo_test_data import SharepointRepoTestData
import os
import json
import re
from datetime import datetime, timezone

from src.kbotloadscheduler.loader.sharepoint import sharepoint_loader
from src.kbotloadscheduler.loader.sharepoint.sharepoint_loader import SharepointLoader
from src.kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from tests.testutils.mock_gcs import MockGcs


class TestSharepointLoader:

    def test_get_single_subfolder_with_unique_id(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'UniqueId'
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        files_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderById\\(", "")
            + "'.*'\\)/files\\?\\$select")
        requests_mock.get(files_url_matcher,
                          json=SharepointRepoTestData.GET_SINGLE_SUBFOLDER_FILES)
        folders_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderById\\(", "")
            + "'.*'\\)/folders\\?\\$select")
        requests_mock.get(folders_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_EMPTY_FOLDERS)

        folder_infos = SharepointRepoTestData.SINGLE_FOLDER_INFO_INPUT

        document_list = loader.get_files_and_subfolders(sharepoint_client, folder_infos)
        assert len(document_list) == 2
        assert document_list[0]['Name'] == "Benchmark Excellence _ 230417.pdf"
        assert document_list[1]['Name'] == "EtudeMarche2024.pdf"

    def test_get_document_list_with_unique_id(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'UniqueId'
        config_with_secret = self.__init_config__(mocker)

        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        root_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativePath\\(decodedurl=", "")
            + "'.*'\\)\\?\\$select")
        requests_mock.get(root_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT)
        files_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderById\\(", "")
            + "'.*'\\)/files\\?\\$select")
        requests_mock.get(files_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_FILES)
        folders_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderById\\(", "")
            + "'.*'\\)/folders\\?\\$select")
        requests_mock.get(folders_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_EMPTY_FOLDERS)
        # Call get_document_list
        actual_document_list = loader.get_document_list(source)
        # Assertions
        assert actual_document_list[0].id == 'domA|srcA1|af7b286c-027b-4a20-b9b7-02b752b87b53'
        assert len(actual_document_list) == 63

    def test_get_document_with_unique_id(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'UniqueId'
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("fakebucket")
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        file_relative_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFileById\\(", "") + "'.*'\\)\\?\\$select")
        requests_mock.get(file_relative_url_matcher,
                          json=SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS)
        file_get_content = re.compile(
            sharepoint_client.build_full_url("GetFileById\\(", "") + "'.*'\\)/\\$value")
        requests_mock.get(file_get_content,
                          text="fake content")
        document_to_load = DocumentBean(
            id='domA|srcA1|af7b286c-027b-4a20-b9b7-02b752b87b53',
            name='domA/srcA1/chemin/fichier.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 9, 12, 12, 42, 36)
        )
        actual_metadata = loader.get_document(source, document_to_load, "gs://fakebucket/sharepointdoc/")
        assert actual_metadata == {
            'creationDate': datetime.fromisoformat('2023-07-21T07:55:32').astimezone(timezone.utc),
            'document_id': 'domA|srcA1|af7b286c-027b-4a20-b9b7-02b752b87b53',
            'document_name': 'domA/srcA1/chemin/fichier.pdf',
            'location': 'gs://fakebucket/sharepointdoc//domA_srcA1_chemin_fichier.pdf',
            'modificationDate': datetime.fromisoformat('2023-09-20T09:13:04').astimezone(timezone.utc),
            'source_path': 'https://orange0.sharepoint.com/domA/srcA1/chemin/fichier.pdf'
        }
        assert my_mock_gcs.return_blob(
            "fakebucket",
            "sharepointdoc//domA_srcA1_chemin_fichier.pdf",
            get_blob_call=True
        ) is not None
        assert my_mock_gcs.return_blob(
            "fakebucket",
            "sharepointdoc//domA_srcA1_chemin_fichier.pdf",
            get_blob_call=True).download_as_string() == b"fake content"

    def test_get_documents_with_unique_id(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'UniqueId'
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("fakebucket")
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        file_relative_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFileById\\(", "") + "'.*'\\)\\?\\$select")
        requests_mock.get(file_relative_url_matcher,
                          json=SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS)
        file_get_content = re.compile(
            sharepoint_client.build_full_url("GetFileById\\(", "") + "'.*'\\)/\\$value")
        requests_mock.get(file_get_content,
                          text="fake content")

        document1_to_load = DocumentBean(
            id='domA|srcA1|2dcc222b-a7e8-4396-9395-39dc9efce914_1',
            name='domA/srcA1/chemin/doc1.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 8, 12, 12, 42, 36))

        document2_to_load = DocumentBean(
            id='domA|srcA1|2dcc222b-a7e8-4396-9395-39dc9efce914_2',
            name='domA/srcA1/chemin/doc2.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 9, 12, 12, 42, 36)
        )
        metadatas = [
            loader.get_document(source, document1_to_load, "gs://fakebucket/sharepointdoc/"),
            loader.get_document(source, document2_to_load, "gs://fakebucket/sharepointdoc/")
        ]
        assert metadatas[0]['document_id'] == 'domA|srcA1|2dcc222b-a7e8-4396-9395-39dc9efce914_1'
        assert metadatas[1]['document_id'] == 'domA|srcA1|2dcc222b-a7e8-4396-9395-39dc9efce914_2'

    def test_get_single_subfolder_with_server_relative_url(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'ServerRelativeUrl'
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        files_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativePath\\(decodedurl=", "")
            + "'.*'\\)/files\\?\\$select")
        requests_mock.get(files_url_matcher,
                          json=SharepointRepoTestData.GET_SINGLE_SUBFOLDER_FILES)
        folders_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativePath\\(decodedurl=", "")
            + "'.*'\\)/folders\\?\\$select")
        requests_mock.get(folders_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_EMPTY_FOLDERS)

        folder_infos = SharepointRepoTestData.SINGLE_FOLDER_INFO_INPUT

        document_list = loader.get_files_and_subfolders(sharepoint_client, folder_infos)
        assert len(document_list) == 2
        assert document_list[0]['Name'] == "Benchmark Excellence _ 230417.pdf"
        assert document_list[1]['Name'] == "EtudeMarche2024.pdf"

    def test_get_document_list_with_server_relative_url(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'ServerRelativeUrl'
        config_with_secret = self.__init_config__(mocker)

        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        root_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativePath\\(decodedurl=", "")
            + "'.*'\\)\\?\\$select")
        requests_mock.get(root_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_ROOT)
        files_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativePath\\(decodedurl=", "")
            + "'.*'\\)/files\\?\\$select")
        requests_mock.get(files_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_FILES)
        folders_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFolderByServerRelativePath\\(decodedurl=", "")
            + "'.*'\\)/folders\\?\\$select")
        requests_mock.get(folders_url_matcher,
                          json=SharepointRepoTestData.GET_FOLDER_BY_SERVER_EMPTY_FOLDERS)
        # Call get_document_list
        actual_document_list = loader.get_document_list(source)
        # Assertions
        assert actual_document_list[0].id == \
               'domA|srcA1|/sites/EquipeIAGDCCM/Documents partages/Test Knowledge Bot/' \
               'Etudes Baromètres transverses (baro strat, touch point...)/Benchmark Excellence _ 230417.pdf'
        assert len(actual_document_list) == 63

    def test_get_document_with_server_relative_url(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'ServerRelativeUrl'
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("fakebucket")
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        file_relative_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativePath\\(decodedurl=", "") + "'.*'\\)\\?\\$select")
        requests_mock.get(file_relative_url_matcher,
                          json=SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS)
        file_get_content = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativePath\\(decodedurl=", "") + "'.*'\\)/\\$value")
        requests_mock.get(file_get_content,
                          text="fake content")
        document_to_load = DocumentBean(
            id='domA|srcA1|domA/srcA1/chemin/fichier.pdf',
            name='domA/srcA1/chemin/fichier.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 9, 12, 12, 42, 36)
        )
        actual_metadata = loader.get_document(source, document_to_load, "gs://fakebucket/sharepointdoc/")
        assert actual_metadata == {
            'creationDate': datetime.fromisoformat('2023-07-21T07:55:32').astimezone(timezone.utc),
            'document_id': 'domA|srcA1|domA/srcA1/chemin/fichier.pdf',
            'document_name': 'domA/srcA1/chemin/fichier.pdf',
            'location': 'gs://fakebucket/sharepointdoc//domA_srcA1_chemin_fichier.pdf',
            'modificationDate': datetime.fromisoformat('2023-09-20T09:13:04').astimezone(timezone.utc),
            'source_path': 'https://orange0.sharepoint.com/domA/srcA1/chemin/fichier.pdf'
        }
        assert my_mock_gcs.return_blob(
            "fakebucket",
            "sharepointdoc//domA_srcA1_chemin_fichier.pdf",
            get_blob_call=True
        ) is not None
        assert my_mock_gcs.return_blob(
            "fakebucket",
            "sharepointdoc//domA_srcA1_chemin_fichier.pdf",
            get_blob_call=True).download_as_string() == b"fake content"

    def test_get_documents_with_server_relative_url(self, mocker, requests_mock):
        os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'ServerRelativeUrl'
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket("fakebucket")
        config_with_secret = self.__init_config__(mocker)
        loader = SharepointLoader(config_with_secret)
        source = SourceBean(**SharepointRepoTestData.SOURCE)
        sharepoint_client = loader.get_sharepoint_client(source)
        file_relative_url_matcher = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativePath\\(decodedurl=", "") + "'.*'\\)\\?\\$select")
        requests_mock.get(file_relative_url_matcher,
                          json=SharepointRepoTestData.GET_FILE_BY_SERVER_INFOS)
        file_get_content = re.compile(
            sharepoint_client.build_full_url("GetFileByServerRelativePath\\(decodedurl=", "") + "'.*'\\)/\\$value")
        requests_mock.get(file_get_content,
                          text="fake content")

        document1_to_load = DocumentBean(
            id='domA|srcA1|domA/srcA1/chemin/doc1.pdf',
            name='domA/srcA1/chemin/doc1.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 8, 12, 12, 42, 36))

        document2_to_load = DocumentBean(
            id='domA|srcA1|domA/srcA1/chemin/doc2.pdf',
            name='domA/srcA1/chemin/doc2.pdf',
            path='gs://fakebucket/sharepointdoc',
            modification_time=datetime(2024, 9, 12, 12, 42, 36)
        )
        metadatas = [
            loader.get_document(source, document1_to_load, "gs://fakebucket/sharepointdoc/"),
            loader.get_document(source, document2_to_load, "gs://fakebucket/sharepointdoc/")
        ]
        assert metadatas[0]['document_id'] == 'domA|srcA1|domA/srcA1/chemin/doc1.pdf'
        assert metadatas[1]['document_id'] == 'domA|srcA1|domA/srcA1/chemin/doc2.pdf'

    @staticmethod
    def __init_config__(mocker):
        config = providers.Configuration()
        config.env.from_env('ENV', 'local')
        config.gcp_project_id.from_env('GCP_PROJECT_ID', '')
        config_with_secret = ConfigWithSecret(config=config)
        config_with_secret.get_sharepoint_client_config = MagicMock(
            return_value=json.loads(SharepointRepoTestData.FAKE_CLIENT_CONFIG))
        config_with_secret.get_sharepoint_client_private_key = MagicMock(return_value="private_key")

        def get_token(obj):
            return "fake_token"

        mocker.patch.object(
            sharepoint_loader.SharepointCredentials, "get_access_token", get_token)

        return config_with_secret
