#!/usr/bin/env python3

from unittest.mock import Mo<PERSON>

def test_mock_getattr():
    """Test how getattr works with Mock objects."""
    
    # Create a Mock object
    mock_obj = Mock()
    
    # Test getattr behavior
    print("Testing getattr with Mock objects:")
    
    # This will create a Mock attribute since it doesn't exist
    result1 = getattr(mock_obj, "non_existent_attr", 42)
    print(f"getattr(mock, 'non_existent_attr', 42) = {result1} (type: {type(result1)})")
    print(f"Is it a Mock? {isinstance(result1, Mock)}")
    
    # Now the attribute exists as a Mock
    result2 = getattr(mock_obj, "non_existent_attr", 42)
    print(f"Second call: getattr(mock, 'non_existent_attr', 42) = {result2} (type: {type(result2)})")
    print(f"Is it a Mock? {isinstance(result2, Mock)}")
    
    # Test hasattr
    print(f"hasattr(mock, 'non_existent_attr') = {hasattr(mock_obj, 'non_existent_attr')}")
    
    # Test comparison with <PERSON><PERSON>
    try:
        comparison_result = 1 >= result1
        print(f"1 >= Mock = {comparison_result}")
    except Exception as e:
        print(f"Comparison failed: {e}")

if __name__ == "__main__":
    test_mock_getattr()
