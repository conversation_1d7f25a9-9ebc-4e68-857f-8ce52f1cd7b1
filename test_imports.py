#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from kbotloadscheduler.loader.confluence.processing.sync_attachment_processor import SyncAttachmentProcessor
    print('✓ SyncAttachmentProcessor imported successfully')
except ImportError as e:
    print(f'❌ Failed to import SyncAttachmentProcessor: {e}')

try:
    from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
    print('✓ SyncContentRetriever imported successfully')
except ImportError as e:
    print(f'❌ Failed to import SyncContentRetriever: {e}')

try:
    from kbotloadscheduler.loader.confluence.config import ProcessingConfig
    print('✓ ProcessingConfig imported successfully')
except ImportError as e:
    print(f'❌ Failed to import ProcessingConfig: {e}')
