from typing import Optional

from pydantic import BaseModel, field_validator

# Définir une constante pour le nombre maximum de workers
MAX_THREAD_WORKERS = 32


class ThreadPoolConfig(BaseModel):
    io_thread_workers: Optional[int] = None
    document_processing_workers: Optional[int] = None
    api_thread_workers: Optional[int] = None

    @field_validator(
        "io_thread_workers", "document_processing_workers", "api_thread_workers"
    )
    @classmethod
    def validate_workers(cls, v: Optional[int]) -> Optional[int]:
        if v is None:
            return v

        if v <= 0:
            raise ValueError("Worker count must be positive")

        if v > MAX_THREAD_WORKERS:
            raise ValueError(f"Worker count cannot exceed {MAX_THREAD_WORKERS}")

        return v
