#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour migrer automatiquement les tests Confluence qui utilisent asyncio
vers des tests synchrones.

Usage:
    python scripts/migrate_async_tests.py [--dry-run]

Options:
    --dry-run  Affiche les modifications sans les appliquer
"""

import argparse
import ast
import os
import re
from pathlib import Path


def find_test_files(base_path):
    """Trouve tous les fichiers de test Python liés à Confluence"""
    all_test_files = []
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if file.startswith("test_") and file.endswith(".py"):
                file_path = os.path.join(root, file)
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    if "confluence" in content.lower():
                        all_test_files.append(file_path)
    return all_test_files


def check_for_asyncio(file_path):
    """Vérifie si un fichier utilise asyncio avec le module ConfluenceLoader"""
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Recherche des imports d'asyncio
    has_asyncio_import = re.search(r'import\s+asyncio', content) is not None

    # Recherche des appels à asyncio.run() avec loader.run()
    has_asyncio_run_loader = re.search(r'asyncio\.run\s*\(\s*loader\.run\s*\(', content) is not None

    # Recherche d'AsyncMock
    has_async_mock = re.search(r'AsyncMock', content) is not None

    # Recherche de décorateurs async
    has_async_def = re.search(r'async\s+def', content) is not None

    return {
        "has_asyncio_import": has_asyncio_import,
        "has_asyncio_run_loader": has_asyncio_run_loader,
        "has_async_mock": has_async_mock,
        "has_async_def": has_async_def,
        "needs_migration": has_asyncio_import or has_asyncio_run_loader or has_async_mock or has_async_def
    }


def migrate_test_file(file_path, dry_run=False):
    """Migre un fichier de test de l'utilisation asyncio vers synchrone"""
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Effectuer les remplacements nécessaires
    modified_content = content

    # 1. Remplacer asyncio.run(loader.run()) par loader.run()
    modified_content = re.sub(
        r'asyncio\.run\s*\(\s*loader\.run\s*\(\s*\)\s*\)',
        'loader.run()',
        modified_content
    )
    modified_content = re.sub(
        r'asyncio\.run\s*\(\s*loader\.run\s*\(([^)]+)\)\s*\)',
        r'loader.run(\1)',
        modified_content
    )

    # 2. Remplacer AsyncMock par MagicMock
    modified_content = re.sub(r'AsyncMock', 'MagicMock', modified_content)

    # 3. Supprimer l'import d'asyncio s'il n'est plus utilisé
    if not re.search(r'asyncio\.\w+', modified_content.replace('asyncio.run(loader.run', '')):
        modified_content = re.sub(r'import\s+asyncio\s*\n', '', modified_content)
        # Supprimer aussi les imports de type "from asyncio import X"
        modified_content = re.sub(r'from\s+asyncio\s+import\s+[^;\n]+\n', '', modified_content)

    # 4. Convertir les fonctions async def en def normales si elles sont liées à Confluence
    # Cette partie est plus complexe et pourrait nécessiter une analyse AST complète
    # Pour l'instant, nous détectons simplement les lignes potentielles
    async_def_lines = []
    for line_num, line in enumerate(modified_content.splitlines(), 1):
        if re.search(r'async\s+def\s+test_\w+', line):
            async_def_lines.append((line_num, line))

    if async_def_lines:
        print(f"Attention : {file_path} contient des fonctions de test asynchrones qui pourraient nécessiter une migration manuelle :")
        for line_num, line in async_def_lines:
            print(f"  - Ligne {line_num}: {line.strip()}")
        modified_content = re.sub(r'async\s+def\s+', 'def ', modified_content)
        print(f"  Les déclarations 'async def' ont été converties en 'def', mais vous devrez peut-être examiner manuellement ce fichier.")
        print(f"  Vérifiez si des appels 'await' subsistent dans ces fonctions.\n")

    # 5. Rechercher et signaler les appels await qui pourraient nécessiter une attention
    await_matches = re.findall(r'await\s+\w+', modified_content)
    if await_matches:
        print(f"Attention : {file_path} contient toujours des expressions 'await' qui doivent être examinées manuellement :")
        for match in set(await_matches):
            print(f"  - {match}")
        print(f"  Celles-ci n'ont pas été modifiées automatiquement car elles nécessitent une analyse contextuelle.\n")

    # Si du contenu a été modifié et que nous ne sommes pas en mode simulation
    if content != modified_content and not dry_run:
        print(f"Modification du fichier {file_path}")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(modified_content)
        return True
    elif content != modified_content:
        print(f"[DRY RUN] Le fichier {file_path} serait modifié")
        for i, (old_line, new_line) in enumerate(zip(content.splitlines(), modified_content.splitlines())):
            if old_line != new_line:
                print(f"  Ligne {i+1}:")
                print(f"    - {old_line}")
                print(f"    + {new_line}")
        return True
    else:
        print(f"Aucune modification nécessaire pour {file_path}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Migre les tests Confluence de asyncio vers synchrone")
    parser.add_argument("--dry-run", action="store_true", help="Affiche les modifications sans les appliquer")
    args = parser.parse_args()

    # Chemin du projet
    project_path = Path(__file__).parent.parent
    print(f"Analyse du projet dans {project_path}")

    # Trouvez tous les fichiers de test liés à Confluence
    test_files = find_test_files(project_path / "src")
    test_files.extend(find_test_files(project_path / "tests"))
    test_files.extend(find_test_files(project_path / "test"))
    print(f"Trouvé {len(test_files)} fichiers de test potentiellement liés à Confluence")

    # Analyser et migrer chaque fichier si nécessaire
    files_to_migrate = []
    for file_path in test_files:
        result = check_for_asyncio(file_path)
        if result["needs_migration"]:
            files_to_migrate.append((file_path, result))

    print(f"\n{len(files_to_migrate)} fichiers nécessitent une migration\n")

    # Migrer les fichiers identifiés
    migrated_files = 0
    for file_path, result in files_to_migrate:
        print(f"\nAnalyse de {file_path}")
        print(f"  - Utilise import asyncio: {result['has_asyncio_import']}")
        print(f"  - Utilise asyncio.run(loader.run()): {result['has_asyncio_run_loader']}")
        print(f"  - Utilise AsyncMock: {result['has_async_mock']}")
        print(f"  - Contient des fonctions async def: {result['has_async_def']}")

        if migrate_test_file(file_path, args.dry_run):
            migrated_files += 1

    action_verb = "seraient" if args.dry_run else "ont été"
    print(f"\n{migrated_files} fichiers sur {len(files_to_migrate)} {action_verb} modifiés")
    if args.dry_run and migrated_files > 0:
        print("\nExecutez sans --dry-run pour appliquer les modifications")


if __name__ == "__main__":
    main()
