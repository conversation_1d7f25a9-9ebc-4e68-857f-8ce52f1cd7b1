#!/usr/bin/env python3
"""
Script pour corriger automatiquement les imports confluence_rag vers kbotloadscheduler.loader.confluence
"""

import os
import re
import sys

def fix_imports_in_file(file_path):
    """Corrige les imports dans un fichier donné."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacer tous les imports confluence_rag par kbotloadscheduler.loader.confluence
        original_content = content
        content = re.sub(
            r'confluence_rag\.', 
            'kbotloadscheduler.loader.confluence.', 
            content
        )
        
        # Remplacer les imports directs de confluence_rag
        content = re.sub(
            r'from confluence_rag import', 
            'from kbotloadscheduler.loader.confluence import', 
            content
        )
        content = re.sub(
            r'import confluence_rag', 
            'import kbotloadscheduler.loader.confluence', 
            content
        )
        
        # Sauvegarder seulement si des changements ont été faits
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Corrigé: {file_path}")
            return True
        else:
            print(f"- Aucun changement: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Erreur avec {file_path}: {e}")
        return False

def main():
    """Fonction principale."""
    test_files = [
        "src/kbotloadscheduler/loader/confluence/tests/test_exceptions.py",
        "src/kbotloadscheduler/loader/confluence/tests/__init__.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_storage.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_gitlab_ci_quick.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_tracking.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_quick_validation.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_performance_optimization.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_content_retriever.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_document_extractors.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_gitlab_ci_simulation.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_log_security.py",
        "src/kbotloadscheduler/loader/confluence/tests/run_integration_tests.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_specific_failures.py",
        "src/kbotloadscheduler/loader/confluence/tests/test_tracking_gcs.py"
    ]
    
    fixed_count = 0
    total_count = 0
    
    for file_path in test_files:
        if os.path.exists(file_path):
            total_count += 1
            if fix_imports_in_file(file_path):
                fixed_count += 1
        else:
            print(f"⚠ Fichier non trouvé: {file_path}")
    
    print(f"\nRésumé: {fixed_count}/{total_count} fichiers corrigés")

if __name__ == "__main__":
    main()
