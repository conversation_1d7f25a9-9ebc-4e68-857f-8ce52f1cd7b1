#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le récupérateur de contenu.
"""

import logging
import unittest
from unittest.mock import Mock, MagicMock, patch
import pytest

from ..config import SearchCriteria
from ..exceptions import ContentProcessingError
from ..models import ContentItem, SpaceInfo, UserInfo
from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import (
    SyncContentRetriever as ContentRetriever,
)


class TestContentRetriever(unittest.TestCase):
    """Tests pour la classe ContentRetriever."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)

        # Mock du client Confluence
        self.mock_client = MagicMock()

        # Configuration de traitement avec mock pour thread_pool_config
        self.processing_config = Mock()
        self.processing_config.chunk_size = 1000
        self.processing_config.overlap_size = 200
        self.processing_config.max_parallel_downloads = 5
        self.processing_config.max_children_depth = 3

        # Mock pour thread_pool_config avec valeurs numériques
        thread_pool_config = Mock()
        thread_pool_config.io_thread_workers = 5
        thread_pool_config.document_processing_workers = 3
        thread_pool_config.api_thread_workers = 4
        self.processing_config.thread_pool_config = thread_pool_config
        
        # Add max_thread_workers to the processing config mock
        self.processing_config.max_thread_workers = 4

        # Créer des objets de test
        self.space_info = SpaceInfo(
            id="1", key="TEST", name="Test Space", type="global"
        )

        self.user_info = UserInfo(
            id="user1", username="testuser", display_name="Test User"
        )

        self.content_item = ContentItem(
            id="123",
            type="page",
            status="current",
            title="Test Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123",
            body_view="<p>Test HTML content</p>",
            body_plain="Test plain content",
        )

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    def test_content_retriever_initialization(self, mock_attachment_processor_class):
        """Test de l'initialisation du récupérateur de contenu."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        self.assertEqual(retriever.client, self.mock_client)
        self.assertEqual(retriever.config, self.processing_config)
        # Check that the attachment processor instance was created correctly
        mock_attachment_processor_class.assert_called_once()
        self.assertEqual(retriever.attachment_processor, mock_attachment_processor)
        self.assertIsNotNone(retriever.chunker)

        # Vérifier les statistiques initiales
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 0)
        self.assertEqual(stats["attachments_processed"], 0)
        self.assertEqual(stats["errors"], 0)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    @patch("kbotloadscheduler.loader.confluence.config.ProcessingConfig.from_env")
    def test_content_retriever_default_config(
        self, mock_from_env, mock_get_thread_pool_manager, mock_attachment_processor_class
    ):
        """Test de l'initialisation avec configuration par défaut."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        # Mock de la configuration par défaut
        mock_config = Mock()
        mock_config.chunk_size = 1500
        mock_config.overlap_size = 300

        # Mock pour thread_pool_config avec valeurs numériques
        thread_pool_config = Mock()
        thread_pool_config.io_thread_workers = 5
        thread_pool_config.document_processing_workers = 3
        thread_pool_config.api_thread_workers = 4
        thread_pool_config.thread_name_prefix = "Test-Worker"
        thread_pool_config.max_queue_size = 100
        mock_config.thread_pool_config = thread_pool_config
        
        # Add max_thread_workers to the mock config
        mock_config.max_thread_workers = 4
        mock_config.max_children_depth = 3

        mock_from_env.return_value = mock_config

        retriever = ContentRetriever(self.mock_client)

        # Check that the configuration was used
        mock_from_env.assert_called_once()
        # The actual config should be the mocked one
        self.assertEqual(retriever.config, mock_config)
        # Check that the thread pool manager was called
        mock_get_thread_pool_manager.assert_called_once_with(mock_config.thread_pool_config)
        # Check that the attachment processor was created
        mock_attachment_processor_class.assert_called_once()

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    def test_retrieve_content_success(self, mock_attachment_processor_class):
        """Test de récupération de contenu réussie."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.process_attachment = MagicMock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock du client pour retourner le contenu
        self.mock_client.get_content.return_value = self.content_item

        # Mock du chunker
        mock_chunks = [
            {"chunk_id": "123_chunk_0", "content": "Test chunk 1"},
            {"chunk_id": "123_chunk_1", "content": "Test chunk 2"},
        ]
        retriever.chunker.create_chunks = Mock(return_value=mock_chunks)

        result = retriever.retrieve_content("123", process_attachments=True)

        # Vérifications
        self.assertEqual(result.id, "123")
        self.assertEqual(result.title, "Test Page")
        self.assertEqual(result.processed_chunks, mock_chunks)

        # Vérifier les appels
        self.mock_client.get_content.assert_called_once_with("123")
        retriever.chunker.create_chunks.assert_called_once_with(result)

        # Vérifier les statistiques
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 1)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    def test_retrieve_content_without_attachments(
        self, mock_get_thread_pool_manager, mock_attachment_processor_class
    ):
        """Test de récupération de contenu sans traitement des pièces jointes."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock du client
        self.mock_client.get_content.return_value = self.content_item

        # Mock du chunker
        mock_chunks = [{"chunk_id": "123_chunk_0", "content": "Test chunk"}]
        retriever.chunker.create_chunks = Mock(return_value=mock_chunks)

        result = retriever.retrieve_content("123", process_attachments=False)

        # Vérifications
        self.assertEqual(result.id, "123")
        self.assertEqual(result.processed_chunks, mock_chunks)

        # Vérifier que le processeur de pièces jointes n'a pas été appelé
        retriever.attachment_processor.process_attachment = MagicMock()
        retriever.attachment_processor.process_attachment.assert_not_called()

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    def test_retrieve_content_error_handling(self, mock_get_thread_pool_manager, mock_attachment_processor_class):
        """Test de gestion d'erreur lors de la récupération via search_and_retrieve."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Mock du client pour lever une exception lors de get_content
        self.mock_client.get_content.side_effect = Exception("API Error")
        
        # Mock de search_content pour retourner un résultat
        search_results = [Mock(id="123", title="Test Page")]
        self.mock_client.search_content.return_value = search_results

        # Create SearchCriteria
        from ..config import SearchCriteria
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        # Appeler search_and_retrieve qui doit gérer l'erreur sans lever d'exception
        results = retriever.search_and_retrieve(criteria)
        
        # Vérifier qu'aucun résultat n'est retourné en cas d'erreur
        self.assertEqual(results, [])

        # Vérifier les statistiques d'erreur (errors are counted in both methods)
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["errors"], 2)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")  
    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    def test_search_and_retrieve_success(self, mock_get_thread_pool_manager, mock_attachment_processor_class):
        """Test de recherche et récupération réussie."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Créer des critères de recherche
        criteria = SearchCriteria(
            spaces=["TEST"], content_types=["page"], max_results=10
        )

        # Mock des résultats de recherche
        search_results = [
            Mock(id="123", title="Page 1"),
            Mock(id="456", title="Page 2"),
        ]
        self.mock_client.search_content.return_value = search_results

        # Mock de retrieve_content - create actual content items
        content_item_1 = ContentItem(
            id="123", type="page", status="current", title="Page 1",
            space=self.space_info, version={"number": 1},
            created="2023-01-01T12:00:00", creator=self.user_info,
            last_updated="2023-01-01T12:00:00", last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123"
        )
        content_item_2 = ContentItem(
            id="456", type="page", status="current", title="Page 2",
            space=self.space_info, version={"number": 1},
            created="2023-01-01T12:00:00", creator=self.user_info,
            last_updated="2023-01-01T12:00:00", last_updater=self.user_info,
            content_url="https://example.com/content/456",
            web_ui_url="https://example.com/pages/456"
        )

        retriever.retrieve_content = MagicMock(side_effect=[content_item_1, content_item_2])

        results = retriever.search_and_retrieve(
            criteria, process_attachments=True
        )

        # Vérifications
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0].id, "123")
        self.assertEqual(results[1].id, "456")

        # Vérifier les appels
        self.mock_client.search_content.assert_called_once_with(criteria)
        self.assertEqual(retriever.retrieve_content.call_count, 2)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    def test_search_and_retrieve_empty_results(
        self, mock_get_thread_pool_manager
    ):
        """Test de recherche sans résultats."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Critères de recherche
        criteria = SearchCriteria(spaces=["EMPTY"], max_results=10)

        # Mock de recherche vide
        self.mock_client.search_content.return_value = []

        results = retriever.search_and_retrieve(criteria)

        # Vérifications
        self.assertEqual(results, [])
        self.mock_client.search_content.assert_called_once_with(criteria)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    def test_search_and_retrieve_partial_failure(
        self, mock_get_thread_pool_manager, mock_attachment_processor_class
    ):
        """Test de recherche avec échec partiel."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Critères de recherche
        criteria = SearchCriteria(spaces=["TEST"], max_results=10)

        # Mock des résultats de recherche
        search_results = [
            Mock(id="123", title="Page 1"),
            Mock(id="456", title="Page 2"),
            Mock(id="789", title="Page 3"),
        ]
        self.mock_client.search_content.return_value = search_results

        # Mock de retrieve_content avec un échec
        retriever.retrieve_content = MagicMock(
            side_effect=[
                self.content_item,  # Succès
                Exception("Retrieval failed"),  # Échec
                self.content_item,  # Succès
            ]
        )

        results = retriever.search_and_retrieve(criteria)

        # Vérifications - seulement 2 résultats sur 3
        self.assertEqual(len(results), 2)

        # Vérifier les statistiques d'erreur
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["errors"], 1)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    def test_get_retrieval_stats(self, mock_attachment_processor_class):
        """Test de récupération des statistiques."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        
        # Mock des statistiques du processeur de pièces jointes - set this BEFORE creating the retriever
        mock_attachment_processor.get_processing_stats.return_value = {
            "processed": 10,
            "failed": 2,
            "skipped": 1,
        }
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Modifier les statistiques internes
        retriever._stats["content_retrieved"] = 5
        retriever._stats["attachments_processed"] = 3
        retriever._stats["errors"] = 1

        stats = retriever.get_retrieval_stats()

        # Vérifications
        self.assertEqual(stats["content_retrieved"], 5)
        self.assertEqual(stats["attachments_processed"], 3)
        self.assertEqual(stats["errors"], 1)
        self.assertEqual(stats["attachment_processed"], 10)
        self.assertEqual(stats["attachment_failed"], 2)
        self.assertEqual(stats["attachment_skipped"], 1)

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    def test_reset_stats(self, mock_attachment_processor_class):
        """Test de remise à zéro des statistiques."""
        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.reset_stats = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Modifier les statistiques
        retriever._stats["content_retrieved"] = 5
        retriever._stats["errors"] = 2

        # Remettre à zéro
        retriever.reset_stats()

        # Vérifications
        stats = retriever.get_retrieval_stats()
        self.assertEqual(stats["content_retrieved"], 0)
        self.assertEqual(stats["attachments_processed"], 0)
        self.assertEqual(stats["errors"], 0)

        # Vérifier que le reset du processeur a été appelé
        mock_attachment_processor.reset_stats.assert_called_once()

    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager")
    @patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor")
    @patch(
        "kbotloadscheduler.loader.confluence.processing.sync_content_retriever.TextProcessor.html_to_plain_text"
    )
    def test_html_to_plain_text_conversion(
        self, mock_html_to_plain, mock_attachment_processor_class, mock_get_thread_pool_manager
    ):
        """Test de conversion HTML vers texte brut."""
        # Mock du thread pool manager
        mock_thread_pool_manager = Mock()
        mock_get_thread_pool_manager.return_value = mock_thread_pool_manager

        # Mock de la classe AttachmentProcessor
        mock_attachment_processor = Mock()
        mock_attachment_processor.get_processing_stats.return_value = {}
        mock_attachment_processor_class.return_value = mock_attachment_processor

        # Mock de la conversion HTML
        mock_html_to_plain.return_value = "Converted plain text"

        retriever = ContentRetriever(self.mock_client, self.processing_config)

        # Contenu avec HTML
        html_content = ContentItem(
            id="123",
            type="page",
            status="current",
            title="HTML Page",
            space=self.space_info,
            version={"number": 1},
            created="2023-01-01T12:00:00",
            creator=self.user_info,
            last_updated="2023-01-01T12:00:00",
            last_updater=self.user_info,
            content_url="https://example.com/content/123",
            web_ui_url="https://example.com/pages/123",
            body_view="<p>HTML content</p>",
            body_plain=None,
        )

        self.mock_client.get_content.return_value = html_content
        retriever.chunker.create_chunks = Mock(return_value=[])

        result = retriever.retrieve_content("123", process_attachments=False)

        # Vérifications
        mock_html_to_plain.assert_called_once_with("<p>HTML content</p>")
        self.assertEqual(result.body_plain, "Converted plain text")


if __name__ == "__main__":
    unittest.main()
