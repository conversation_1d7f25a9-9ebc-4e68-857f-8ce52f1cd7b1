#!/usr/bin/env python3

import subprocess
import sys
import os

# Change to the project directory
project_dir = "/Users/<USER>/IdeaProjects/kbot-load-scheduler"
os.chdir(project_dir)

# Try to run the tests
try:
    print("Running content retriever tests...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "src/kbotloadscheduler/loader/confluence/tests/test_content_retriever.py",
        "-v", "--tb=short"
    ], capture_output=True, text=True, timeout=60)
    
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")
    print(result.stderr)
    print(f"\nReturn code: {result.returncode}")
    
except subprocess.TimeoutExpired:
    print("Test execution timed out after 60 seconds")
except Exception as e:
    print(f"Error running tests: {e}")

# Also try to run our verification script
try:
    print("\n" + "="*50)
    print("Running verification script...")
    result = subprocess.run([sys.executable, "verify_fixes.py"], 
                          capture_output=True, text=True, timeout=30)
    
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")
    print(result.stderr)
    print(f"\nReturn code: {result.returncode}")
    
except subprocess.TimeoutExpired:
    print("Verification script timed out after 30 seconds")
except Exception as e:
    print(f"Error running verification script: {e}")
