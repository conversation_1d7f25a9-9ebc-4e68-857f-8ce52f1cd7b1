#!/usr/bin/env python3
import os
import sys
import subprocess

# Set environment variables as per Makefile
os.environ['ENV'] = 'tests'
os.environ['GCP_PROJECT_ID'] = 'ofr-ekb-knowledgebot-test'
os.environ['ROOT_TEST'] = '/Users/<USER>/IdeaProjects/kbot-load-scheduler/tests'
os.environ['PATH_TO_SECRET_CONFIG'] = '/Users/<USER>/IdeaProjects/kbot-load-scheduler/conf/etc/secrets/tests'
os.environ['KBOT_BACK_API_URL'] = 'https://kbot-back-api:8080'
os.environ['KBOT_EMBEDDING_API_URL'] = 'https://kbot-embedding-api:8081'
os.environ['URL_SERVICE_SHAREPOINT'] = 'https://orange0.sharepoint.com/'
os.environ['SHAREPOINT_IN_DOC_ID_USE_PROPERTY'] = 'UniqueId'
os.environ['KBOT_WORK_BUCKET_PREFIX'] = 'gs://mon_bucket-[perimeter_code]'
os.environ['PYTHONUNBUFFERED'] = '1'

print("Running specific pytest test...")
try:
    # Change to the project directory
    os.chdir('/Users/<USER>/IdeaProjects/kbot-load-scheduler')
    
    # Use pipenv to run the test
    result = subprocess.run([
        'pipenv', 'run', 'python', '-m', 'pytest', 
        'tests/loader/test_confluence_with_mock_confluence.py::TestConfluenceLoaderWithMockConfluence::test_get_document_with_mock_confluence_and_gcs',
        '-v', '--tb=short', '-s'
    ], capture_output=True, text=True, timeout=60)
    
    print("STDOUT:")
    print(result.stdout)
    print("\nSTDERR:")  
    print(result.stderr)
    print(f"\nReturn code: {result.returncode}")
    
except subprocess.TimeoutExpired:
    print("Test timed out after 60 seconds")
except Exception as e:
    print(f"Error running test: {e}")
    import traceback
    traceback.print_exc()
