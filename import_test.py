#!/usr/bin/env python3
"""
Simple import test to identify the hanging module
"""

import sys
import os

# Add paths
sys.path.append('src')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

def test_imports():
    try:
        print("1. Testing basic imports...")
        import logging
        import json
        from datetime import datetime, timezone
        from typing import Any, Dict, List, Optional
        print("✅ Basic imports successful")
        
        print("2. Testing pydantic imports...")
        from pydantic import BaseModel, Field, HttpUrl, SecretStr
        print("✅ Pydantic imports successful")
        
        print("3. Testing kbot bean imports...")
        from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
        print("✅ Bean imports successful")
        
        print("4. Testing confluence config imports...")
        # Try importing each part of the config module separately
        from kbotloadscheduler.loader.confluence.config import SearchCriteria
        print("✅ SearchCriteria imported")
        
        from kbotloadscheduler.loader.confluence.config import ConfluenceConfig
        print("✅ ConfluenceConfig imported")
        
        from kbotloadscheduler.loader.confluence.config import StorageConfig
        print("✅ StorageConfig imported")
        
        from kbotloadscheduler.loader.confluence.config import ProcessingConfig
        print("✅ ProcessingConfig imported")
        
        print("5. Testing confluence models imports...")
        from kbotloadscheduler.loader.confluence.models import ContentItem
        print("✅ ContentItem imported")
        
        print("6. Testing orchestrator imports...")
        from kbotloadscheduler.loader.confluence.orchestrator import SyncOrchestrator
        print("✅ SyncOrchestrator imported")
        
        print("7. Testing abstract loader imports...")
        from kbotloadscheduler.loader.abstract_loader import AbstractLoader, LoaderException
        print("✅ AbstractLoader imported")
        
        print("8. Testing confluence loader imports...")
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        print("✅ ConfluenceLoader imported")
        
        print("🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
