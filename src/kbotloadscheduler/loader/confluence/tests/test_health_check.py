#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module de health checks.
"""

import asyncio
import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from ..config import HealthCheckConfig
from ..health_check import (
    HealthStatus,
    HealthCheckResult,
    SystemHealthReport,
    HealthChecker,
)


class TestHealthStatus(unittest.TestCase):
    """Tests pour l'énumération HealthStatus."""

    def test_health_status_values(self):
        """Test des valeurs de l'énumération HealthStatus."""
        self.assertEqual(HealthStatus.HEALTHY.value, "healthy")
        self.assertEqual(HealthStatus.DEGRADED.value, "degraded")
        self.assertEqual(HealthStatus.UNHEALTHY.value, "unhealthy")
        self.assertEqual(HealthStatus.UNKNOWN.value, "unknown")

    def test_health_status_members(self):
        """Test des membres de l'énumération HealthStatus."""
        expected_members = {"HEALTHY", "DEGRADED", "UNHEALTHY", "UNKNOWN"}
        actual_members = {member.name for member in HealthStatus}
        self.assertEqual(actual_members, expected_members)


class TestHealthCheckResult(unittest.TestCase):
    """Tests pour la classe HealthCheckResult."""

    def test_health_check_result_creation(self):
        """Test de création d'un HealthCheckResult."""
        result = HealthCheckResult(
            name="test_check",
            status=HealthStatus.HEALTHY,
            message="Test successful",
            details={"key": "value"},
            duration_ms=123.45,
        )

        self.assertEqual(result.name, "test_check")
        self.assertEqual(result.status, HealthStatus.HEALTHY)
        self.assertEqual(result.message, "Test successful")
        self.assertEqual(result.details, {"key": "value"})
        self.assertEqual(result.duration_ms, 123.45)
        self.assertIsInstance(result.timestamp, datetime)

    def test_health_check_result_defaults(self):
        """Test des valeurs par défaut de HealthCheckResult."""
        result = HealthCheckResult(
            name="test_check", status=HealthStatus.HEALTHY, message="Test successful"
        )

        self.assertEqual(result.details, {})
        self.assertEqual(result.duration_ms, 0.0)
        self.assertIsInstance(result.timestamp, datetime)

    def test_health_check_result_to_dict(self):
        """Test de conversion en dictionnaire."""
        timestamp = datetime.now()
        result = HealthCheckResult(
            name="test_check",
            status=HealthStatus.DEGRADED,
            message="Test warning",
            details={"warning": "low memory"},
            timestamp=timestamp,
            duration_ms=456.78,
        )

        result_dict = result.to_dict()

        expected_dict = {
            "name": "test_check",
            "status": "degraded",
            "message": "Test warning",
            "details": {"warning": "low memory"},
            "timestamp": timestamp.isoformat(),
            "duration_ms": 456.78,
        }

        self.assertEqual(result_dict, expected_dict)


class TestSystemHealthReport(unittest.TestCase):
    """Tests pour la classe SystemHealthReport."""

    def test_system_health_report_creation(self):
        """Test de création d'un SystemHealthReport."""
        check1 = HealthCheckResult("check1", HealthStatus.HEALTHY, "OK")
        check2 = HealthCheckResult("check2", HealthStatus.DEGRADED, "Warning")

        report = SystemHealthReport(
            overall_status=HealthStatus.DEGRADED, checks=[check1, check2]
        )

        self.assertEqual(report.overall_status, HealthStatus.DEGRADED)
        self.assertEqual(len(report.checks), 2)
        self.assertEqual(report.checks[0], check1)
        self.assertEqual(report.checks[1], check2)
        self.assertIsInstance(report.timestamp, datetime)

    def test_system_health_report_to_dict(self):
        """Test de conversion en dictionnaire."""
        check = HealthCheckResult("test_check", HealthStatus.HEALTHY, "OK")
        timestamp = datetime.now()

        report = SystemHealthReport(
            overall_status=HealthStatus.HEALTHY, checks=[check], timestamp=timestamp
        )

        report_dict = report.to_dict()

        self.assertEqual(report_dict["overall_status"], "healthy")
        self.assertEqual(len(report_dict["checks"]), 1)
        self.assertEqual(report_dict["timestamp"], timestamp.isoformat())
        self.assertIn("summary", report_dict)

    def test_system_health_report_summary(self):
        """Test de génération du résumé."""
        checks = [
            HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            HealthCheckResult("check2", HealthStatus.HEALTHY, "OK"),
            HealthCheckResult("check3", HealthStatus.DEGRADED, "Warning"),
            HealthCheckResult("check4", HealthStatus.UNHEALTHY, "Error"),
        ]

        report = SystemHealthReport(overall_status=HealthStatus.DEGRADED, checks=checks)

        report_dict = report.to_dict()
        summary = report_dict["summary"]

        self.assertEqual(summary["total_checks"], 4)
        self.assertEqual(summary["healthy"], 2)
        self.assertEqual(summary["degraded"], 1)
        self.assertEqual(summary["unhealthy"], 1)
        self.assertEqual(summary["unknown"], 0)


class TestHealthChecker(unittest.TestCase):
    """Tests pour la classe HealthChecker."""

    def setUp(self):
        """Configuration des tests."""
        self.config = HealthCheckConfig(
            enabled=True,
            check_interval_seconds=30,
            timeout_seconds=5,
            memory_threshold_percent=85.0,
            disk_threshold_percent=90.0,
            check_confluence_api=True,
            check_storage=True,
            check_circuit_breakers=True,
            check_thread_pools=True,
            check_system_resources=True,
        )
        self.health_checker = HealthChecker(self.config)

    def test_health_checker_initialization(self):
        """Test d'initialisation du HealthChecker."""
        self.assertEqual(self.health_checker.config, self.config)
        self.assertIsNone(self.health_checker._last_check_time)
        self.assertIsNone(self.health_checker._cached_report)

    def test_health_checker_disabled(self):
        """Test avec health checks désactivés."""
        config = HealthCheckConfig(enabled=False)
        health_checker = HealthChecker(config)

        # Utiliser asyncio.run pour exécuter la méthode async
        report = asyncio.run(health_checker.check_system_health())

        self.assertEqual(report.overall_status, HealthStatus.UNKNOWN)
        self.assertEqual(len(report.checks), 1)
        self.assertEqual(report.checks[0].name, "health_checks")
        self.assertIn("désactivés", report.checks[0].message)

    def test_get_cached_report_no_cache(self):
        """Test de récupération du cache quand il n'y en a pas."""
        result = self.health_checker.get_cached_report()
        self.assertIsNone(result)

    def test_get_cached_report_expired(self):
        """Test de récupération du cache expiré."""
        # Simuler un cache expiré
        old_time = datetime.now() - timedelta(
            seconds=self.config.check_interval_seconds + 1
        )
        self.health_checker._last_check_time = old_time
        self.health_checker._cached_report = Mock()

        result = self.health_checker.get_cached_report()
        self.assertIsNone(result)

    def test_get_cached_report_valid(self):
        """Test de récupération du cache valide."""
        # Simuler un cache valide
        recent_time = datetime.now() - timedelta(
            seconds=self.config.check_interval_seconds - 1
        )
        mock_report = Mock()
        self.health_checker._last_check_time = recent_time
        self.health_checker._cached_report = mock_report

        result = self.health_checker.get_cached_report()
        self.assertEqual(result, mock_report)

    def test_determine_overall_status_all_healthy(self):
        """Test de détermination du statut global avec tous les checks sains."""
        checks = [
            HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            HealthCheckResult("check2", HealthStatus.HEALTHY, "OK"),
        ]

        status = self.health_checker._determine_overall_status(checks)
        self.assertEqual(status, HealthStatus.HEALTHY)

    def test_determine_overall_status_with_degraded(self):
        """Test de détermination du statut global avec des checks dégradés."""
        checks = [
            HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            HealthCheckResult("check2", HealthStatus.DEGRADED, "Warning"),
        ]

        status = self.health_checker._determine_overall_status(checks)
        self.assertEqual(status, HealthStatus.DEGRADED)

    def test_determine_overall_status_with_unhealthy(self):
        """Test de détermination du statut global avec des checks défaillants."""
        checks = [
            HealthCheckResult("check1", HealthStatus.HEALTHY, "OK"),
            HealthCheckResult("check2", HealthStatus.DEGRADED, "Warning"),
            HealthCheckResult("check3", HealthStatus.UNHEALTHY, "Error"),
        ]

        status = self.health_checker._determine_overall_status(checks)
        self.assertEqual(status, HealthStatus.UNHEALTHY)

    def test_determine_overall_status_empty_checks(self):
        """Test de détermination du statut global avec aucun check."""
        status = self.health_checker._determine_overall_status([])
        self.assertEqual(status, HealthStatus.UNKNOWN)

    @patch("kbotloadscheduler.loader.confluence.health_check.psutil.virtual_memory")
    @patch("kbotloadscheduler.loader.confluence.health_check.psutil.disk_usage")
    def test_check_system_resources_healthy(self, mock_disk_usage, mock_virtual_memory):
        """Test de vérification des ressources système saines."""
        # Simuler des ressources saines
        mock_memory = Mock()
        mock_memory.percent = 50.0  # En dessous du seuil de 85%
        mock_memory.available = 8 * 1024**3  # 8 GB
        mock_memory.total = 16 * 1024**3  # 16 GB
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.used = 100 * 1024**3  # 100 GB
        mock_disk.total = 500 * 1024**3  # 500 GB (20% utilisé)
        mock_disk.free = 400 * 1024**3  # 400 GB
        mock_disk_usage.return_value = mock_disk

        result = asyncio.run(self.health_checker._check_system_resources())

        self.assertEqual(result.name, "system_resources")
        self.assertEqual(result.status, HealthStatus.HEALTHY)
        self.assertIn("memory", result.details)
        self.assertIn("disk", result.details)

    @patch("kbotloadscheduler.loader.confluence.health_check.psutil.virtual_memory")
    @patch("kbotloadscheduler.loader.confluence.health_check.psutil.disk_usage")
    def test_check_system_resources_degraded(
        self, mock_disk_usage, mock_virtual_memory
    ):
        """Test de vérification des ressources système dégradées."""
        # Simuler des ressources dégradées
        mock_memory = Mock()
        mock_memory.percent = 90.0  # Au-dessus du seuil de 85% -> UNHEALTHY
        mock_memory.available = 2 * 1024**3  # 2 GB
        mock_memory.total = 16 * 1024**3  # 16 GB
        mock_virtual_memory.return_value = mock_memory

        mock_disk = Mock()
        mock_disk.used = 450 * 1024**3  # 450 GB
        mock_disk.total = 500 * 1024**3  # 500 GB (90% utilisé) -> UNHEALTHY
        mock_disk.free = 50 * 1024**3  # 50 GB
        mock_disk_usage.return_value = mock_disk

        result = asyncio.run(self.health_checker._check_system_resources())

        self.assertEqual(result.name, "system_resources")
        # 90% mémoire et 90% disque sont au-dessus des seuils (85% et 90%) -> UNHEALTHY
        self.assertEqual(result.status, HealthStatus.UNHEALTHY)

    @patch("kbotloadscheduler.loader.confluence.health_check.get_thread_pool_manager")
    def test_check_thread_pools_healthy(self, mock_get_manager):
        """Test de vérification des thread pools sains."""
        # Simuler un gestionnaire de thread pools sain
        mock_manager = Mock()
        mock_manager.get_pool_stats.return_value = {
            "pool1": {
                "max_workers": 4,
                "active_threads": 1,
                "completed_tasks": 100,
                "pending_tasks": 0,
            },
            "pool2": {
                "max_workers": 4,
                "active_threads": 1,
                "completed_tasks": 50,
                "pending_tasks": 0,
            },
        }
        mock_get_manager.return_value = mock_manager

        result = asyncio.run(self.health_checker._check_thread_pools())

        self.assertEqual(result.name, "thread_pools")
        self.assertEqual(result.status, HealthStatus.HEALTHY)
        self.assertIn("pools", result.details)
        self.assertIn("active_threads", result.details)
        self.assertIn("total_workers", result.details)

    @patch("kbotloadscheduler.loader.confluence.health_check.get_thread_pool_manager")
    def test_check_thread_pools_error(self, mock_get_manager):
        """Test de vérification des thread pools avec erreur."""
        # Simuler une erreur
        mock_get_manager.side_effect = RuntimeError("Thread pool error")

        result = asyncio.run(self.health_checker._check_thread_pools())

        self.assertEqual(result.name, "thread_pools")
        # En cas d'exception, le statut est UNKNOWN selon l'implémentation
        self.assertEqual(result.status, HealthStatus.UNKNOWN)
        self.assertIn("Thread pool error", result.message)


if __name__ == "__main__":
    unittest.main()
