#!/usr/bin/env python3
"""
Direct test runner for the confluence loader test
"""

import sys
import os
import json
from datetime import datetime, timezone
from unittest.mock import MagicMock, patch

# Add paths
sys.path.append('src')
sys.path.append('tests')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://mock-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'DOCS'

def run_test():
    try:
        print("🚀 Starting direct test of confluence loader...")
        
        # Import components
        from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        
        print("✅ Basic imports successful")
        
        # Create mock config
        mock_config_with_secret = MagicMock()
        mock_config_with_secret.get_confluence_credentials.return_value = {
            "pat_token": "mock_confluence_pat_token"
        }
        
        # Create confluence source
        config = {
            "spaces": ["DOCS", "TECH"],
            "max_results": 100,
            "include_attachments": True,
            "content_types": ["page", "blogpost"],
            "labels": ["public"]
        }

        confluence_source = SourceBean(
            id=1,
            code="mock_confluence_test",
            label="Mock Confluence Test",
            src_type="confluence",
            configuration=json.dumps(config),
            last_load_time=int(datetime.now(timezone.utc).timestamp()),
            load_interval=24,
            domain_code="mock_domain",
            perimeter_code="mock_test",
            force_embedding=False
        )
        
        print("✅ Test data setup successful")
        
        # Create test document
        test_document = DocumentBean(
            id="mock_domain/mock_confluence_test/page_123456",
            name="API Guide",
            path="https://mock-confluence.example.com/display/DOCS/API+Guide",
            modification_time=datetime.now(timezone.utc)
        )
        
        # Mock sync result
        mock_sync_result = {
            "sync_id": "mock-sync-789",
            "total_content": 4,
            "processed_content": 4,
            "skipped_content": 0,
            "failed_content": 0,
        }
        
        # Test the loader creation and get_document method with proper mocking
        with patch("kbotloadscheduler.gcs.gcs_utils.storage"), \
             patch('kbotloadscheduler.loader.confluence.loader.SyncOrchestrator') as mock_orchestrator_class, \
             patch('kbotloadscheduler.loader.confluence.loader.ConfluenceConfig') as mock_confluence_config_class, \
             patch('kbotloadscheduler.loader.confluence.loader.StorageConfig') as mock_storage_config_class, \
             patch('kbotloadscheduler.loader.confluence.loader.ProcessingConfig') as mock_processing_config_class:
            
            # Setup mocks
            mock_orchestrator = MagicMock()
            mock_orchestrator.run.return_value = mock_sync_result
            mock_orchestrator_class.return_value = mock_orchestrator
            
            mock_confluence_config = MagicMock()
            mock_storage_config = MagicMock()
            mock_processing_config = MagicMock()
            mock_processing_config.enable_change_tracking = False
            
            mock_confluence_config_class.return_value = mock_confluence_config
            mock_storage_config_class.return_value = mock_storage_config
            mock_processing_config_class.from_env.return_value = mock_processing_config
            
            print("✅ Mocks setup successful")
            
            # Create loader
            loader = ConfluenceLoader(mock_config_with_secret)
            print("✅ ConfluenceLoader created successfully")
            
            # Mock the _create_confluence_config method to avoid configuration creation errors
            with patch.object(loader, '_create_confluence_config', return_value=mock_confluence_config):
                print("✅ Configuration method mocked")
                
                # Test get_document
                output_path = "gs://mock-confluence-bucket/confluence-data"
                metadata = loader.get_document(confluence_source, test_document, output_path)
                
                print("✅ get_document called successfully")
                
                # Verify results
                assert metadata is not None
                assert metadata["document_id"] == test_document.id
                assert metadata["document_name"] == test_document.name
                assert metadata["location"] == output_path
                
                print("✅ All assertions passed!")
                print(f"📊 Metadata: {metadata}")
                
        print("🎉 Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_test()
    sys.exit(0 if success else 1)
