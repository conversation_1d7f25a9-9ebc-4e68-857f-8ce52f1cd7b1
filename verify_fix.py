#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, 'src')

import pytest
from unittest.mock import MagicMock, patch

def test_confluence_loader_import_fix():
    """Test that ConfluenceLoader can be imported with proper mocking"""
    
    # Mock Google Cloud dependencies before importing
    with patch('google.cloud.secretmanager.SecretManagerServiceClient'):
        # This should now work without the AttributeError
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        
        # Test that we can patch the corrected path
        with patch('kbotloadscheduler.loader.confluence.loader.SyncOrchestrator') as mock_orch:
            # Create a mock config
            mock_config = MagicMock()
            mock_config.get_confluence_credentials.return_value = {"pat_token": "test"}
            
            # This should work without errors
            loader = ConfluenceLoader(mock_config)
            print("✅ ConfluenceLoader created successfully")
            print("✅ SyncOrchestrator patch path works")
            return True
    
if __name__ == "__main__":
    try:
        result = test_confluence_loader_import_fix()
        if result:
            print("🎉 Fix verification successful!")
        else:
            print("❌ Fix verification failed")
    except Exception as e:
        print(f"❌ Error during fix verification: {e}")
        import traceback
        traceback.print_exc()
