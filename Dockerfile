# Use an official lightweight Python image.
FROM python:3.12-slim

# Update system packages to fix security vulnerabilities
RUN apt-get update && apt-get upgrade -y && rm -rf /var/lib/apt/lists/*

# Set the working directory to /app
WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install build dependencies, Python packages, and clean up in one layer to minimize image size
# Hadolint ignore rules for the following line
# hadolint ignore=DL3008,DL3013
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    python3-dev \
    && python -m pip install --no-cache-dir --upgrade pip \
    && python -m pip install --no-cache-dir --requirement requirements.txt \
    && apt-get remove -y build-essential gcc python3-dev \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/*

# Copy the application source code
COPY src/ ./

# Make port 8080 available to the world outside this container
EXPOSE 8080

# Command to run the application using Uvicorn
CMD ["uvicorn", "kbotloadscheduler.main:app", "--host", "0.0.0.0", "--port", "8080"]

# To build image locally:
# docker build . -t kbot-loadscheduler
# To run image locally:
# bash runDockerLocal
# And go to http://localhost:8080/docs
