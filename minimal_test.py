#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    try:
        print("Testing basic imports...")
        
        # Test kbotloadscheduler imports
        from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
        print("✅ ConfluenceLoader imported")
        
        from kbotloadscheduler.loader.confluence.orchestrator import SyncOrchestrator
        print("✅ SyncOrchestrator imported")
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_patch_path():
    try:
        print("\nTesting patch path...")
        from unittest.mock import patch
        
        # Test that the corrected patch path works
        with patch('kbotloadscheduler.loader.confluence.loader.SyncOrchestrator') as mock_orch:
            print("✅ Patch path works!")
            return True
            
    except Exception as e:
        print(f"❌ Patch test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success1 = test_imports()
    success2 = test_patch_path()
    
    if success1 and success2:
        print("\n🎉 All tests passed! The fix should work.")
    else:
        print("\n💥 Some tests failed.")
