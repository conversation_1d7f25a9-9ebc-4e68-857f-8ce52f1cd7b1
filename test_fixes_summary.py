#!/usr/bin/env python3
"""
Final validation script that attempts to run actual unit tests and our validation
"""

import subprocess
import sys
import os
import time

def run_pytest():
    """Try to run the actual pytest tests."""
    
    print("Attempting to run actual pytest tests...")
    print("=" * 60)
    
    # Change to the project directory
    project_dir = "/Users/<USER>/IdeaProjects/kbot-load-scheduler"
    os.chdir(project_dir)
    
    # List of test commands to try
    test_commands = [
        [sys.executable, "-m", "pytest", 
         "src/kbotloadscheduler/loader/confluence/tests/test_content_retriever.py", 
         "-v", "--tb=short", "--no-header"],
        [sys.executable, "-c", "import unittest; unittest.main(module='src.kbotloadscheduler.loader.confluence.tests.test_content_retriever', exit=False)"],
        ["python3", "-m", "pytest", 
         "src/kbotloadscheduler/loader/confluence/tests/test_content_retriever.py", 
         "-v", "--tb=line"]
    ]
    
    for i, cmd in enumerate(test_commands):
        print(f"\nTrying command {i+1}: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30,
                cwd=project_dir
            )
            
            if result.returncode == 0:
                print("✓ Tests passed!")
                print("STDOUT:")
                print(result.stdout)
                return True
            else:
                print(f"✗ Tests failed (return code: {result.returncode})")
                print("STDOUT:")
                print(result.stdout)
                print("STDERR:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("✗ Command timed out after 30 seconds")
        except FileNotFoundError:
            print("✗ Command not found")
        except Exception as e:
            print(f"✗ Error running command: {e}")
    
    return False

def test_confluence_config():
    """Test des corrections de ConfluenceConfig."""
    print("=== Test ConfluenceConfig ===")
    
    from src.kbotloadscheduler.loader.confluence.config import ConfluenceConfig
    from pydantic import SecretStr, ValidationError
    
    # Test 1: Configuration avec PAT token
    try:
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            pat_token=SecretStr("test_token"),
            default_space_key="TEST"
        )
        assert config.enable_parallel_pagination is True
        assert config.max_parallel_requests == 3
        assert config.parallel_pagination_threshold == 200
        print("✅ PAT token configuration OK")
    except Exception as e:
        print(f"❌ PAT token configuration failed: {e}")
        return False
    
    # Test 2: Configuration avec username/api_token
    try:
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token=SecretStr("test_api_token"),
            default_space_key="TEST"
        )
        print("✅ Username/API token configuration OK")
    except Exception as e:
        print(f"❌ Username/API token configuration failed: {e}")
        return False
    
    # Test 3: Configuration sans authentification (doit échouer)
    try:
        config = ConfluenceConfig(
            url="https://test.atlassian.net",
            default_space_key="TEST"
        )
        print("❌ Configuration sans auth devrait échouer")
        return False
    except ValidationError:
        print("✅ Validation sans auth échoue correctement")
    
    return True

def test_search_criteria():
    """Test des corrections de SearchCriteria."""
    print("\n=== Test SearchCriteria ===")
    
    from src.kbotloadscheduler.loader.confluence.config import SearchCriteria
    from pydantic import ValidationError
    
    # Test 1: Configuration basique
    try:
        criteria = SearchCriteria(
            spaces=["TEST", "DOCS"],
            max_results=500,
            last_modified_days=30,
            include_attachments=True
        )
        assert criteria.spaces == ["TEST", "DOCS"]
        assert criteria.max_results == 500
        assert criteria.last_modified_days == 30
        assert criteria.include_attachments is True
        print("✅ Configuration basique OK")
    except Exception as e:
        print(f"❌ Configuration basique failed: {e}")
        return False
    
    # Test 2: Valeurs par défaut
    try:
        criteria = SearchCriteria()
        assert criteria.max_results == 1000
        assert criteria.include_attachments is True
        assert criteria.last_modified_days is None
        print("✅ Valeurs par défaut OK")
    except Exception as e:
        print(f"❌ Valeurs par défaut failed: {e}")
        return False
    
    # Test 3: Validation max_results (doit échouer)
    try:
        criteria = SearchCriteria(max_results=0)
        print("❌ max_results=0 devrait échouer")
        return False
    except ValidationError:
        print("✅ Validation max_results échoue correctement")
    
    # Test 4: Validation last_modified_days (doit échouer)
    try:
        criteria = SearchCriteria(last_modified_days=-5)
        print("❌ last_modified_days négatif devrait échouer")
        return False
    except ValidationError:
        print("✅ Validation last_modified_days échoue correctement")
    
    return True

def test_thread_pool_config():
    """Test des corrections de ThreadPoolConfig."""
    print("\n=== Test ThreadPoolConfig ===")
    
    from src.kbotloadscheduler.loader.confluence.config import ThreadPoolConfig, ProcessingConfig
    
    # Test 1: Configuration par défaut
    try:
        config = ThreadPoolConfig()
        assert config.io_thread_workers == 8
        assert config.document_processing_workers == 4
        assert config.api_thread_workers == 3
        assert config.thread_name_prefix == "ConfluenceRAG-Worker"
        assert config.max_queue_size == 100
        print("✅ Configuration par défaut OK")
    except Exception as e:
        print(f"❌ Configuration par défaut failed: {e}")
        return False
    
    # Test 2: Configuration personnalisée
    try:
        config = ThreadPoolConfig(
            io_thread_workers=12,
            document_processing_workers=6,
            api_thread_workers=4
        )
        assert config.io_thread_workers == 12
        assert config.document_processing_workers == 6
        assert config.api_thread_workers == 4
        print("✅ Configuration personnalisée OK")
    except Exception as e:
        print(f"❌ Configuration personnalisée failed: {e}")
        return False
    
    # Test 3: Intégration avec ProcessingConfig
    try:
        processing_config = ProcessingConfig()
        assert processing_config.thread_pool_config.io_thread_workers == 8
        print("✅ Intégration ProcessingConfig OK")
    except Exception as e:
        print(f"❌ Intégration ProcessingConfig failed: {e}")
        return False
    
    return True

def test_models():
    """Test des modèles de données."""
    print("\n=== Test Models ===")
    
    from src.kbotloadscheduler.loader.confluence.models import UserInfo, SpaceInfo, ContentItem
    from datetime import datetime, timezone
    
    # Test 1: UserInfo
    try:
        user = UserInfo(
            id="user123",
            username="testuser",
            display_name="Test User"
        )
        assert user.id == "user123"
        print("✅ UserInfo OK")
    except Exception as e:
        print(f"❌ UserInfo failed: {e}")
        return False
    
    # Test 2: SpaceInfo
    try:
        space = SpaceInfo(
            id="space123",
            key="TEST",
            name="Test Space",
            type="global"
        )
        assert space.key == "TEST"
        print("✅ SpaceInfo OK")
    except Exception as e:
        print(f"❌ SpaceInfo failed: {e}")
        return False
    
    return True

def run_all_tests():
    """Exécute tous les tests."""
    print("🚀 Démarrage des tests de vérification des correctifs\n")
    
    tests = [
        test_confluence_config,
        test_search_criteria,
        test_thread_pool_config,
        test_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"❌ Test {test.__name__} échoué")
    
    print(f"\n📊 Résultats: {passed}/{total} tests passés")
    
    if passed == total:
        print("🎉 Tous les correctifs fonctionnent correctement !")
        return True
    else:
        print("❌ Certains correctifs nécessitent encore des ajustements")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
