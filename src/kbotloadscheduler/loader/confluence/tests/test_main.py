#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour les fonctions de logging.
"""

import logging
import unittest

from ..logging_utils import setup_logging, LoggingConfig


class TestSetupLogging(unittest.TestCase):
    """Tests pour la fonction setup_logging."""

    def setUp(self):
        """Configuration des tests."""
        # Sauvegarder l'état initial du logging
        self.original_handlers = logging.getLogger().handlers[:]
        self.original_level = logging.getLogger().level

    def tearDown(self):
        """Nettoyage après les tests."""
        # Restaurer l'état initial du logging
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        for handler in self.original_handlers:
            root_logger.addHandler(handler)
        root_logger.setLevel(self.original_level)

    def test_setup_logging_default_config(self):
        """Test de configuration du logging avec config par défaut."""
        setup_logging()

        root_logger = logging.getLogger()
        self.assertGreaterEqual(len(root_logger.handlers), 1)
        self.assertEqual(root_logger.level, logging.INFO)

    def test_setup_logging_custom_config(self):
        """Test de configuration du logging avec config personnalisée."""
        config = LoggingConfig(level="DEBUG", enable_console=True, enable_file=False)

        setup_logging(config)

        root_logger = logging.getLogger()
        self.assertEqual(root_logger.level, logging.DEBUG)
        self.assertGreaterEqual(len(root_logger.handlers), 1)

    def test_setup_logging_with_file(self):
        """Test de configuration du logging avec fichier."""
        config = LoggingConfig(level="INFO", enable_console=False, enable_file=True)

        setup_logging(config)

        # Vérifier qu'au moins un handler a été configuré
        root_logger = logging.getLogger()
        self.assertGreaterEqual(len(root_logger.handlers), 1)
        self.assertEqual(root_logger.level, logging.INFO)

    def test_setup_logging_valid_levels(self):
        """Test de configuration avec différents niveaux de log valides."""
        # Tester différents niveaux valides
        test_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]

        for level_name in test_levels:
            with self.subTest(level=level_name):
                config = LoggingConfig(level=level_name)
                setup_logging(config)

                root_logger = logging.getLogger()
                expected_level = getattr(logging, level_name)
                self.assertEqual(root_logger.level, expected_level)


if __name__ == "__main__":
    unittest.main()
