#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from unittest.mock import Mock

# Test the _retrieve_children_recursively method directly
def test_mock_depth_comparison():
    """Test the specific problematic comparison."""
    
    # Simulate what happens in the code
    mock_criteria = Mock()
    
    # This is what happens in the original code
    print("Testing original problematic getattr...")
    try:
        max_children_depth = getattr(mock_criteria, "max_children_depth", 3)
        print(f"getattr result: {max_children_depth} (type: {type(max_children_depth)})")
        
        # This should fail
        current_depth = 1
        result = current_depth >= max_children_depth
        print(f"Comparison result: {result}")
    except Exception as e:
        print(f"❌ Original approach failed: {e}")
    
    # Test our fix
    print("\nTesting our fix...")
    max_children_depth = 3  # Default value
    if hasattr(mock_criteria, "max_children_depth"):
        attr_value = getattr(mock_criteria, "max_children_depth")
        print(f"attr_value: {attr_value} (type: {type(attr_value)})")
        print(f"has _mock_name: {hasattr(attr_value, '_mock_name')}")
        # Check if it's a real integer value, not a Mock
        if isinstance(attr_value, int):
            max_children_depth = attr_value
        elif hasattr(attr_value, '_mock_name'):
            # It's a Mock object, use default
            max_children_depth = 3
        else:
            # Try to convert to int, fallback to default
            try:
                max_children_depth = int(attr_value)
            except (ValueError, TypeError):
                max_children_depth = 3
    
    print(f"Final max_children_depth: {max_children_depth} (type: {type(max_children_depth)})")
    
    # Test comparison
    current_depth = 1
    result = current_depth >= max_children_depth
    print(f"✓ Fixed comparison: {current_depth} >= {max_children_depth} = {result}")

if __name__ == "__main__":
    test_mock_depth_comparison()
