class DataFactory:
    @staticmethod
    def create_page_hierarchy(depth=3, width=3):
        pages = []
        for i in range(width):
            page = {
                'id': f'page_{i}',
                'title': f'Page {i}',
                'content': f'Content {i}',
                'children': []
            }
            if depth > 1:
                page['children'] = DataFactory.create_page_hierarchy(depth-1, width)
            pages.append(page)
        return pages
        
    @staticmethod
    def create_page(**kwargs):
        return {
            'id': kwargs.get('id', '123'),
            'title': kwargs.get('title', 'Test Page'),
            'content': kwargs.get('content', 'Test Content'),
            'attachments': kwargs.get('attachments', [])
        }
