#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from unittest.mock import Mock, patch

def test_attachment_processor_mock():
    """Test if the SyncAttachmentProcessor mock works correctly."""
    
    # Try importing the required modules
    try:
        from kbotloadscheduler.loader.confluence.processing.sync_content_retriever import SyncContentRetriever
        from kbotloadscheduler.loader.confluence.config import ProcessingConfig
        print("✓ Imports successful")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return
    
    # Create mock objects
    mock_client = Mock()
    
    # Create processing config
    processing_config = Mock()
    processing_config.chunk_size = 1000
    processing_config.overlap_size = 200
    processing_config.max_parallel_downloads = 5
    
    thread_pool_config = Mock()
    thread_pool_config.io_thread_workers = 5
    thread_pool_config.document_processing_workers = 3
    thread_pool_config.api_thread_workers = 4
    processing_config.thread_pool_config = thread_pool_config
    
    # Test with mock
    with patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.SyncAttachmentProcessor") as mock_attachment_processor_class:
        with patch("kbotloadscheduler.loader.confluence.processing.sync_content_retriever.get_thread_pool_manager") as mock_thread_pool:
            mock_attachment_processor = Mock()
            mock_attachment_processor_class.return_value = mock_attachment_processor
            mock_thread_pool.return_value = Mock()
            
            # Configure mock before creating retriever
            mock_attachment_processor.get_processing_stats.return_value = {
                "processed": 10,
                "failed": 2,
                "skipped": 1,
            }
            
            retriever = SyncContentRetriever(mock_client, processing_config)
            
            print(f"✓ SyncContentRetriever created")
            print(f"  Attachment processor type: {type(retriever.attachment_processor)}")
            print(f"  Is it the mock? {retriever.attachment_processor is mock_attachment_processor}")
            
            # Modify internal stats
            retriever._stats["content_retrieved"] = 5
            retriever._stats["attachments_processed"] = 3
            retriever._stats["errors"] = 1
            
            # Get stats
            stats = retriever.get_retrieval_stats()
            print(f"✓ Stats retrieved: {stats}")
            
            # Test specific assertions
            print(f"  content_retrieved: {stats.get('content_retrieved', 'missing')} (expected: 5)")
            print(f"  attachment_processed: {stats.get('attachment_processed', 'missing')} (expected: 10)")

if __name__ == "__main__":
    test_attachment_processor_mock()
