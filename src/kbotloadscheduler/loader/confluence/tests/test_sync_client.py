#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le client Confluence synchrone.
"""

import unittest
from unittest.mock import Mock, patch

from pydantic import SecretStr

from ..config import ConfluenceConfig, SearchCriteria
from ..constants import AuthType
from ..exceptions import AuthenticationError, ContentNotFoundError
from ..models import ContentItem
from ..sync_client import SyncConfluenceClient


class TestSyncConfluenceClient(unittest.TestCase):
    """Tests pour SyncConfluenceClient."""

    def setUp(self):
        """Configuration des tests."""
        self.base_url = "https://example.atlassian.net"
        self.config = ConfluenceConfig(
            url=self.base_url,
            pat_token=SecretStr("test_pat_token_123456789"),
            default_space_key="TEST",
        )

    def test_client_initialization(self):
        """Test de l'initialisation du client."""
        client = SyncConfluenceClient(self.config)

        self.assertEqual(client.config, self.config)
        self.assertEqual(client.base_url, self.base_url)
        self.assertIsNotNone(client.auth_manager)
        self.assertIsNotNone(client.response_processor)
        self.assertIsNotNone(client.circuit_breaker)
        self.assertIsNotNone(client.http_client)

    def test_client_context_manager(self):
        """Test du context manager."""
        with SyncConfluenceClient(self.config) as client:
            self.assertIsNotNone(client)
        # Le client devrait être fermé automatiquement

    @patch("src.kbotloadscheduler.loader.confluence.sync_client.SyncHTTPClient")
    def test_make_api_request_success(self, mock_http_client_class):
        """Test d'un appel API réussi."""
        # Setup mock
        mock_http_client = Mock()
        mock_http_client_class.return_value = mock_http_client

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"test": "data"}
        mock_response.headers = {"Content-Type": "application/json"}
        mock_http_client.request.return_value = mock_response

        client = SyncConfluenceClient(self.config)

        result = client._make_api_request(
            method="GET", endpoint="/test", service_name="test_service"
        )

        self.assertEqual(result, {"test": "data"})

    @patch("src.kbotloadscheduler.loader.confluence.sync_client.SyncHTTPClient")
    def test_search_content_basic(self, mock_http_client_class):
        """Test de recherche de contenu basique."""
        # Setup mock
        mock_http_client = Mock()
        mock_http_client_class.return_value = mock_http_client

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "results": [
                {
                    "id": "123",
                    "title": "Test Page",
                    "type": "page",
                    "space": {"key": "TEST"},
                    "body": {"storage": {"value": "Test content"}},
                    "version": {"number": 1},
                    "_links": {"webui": "/pages/123"},
                }
            ],
            "size": 1,
            "_links": {},
        }
        mock_response.headers = {"Content-Type": "application/json"}
        mock_http_client.request.return_value = mock_response

        client = SyncConfluenceClient(self.config)
        criteria = SearchCriteria(space_key="TEST", limit=10)

        results = client.search_content(criteria)

        self.assertEqual(len(results), 1)
        self.assertIsInstance(results[0], ContentItem)
        self.assertEqual(results[0].id, "123")
        self.assertEqual(results[0].title, "Test Page")

    @patch("src.kbotloadscheduler.loader.confluence.sync_client.SyncHTTPClient")
    def test_get_content_success(self, mock_http_client_class):
        """Test de récupération d'un contenu par ID."""
        # Setup mock
        mock_http_client = Mock()
        mock_http_client_class.return_value = mock_http_client

        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "123",
            "title": "Test Page",
            "type": "page",
            "space": {"key": "TEST"},
            "body": {"storage": {"value": "Test content"}},
            "version": {"number": 1},
            "_links": {"webui": "/pages/123"},
        }
        mock_response.headers = {"Content-Type": "application/json"}
        mock_http_client.request.return_value = mock_response

        client = SyncConfluenceClient(self.config)

        result = client.get_content("123")

        self.assertIsInstance(result, ContentItem)
        self.assertEqual(result.id, "123")
        self.assertEqual(result.title, "Test Page")

    @patch("src.kbotloadscheduler.loader.confluence.sync_client.SyncHTTPClient")
    def test_get_content_not_found(self, mock_http_client_class):
        """Test de récupération d'un contenu inexistant."""
        # Setup mock
        mock_http_client = Mock()
        mock_http_client_class.return_value = mock_http_client

        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Content not found"
        mock_response.headers = {}
        mock_http_client.request.return_value = mock_response

        client = SyncConfluenceClient(self.config)

        with self.assertRaises(ContentNotFoundError):
            client.get_content("nonexistent")

    def test_authentication_pat_token(self):
        """Test de l'authentification avec PAT token."""
        config = ConfluenceConfig(
            url=self.base_url, pat_token=SecretStr("test_pat_token")
        )

        client = SyncConfluenceClient(config)
        self.assertEqual(client.auth_manager.auth_type, AuthType.PAT_TOKEN)

    def test_authentication_api_token(self):
        """Test de l'authentification avec API token."""
        config = ConfluenceConfig(
            url=self.base_url,
            username="<EMAIL>",
            api_token=SecretStr("test_api_token"),
        )

        client = SyncConfluenceClient(config)
        self.assertEqual(client.auth_manager.auth_type, AuthType.API_TOKEN)

    def test_authentication_invalid(self):
        """Test de configuration d'authentification invalide."""
        config = ConfluenceConfig(url=self.base_url, default_space_key="TEST")

        with self.assertRaises(AuthenticationError):
            SyncConfluenceClient(config)


if __name__ == "__main__":
    unittest.main()
