#!/usr/bin/env python3
import os
import sys
import subprocess

# Set up environment  
os.environ['PYTHONPATH'] = 'src:tests'
os.environ['CONFLUENCE_URL'] = 'https://test-confluence.example.com'
os.environ['DEFAULT_SPACE_KEY'] = 'TEST'

# Run a single test with proper timeout
cmd = [
    sys.executable, '-m', 'pytest', 
    'tests/routes/test_loader_routes.py::TestLoaderRoutes::test_get_document_list',
    '-v', '-s', '--tb=short', '--durations=10'
]

print(f"Running: {' '.join(cmd)}")
print("Environment variables set:")
print(f"  PYTHONPATH: {os.environ.get('PYTHONPATH')}")
print(f"  CONFLUENCE_URL: {os.environ.get('CONFLUENCE_URL')}")

try:
    result = subprocess.run(cmd, timeout=60, cwd=os.getcwd())
    print(f"Test completed with return code: {result.returncode}")
except subprocess.TimeoutExpired:
    print("Test timed out after 60 seconds")
except Exception as e:
    print(f"Error: {e}")
