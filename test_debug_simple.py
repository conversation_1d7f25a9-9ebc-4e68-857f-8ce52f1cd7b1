#!/usr/bin/env python3
import sys
import os
sys.path.append('src')

# Set environment variables
os.environ['CONFLUENCE_URL'] = 'https://test.com'
os.environ['DEFAULT_SPACE_KEY'] = 'TEST'

try:
    from kbotloadscheduler.loader.confluence.loader import ConfluenceLoader
    print("✓ ConfluenceLoader imported successfully")
    
    # Create a basic config
    class Config:
        pass
    
    config = Config()
    loader = ConfluenceLoader(config)
    print("✓ ConfluenceLoader created successfully")
    print(f"Constructor signature: {ConfluenceLoader.__init__.__code__.co_varnames}")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
