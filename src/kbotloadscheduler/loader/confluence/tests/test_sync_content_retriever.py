import unittest
from unittest.mock import Mock, patch
from .data_factory import DataFactory

class TestSyncContentRetriever(unittest.TestCase):
    def setUp(self):
        self.mock_client = Mock()
        self.data_factory = DataFactory()

    def test_retrieve_content_success(self):
        test_pages = self.data_factory.create_page_hierarchy()
        self.mock_client.get_pages.return_value = test_pages
        # ...existing test code...

    def test_retrieve_content_error(self):
        self.mock_client.get_pages.return_value = []
        # ...existing test code...
