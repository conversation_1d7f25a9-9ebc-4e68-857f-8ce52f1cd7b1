#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour l'orchestrateur de synchronisation.
"""

import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from pydantic import SecretStr

from ..config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from ..exceptions import AuthenticationError, APIError
from ..models import ContentItem, SpaceInfo, UserInfo
from ..orchestrator import SyncOrchestrator


class TestSyncOrchestrator(unittest.TestCase):
    """Tests pour la classe SyncOrchestrator."""

    def setUp(self):
        """Configuration des tests."""
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            pat_token=SecretStr("mock_token"),
        )
        self.criteria = SearchCriteria(spaces=["TEST"], max_results=10)
        self.storage_config = StorageConfig(
            storage_type="filesystem", output_dir="/tmp/test_output"
        )
        self.processing_config = ProcessingConfig(
            max_parallel_downloads=2, max_thread_workers=2
        )

        # Objets de test
        self.test_user = UserInfo(
            id="user123", username="testuser", display_name="Test User"
        )

        self.test_space = SpaceInfo(
            id="space123", key="TEST", name="Test Space", type="global"
        )

        self.test_datetime = datetime.now()

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_orchestrator_initialization(self, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test de l'initialisation de l'orchestrateur."""
        mock_storage.return_value = Mock()

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        # Vérifier que l'orchestrateur est correctement créé
        self.assertIsInstance(orchestrator, SyncOrchestrator)
        self.assertEqual(orchestrator.config, self.config)
        self.assertEqual(orchestrator.criteria, self.criteria)
        self.assertEqual(orchestrator.storage_config, self.storage_config)
        self.assertEqual(orchestrator.processing_config, self.processing_config)

        # Vérifier que les composants sont initialisés
        mock_client.assert_called_once_with(self.config)
        mock_retriever.assert_called_once()
        mock_tracker.assert_called_once()
        mock_storage.assert_called_once_with("filesystem", base_dir="/tmp/test_output")

        # Vérifier les statistiques initiales
        self.assertIsNotNone(orchestrator.stats)
        self.assertEqual(orchestrator.stats["storage_type"], "filesystem")
        self.assertEqual(orchestrator.stats["errors"], 0)

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_orchestrator_initialization_gcs(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test de l'initialisation avec stockage GCS."""
        mock_storage.return_value = Mock()

        gcs_config = StorageConfig(
            storage_type="gcs",
            gcs_bucket_name="test-bucket",
            gcs_base_prefix="test-prefix",
        )

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, gcs_config, self.processing_config
        )

        # Ajout des assertions pour utiliser la variable orchestrator
        self.assertEqual(orchestrator.storage_config, gcs_config)
        self.assertEqual(orchestrator.stats["storage_type"], "gcs")

        mock_storage.assert_called_once_with(
            "gcs", bucket_name="test-bucket", base_prefix="test-prefix"
        )

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_orchestrator_initialization_invalid_storage(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test de l'initialisation avec type de stockage invalide."""
        invalid_config = StorageConfig(storage_type="invalid")

        with self.assertRaises(ValueError):
            SyncOrchestrator(
                self.config, self.criteria, invalid_config, self.processing_config
            )

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_retrieve_content_success(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test de récupération de contenu réussie."""
        mock_storage.return_value = Mock()
        mock_content_items = [
            ContentItem(
                id="123",
                title="Test Page",
                type="page",
                status="current",
                space=self.test_space,
                version={"number": 1},
                created=self.test_datetime,
                creator=self.test_user,
                last_updated=self.test_datetime,
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/rest/api/content/123",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/123",
            ),
            ContentItem(
                id="456",
                title="Another Page",
                type="page",
                status="current",
                space=self.test_space,
                version={"number": 1},
                created=self.test_datetime,
                creator=self.test_user,
                last_updated=self.test_datetime,
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/rest/api/content/456",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/456",
            ),
        ]

        mock_retriever_instance = Mock()
        mock_retriever_instance.search_and_retrieve.return_value = mock_content_items
        mock_retriever.return_value = mock_retriever_instance

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        result = orchestrator._retrieve_content()

        self.assertEqual(result, mock_content_items)
        mock_retriever_instance.search_and_retrieve.assert_called_once_with(
            self.criteria, process_attachments=self.criteria.include_attachments
        )

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_retrieve_content_error(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test de récupération de contenu avec erreur."""
        mock_storage.return_value = Mock()

        mock_retriever_instance = Mock()
        mock_retriever_instance.search_and_retrieve.side_effect = APIError("API Error")
        mock_retriever.return_value = mock_retriever_instance

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        with self.assertRaises(APIError):
            orchestrator._retrieve_content()

        self.assertEqual(orchestrator.stats["errors"], 1)

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_process_changed_content(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test de traitement des contenus modifiés."""
        mock_storage_instance = Mock()
        mock_storage.return_value = mock_storage_instance

        mock_tracker_instance = Mock()
        mock_tracker_instance.has_content_changed.side_effect = [
            True,
            False,
        ]  # Premier changé, deuxième non
        mock_tracker.return_value = mock_tracker_instance

        content_items = [
            ContentItem(
                id="123",
                title="Changed Page",
                type="page",
                status="current",
                space=self.test_space,
                version={"number": 1},
                created=self.test_datetime,
                creator=self.test_user,
                last_updated=self.test_datetime,
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/rest/api/content/123",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/123",
            ),
            ContentItem(
                id="456",
                title="Unchanged Page",
                type="page",
                status="current",
                space=self.test_space,
                version={"number": 1},
                created=self.test_datetime,
                creator=self.test_user,
                last_updated=self.test_datetime,
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/rest/api/content/456",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/456",
            ),
        ]

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        # Mock _store_content method
        orchestrator._store_content = Mock()

        result = orchestrator._process_changed_content(content_items)

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].id, "123")
        self.assertEqual(orchestrator.stats["changed_content_items"], 1)

        # Vérifier que _store_content a été appelé pour le contenu modifié
        orchestrator._store_content.assert_called_once_with(content_items[0])

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_run_success(self, mock_storage, mock_tracker, mock_retriever, mock_client):
        """Test d'exécution complète réussie."""
        mock_storage.return_value = Mock()

        mock_content_items = [
            ContentItem(
                id="123",
                title="Test Page",
                type="page",
                status="current",
                space=self.test_space,
                version={"number": 1},
                created=self.test_datetime,
                creator=self.test_user,
                last_updated=self.test_datetime,
                last_updater=self.test_user,
                content_url="https://test.atlassian.net/rest/api/content/123",
                web_ui_url="https://test.atlassian.net/wiki/spaces/TEST/pages/123",
            )
        ]

        mock_tracker_instance = Mock()
        mock_tracker_instance.record_sync.return_value = {"sync_id": "test_sync"}
        mock_tracker.return_value = mock_tracker_instance

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        # Mock les méthodes internes
        orchestrator._retrieve_content = Mock(return_value=mock_content_items)
        orchestrator._process_changed_content = Mock(return_value=mock_content_items)

        result = orchestrator.run()

        # Vérifier les statistiques
        self.assertIsNotNone(result["start_time"])
        self.assertIsNotNone(result["end_time"])
        self.assertEqual(result["total_content_items"], 1)
        self.assertEqual(result["sync_id"], "test_sync")
        self.assertGreaterEqual(result["processing_time_seconds"], 0)

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_run_authentication_error(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test d'exécution avec erreur d'authentification."""
        mock_storage.return_value = Mock()

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        # Mock pour lever une erreur d'authentification
        orchestrator._retrieve_content = Mock(
            side_effect=AuthenticationError("Authentication failed")
        )

        with self.assertRaises(AuthenticationError):
            orchestrator.run()

        self.assertEqual(orchestrator.stats["errors"], 1)

    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncConfluenceClient")
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.SyncContentRetriever")
    @patch(
        "src.kbotloadscheduler.loader.confluence.orchestrator.ConfluenceChangeTracker"
    )
    @patch("src.kbotloadscheduler.loader.confluence.orchestrator.get_storage_provider")
    def test_get_sync_status(
        self, mock_storage, mock_tracker, mock_retriever, mock_client
    ):
        """Test de récupération du statut de synchronisation."""
        mock_storage.return_value = Mock()

        mock_tracker_instance = Mock()
        mock_tracker_instance.get_last_sync_info.return_value = {
            "last_sync_time": "2023-01-01T00:00:00Z",
            "items_processed": 10,
        }
        mock_tracker.return_value = mock_tracker_instance

        orchestrator = SyncOrchestrator(
            self.config, self.criteria, self.storage_config, self.processing_config
        )

        status = orchestrator.get_sync_status()

        self.assertIn("is_running", status)
        self.assertIn("last_sync", status)
        self.assertIn("current_stats", status)
        self.assertEqual(status["last_sync"]["items_processed"], 10)


if __name__ == "__main__":
    unittest.main()
